#!/usr/bin/env node

/**
 * Enhanced Inventory Migration Script
 * 
 * This script migrates the existing restaurant management system
 * to use the enhanced inventory system with finished goods tracking.
 * 
 * Usage:
 *   node scripts/migrate-enhanced-inventory.js [--dry-run] [--rollback]
 * 
 * Options:
 *   --dry-run   Show what would be migrated without making changes
 *   --rollback  Rollback the enhanced inventory migration
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables:')
  console.error('   NEXT_PUBLIC_SUPABASE_URL')
  console.error('   SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)

// Parse command line arguments
const args = process.argv.slice(2)
const isDryRun = args.includes('--dry-run')
const isRollback = args.includes('--rollback')

async function checkPrerequisites() {
  console.log('🔍 Checking prerequisites...')
  
  // Check if we can connect to the database
  const { data, error } = await supabase.from('branches').select('count').limit(1)
  if (error) {
    throw new Error(`Cannot connect to database: ${error.message}`)
  }
  
  // Check if enhanced tables already exist
  const { data: tables } = await supabase.rpc('get_table_names')
  const enhancedTables = [
    'finished_goods_inventory',
    'enhanced_stock_movements',
    'menu_item_availability_cache',
    'production_finished_goods_output'
  ]
  
  const existingTables = enhancedTables.filter(table => 
    tables?.some(t => t.table_name === table)
  )
  
  if (!isRollback && existingTables.length > 0) {
    console.log('⚠️  Enhanced inventory tables already exist:')
    existingTables.forEach(table => console.log(`   - ${table}`))
    console.log('   Use --rollback to remove them first, or continue to update existing data.')
  }
  
  if (isRollback && existingTables.length === 0) {
    console.log('ℹ️  No enhanced inventory tables found to rollback.')
    return false
  }
  
  return true
}

async function getPreMigrationStats() {
  console.log('📊 Gathering pre-migration statistics...')
  
  const stats = {}
  
  // Count existing production batches
  const { count: batchCount } = await supabase
    .from('production_batches')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'completed')
  
  stats.completedBatches = batchCount || 0
  
  // Count menu items
  const { count: menuCount } = await supabase
    .from('menu_items')
    .select('*', { count: 'exact', head: true })
    .eq('is_available', true)
  
  stats.activeMenuItems = menuCount || 0
  
  // Count warehouses
  const { count: warehouseCount } = await supabase
    .from('warehouses')
    .select('*', { count: 'exact', head: true })
  
  stats.warehouses = warehouseCount || 0
  
  // Count stock movements
  const { count: movementCount } = await supabase
    .from('stock_movements')
    .select('*', { count: 'exact', head: true })
  
  stats.stockMovements = movementCount || 0
  
  console.log('   📦 Completed production batches:', stats.completedBatches)
  console.log('   🍽️  Active menu items:', stats.activeMenuItems)
  console.log('   🏪 Warehouses:', stats.warehouses)
  console.log('   📋 Stock movements:', stats.stockMovements)
  
  return stats
}

async function runMigration() {
  console.log('🚀 Starting enhanced inventory migration...')
  
  const migrationFile = path.join(__dirname, '../database/migrations/001_migrate_to_enhanced_inventory.sql')
  const migrationSQL = fs.readFileSync(migrationFile, 'utf8')
  
  if (isDryRun) {
    console.log('🔍 DRY RUN - Would execute the following migration:')
    console.log('   File:', migrationFile)
    console.log('   Size:', migrationSQL.length, 'characters')
    return
  }
  
  // Execute migration in chunks to avoid timeout
  const sqlStatements = migrationSQL
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('/*'))
  
  console.log(`   Executing ${sqlStatements.length} SQL statements...`)
  
  for (let i = 0; i < sqlStatements.length; i++) {
    const statement = sqlStatements[i]
    if (statement.trim()) {
      try {
        await supabase.rpc('execute_sql', { sql_query: statement })
        process.stdout.write(`   Progress: ${i + 1}/${sqlStatements.length}\r`)
      } catch (error) {
        console.error(`\n❌ Error executing statement ${i + 1}:`)
        console.error('   Statement:', statement.substring(0, 100) + '...')
        console.error('   Error:', error.message)
        throw error
      }
    }
  }
  
  console.log('\n✅ Migration completed successfully!')
}

async function runRollback() {
  console.log('🔄 Starting enhanced inventory rollback...')
  
  const rollbackFile = path.join(__dirname, '../database/migrations/001_rollback_enhanced_inventory.sql')
  const rollbackSQL = fs.readFileSync(rollbackFile, 'utf8')
  
  if (isDryRun) {
    console.log('🔍 DRY RUN - Would execute the following rollback:')
    console.log('   File:', rollbackFile)
    console.log('   Size:', rollbackSQL.length, 'characters')
    return
  }
  
  // Execute rollback
  const { error } = await supabase.rpc('execute_sql', { sql_query: rollbackSQL })
  if (error) {
    throw error
  }
  
  console.log('✅ Rollback completed successfully!')
}

async function getPostMigrationStats() {
  console.log('📊 Gathering post-migration statistics...')
  
  const stats = {}
  
  // Count finished goods inventory
  const { count: finishedGoodsCount } = await supabase
    .from('finished_goods_inventory')
    .select('*', { count: 'exact', head: true })
  
  stats.finishedGoodsItems = finishedGoodsCount || 0
  
  // Count enhanced stock movements
  const { count: enhancedMovementCount } = await supabase
    .from('enhanced_stock_movements')
    .select('*', { count: 'exact', head: true })
  
  stats.enhancedStockMovements = enhancedMovementCount || 0
  
  // Count availability cache entries
  const { count: cacheCount } = await supabase
    .from('menu_item_availability_cache')
    .select('*', { count: 'exact', head: true })
  
  stats.availabilityCacheEntries = cacheCount || 0
  
  // Count production outputs
  const { count: outputCount } = await supabase
    .from('production_finished_goods_output')
    .select('*', { count: 'exact', head: true })
  
  stats.productionOutputs = outputCount || 0
  
  console.log('   📦 Finished goods inventory items:', stats.finishedGoodsItems)
  console.log('   🔄 Enhanced stock movements:', stats.enhancedStockMovements)
  console.log('   💾 Availability cache entries:', stats.availabilityCacheEntries)
  console.log('   🏭 Production outputs:', stats.productionOutputs)
  
  return stats
}

async function main() {
  try {
    console.log('🎯 Enhanced Inventory Migration Tool')
    console.log('=====================================')
    
    if (isDryRun) {
      console.log('🔍 Running in DRY RUN mode - no changes will be made')
    }
    
    if (isRollback) {
      console.log('🔄 Running in ROLLBACK mode')
    }
    
    console.log('')
    
    // Check prerequisites
    const canProceed = await checkPrerequisites()
    if (!canProceed) {
      console.log('✅ Nothing to do.')
      return
    }
    
    // Get pre-migration stats
    const preStats = await getPreMigrationStats()
    console.log('')
    
    // Run migration or rollback
    if (isRollback) {
      await runRollback()
    } else {
      await runMigration()
    }
    
    console.log('')
    
    // Get post-migration stats (only for actual migration)
    if (!isDryRun && !isRollback) {
      const postStats = await getPostMigrationStats()
      console.log('')
      console.log('🎉 Migration Summary:')
      console.log(`   Created ${postStats.finishedGoodsItems} finished goods inventory items`)
      console.log(`   Migrated ${postStats.enhancedStockMovements} stock movements`)
      console.log(`   Initialized ${postStats.availabilityCacheEntries} availability cache entries`)
      console.log(`   Created ${postStats.productionOutputs} production output records`)
    }
    
    console.log('')
    console.log('✅ Operation completed successfully!')
    
    if (!isDryRun) {
      console.log('')
      console.log('📝 Next steps:')
      console.log('   1. Update your application code to use the new services')
      console.log('   2. Test the enhanced inventory functionality')
      console.log('   3. Monitor the system for any issues')
      console.log('   4. Train staff on the new features')
    }
    
  } catch (error) {
    console.error('')
    console.error('❌ Migration failed:')
    console.error('   Error:', error.message)
    console.error('')
    console.error('🔧 Troubleshooting:')
    console.error('   1. Check your database connection')
    console.error('   2. Verify you have the required permissions')
    console.error('   3. Check the migration logs for details')
    console.error('   4. Consider running with --dry-run first')
    process.exit(1)
  }
}

// Run the migration
main()
