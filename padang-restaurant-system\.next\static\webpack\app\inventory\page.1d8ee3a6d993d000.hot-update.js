"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InventoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(app-pages-browser)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_inventory_AddStockModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/inventory/AddStockModal */ \"(app-pages-browser)/./src/components/inventory/AddStockModal.tsx\");\n/* harmony import */ var _components_inventory_UpdateStockModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/inventory/UpdateStockModal */ \"(app-pages-browser)/./src/components/inventory/UpdateStockModal.tsx\");\n/* harmony import */ var _components_inventory_TransferStockModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/inventory/TransferStockModal */ \"(app-pages-browser)/./src/components/inventory/TransferStockModal.tsx\");\n/* harmony import */ var _components_inventory_BulkUpdateModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/inventory/BulkUpdateModal */ \"(app-pages-browser)/./src/components/inventory/BulkUpdateModal.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction InventoryPage() {\n    var _warehouses_find;\n    _s();\n    const { profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [inventory, setInventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [finishedGoods, setFinishedGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [warehouses, setWarehouses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWarehouse, setSelectedWarehouse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [lowStockItems, setLowStockItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('raw_ingredients');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isInventoryLoading, setIsInventoryLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Modal states\n    const [isAddStockModalOpen, setIsAddStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdateStockModalOpen, setIsUpdateStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferStockModalOpen, setIsTransferStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedItem, setSelectedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWarehouses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadWarehouses]\": async ()=>{\n            try {\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_9__.warehouseService.getAllWarehouses();\n                setWarehouses(data);\n                // Auto-select first warehouse if available and no warehouse is selected\n                if (data.length > 0 && !selectedWarehouse) {\n                    setSelectedWarehouse(data[0].id);\n                }\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load warehouses';\n                setError(errorMessage);\n                console.error('Error loading warehouses:', err);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadWarehouses]\"], [\n        selectedWarehouse\n    ]);\n    const loadInventory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadInventory]\": async (warehouseId)=>{\n            if (!warehouseId) return;\n            try {\n                setIsInventoryLoading(true);\n                setError(null) // Clear previous errors\n                ;\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_9__.inventoryService.getWarehouseInventory(warehouseId);\n                setInventory(data || []) // Ensure we always have an array\n                ;\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load inventory';\n                setError(errorMessage);\n                console.error('Error loading inventory:', err);\n                setInventory([]) // Reset inventory on error\n                ;\n            } finally{\n                setIsInventoryLoading(false);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadInventory]\"], []);\n    const loadFinishedGoods = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadFinishedGoods]\": async (warehouseId)=>{\n            if (!warehouseId) return;\n            try {\n                setIsInventoryLoading(true);\n                setError(null);\n                // This would be a new service method to get finished goods inventory\n                const { data, error } = await supabase.from('finished_goods_inventory').select(\"\\n          *,\\n          menu_item:menu_items(id, name, category),\\n          warehouse:warehouses(id, name, code)\\n        \").eq('warehouse_id', warehouseId);\n                if (error) throw error;\n                setFinishedGoods(data || []);\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load finished goods';\n                setError(errorMessage);\n                console.error('Error loading finished goods:', err);\n                setFinishedGoods([]);\n            } finally{\n                setIsInventoryLoading(false);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadFinishedGoods]\"], []);\n    const loadLowStockItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadLowStockItems]\": async ()=>{\n            try {\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_9__.inventoryService.getLowStockItems();\n                setLowStockItems(data || []);\n            } catch (err) {\n                console.error('Failed to load low stock items:', err);\n            // Don't set error state for this as it's not critical\n            }\n        }\n    }[\"InventoryPage.useCallback[loadLowStockItems]\"], []);\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InventoryPage.useEffect\": ()=>{\n            const initializeData = {\n                \"InventoryPage.useEffect.initializeData\": async ()=>{\n                    setIsLoading(true);\n                    await Promise.all([\n                        loadWarehouses(),\n                        loadLowStockItems()\n                    ]);\n                    setIsLoading(false);\n                }\n            }[\"InventoryPage.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"InventoryPage.useEffect\"], [\n        loadWarehouses,\n        loadLowStockItems\n    ]);\n    // Load inventory when warehouse changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InventoryPage.useEffect\": ()=>{\n            if (selectedWarehouse) {\n                loadInventory(selectedWarehouse);\n            } else {\n                setInventory([]);\n            }\n        }\n    }[\"InventoryPage.useEffect\"], [\n        selectedWarehouse,\n        loadInventory\n    ]);\n    const getStockStatus = (item)=>{\n        if (item.current_stock <= item.minimum_stock) {\n            return {\n                status: 'critical',\n                color: 'text-red-600 bg-red-100'\n            };\n        } else if (item.current_stock <= item.reorder_point) {\n            return {\n                status: 'low',\n                color: 'text-yellow-600 bg-yellow-100'\n            };\n        } else {\n            return {\n                status: 'good',\n                color: 'text-green-600 bg-green-100'\n            };\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('id-ID', {\n            style: 'currency',\n            currency: 'IDR',\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const calculateInventoryStats = ()=>{\n        const totalItems = inventory.length;\n        const lowStockCount = inventory.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        const reorderNeededCount = inventory.filter((item)=>item.current_stock <= item.reorder_point).length;\n        const totalValue = inventory.reduce((total, item)=>{\n            const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0);\n            return total + itemValue;\n        }, 0);\n        return {\n            totalItems,\n            lowStockCount,\n            reorderNeededCount,\n            totalValue\n        };\n    };\n    const handleWarehouseChange = (event)=>{\n        setSelectedWarehouse(event.target.value);\n    };\n    const handleAddStock = ()=>{\n        setIsAddStockModalOpen(true);\n    };\n    const handleBulkUpdate = ()=>{\n        setIsBulkUpdateModalOpen(true);\n    };\n    const handleUpdateStock = (itemId)=>{\n        const item = inventory.find((i)=>i.id === itemId);\n        if (item) {\n            setSelectedItem(item);\n            setIsUpdateStockModalOpen(true);\n        }\n    };\n    const handleTransferStock = (itemId)=>{\n        const item = inventory.find((i)=>i.id === itemId);\n        if (item) {\n            setSelectedItem(item);\n            setIsTransferStockModalOpen(true);\n        }\n    };\n    const handleStockUpdated = ()=>{\n        if (selectedWarehouse) {\n            loadInventory(selectedWarehouse);\n        }\n        loadLowStockItems();\n    };\n    const stats = calculateInventoryStats();\n    // Show loading spinner during initial load\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            allowedRoles: [\n                'admin',\n                'manager',\n                'staff'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        allowedRoles: [\n            'admin',\n            'manager',\n            'staff'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Inventory Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedWarehouse,\n                                        onChange: handleWarehouseChange,\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                                        disabled: warehouses.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select Warehouse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            warehouses.map((warehouse)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: warehouse.id,\n                                                    children: [\n                                                        warehouse.name,\n                                                        \" (\",\n                                                        warehouse.code,\n                                                        \")\"\n                                                    ]\n                                                }, warehouse.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleAddStock,\n                                                disabled: !selectedWarehouse,\n                                                className: \"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                                children: \"Add Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleBulkUpdate,\n                                                disabled: !selectedWarehouse || inventory.length === 0,\n                                                className: \"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                                children: \"Bulk Update\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n                        role: \"alert\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block sm:inline\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setError(null),\n                                className: \"absolute top-0 bottom-0 right-0 px-4 py-3\",\n                                \"aria-label\": \"Close error message\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 13\n                    }, this),\n                    lowStockItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400 text-xl mr-3\",\n                                    \"aria-hidden\": \"true\",\n                                    children: \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-red-800\",\n                                            children: \"Low Stock Alert\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-700\",\n                                            children: [\n                                                lowStockItems.length,\n                                                \" item\",\n                                                lowStockItems.length !== 1 ? 's' : '',\n                                                lowStockItems.length === 1 ? ' is' : ' are',\n                                                \" running low across all warehouses\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDCE6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Items\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: stats.totalItems\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"⚠️\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Low Stock Items\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-red-600\",\n                                                            children: stats.lowStockCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDCB0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: formatCurrency(stats.totalValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Reorder Needed\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-yellow-600\",\n                                                            children: stats.reorderNeededCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow overflow-hidden sm:rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg leading-6 font-medium text-gray-900\",\n                                    children: [\n                                        \"Current Inventory\",\n                                        selectedWarehouse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"- \",\n                                                ((_warehouses_find = warehouses.find((w)=>w.id === selectedWarehouse)) === null || _warehouses_find === void 0 ? void 0 : _warehouses_find.name) || 'Unknown Warehouse'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this),\n                            isInventoryLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 15\n                            }, this) : !selectedWarehouse ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: \"Please select a warehouse to view inventory\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 15\n                            }, this) : inventory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: \"No inventory items found for this warehouse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Ingredient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Current Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Min/Max\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: inventory.map((item)=>{\n                                                const stockStatus = getStockStatus(item);\n                                                const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: item.ingredient.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 478,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: item.ingredient.code\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    item.current_stock.toLocaleString(),\n                                                                    \" \",\n                                                                    item.ingredient.unit\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(stockStatus.color),\n                                                                children: stockStatus.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: [\n                                                                item.minimum_stock.toLocaleString(),\n                                                                \" / \",\n                                                                item.maximum_stock.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: formatCurrency(itemValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleUpdateStock(item.id),\n                                                                    className: \"text-indigo-600 hover:text-indigo-900 mr-3 transition-colors\",\n                                                                    children: \"Update\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleTransferStock(item.id),\n                                                                    className: \"text-green-600 hover:text-green-900 transition-colors\",\n                                                                    children: \"Transfer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_AddStockModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isOpen: isAddStockModalOpen,\n                        onClose: ()=>setIsAddStockModalOpen(false),\n                        warehouseId: selectedWarehouse,\n                        onStockAdded: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_UpdateStockModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: isUpdateStockModalOpen,\n                        onClose: ()=>setIsUpdateStockModalOpen(false),\n                        item: selectedItem,\n                        onStockUpdated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_TransferStockModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        isOpen: isTransferStockModalOpen,\n                        onClose: ()=>setIsTransferStockModalOpen(false),\n                        item: selectedItem,\n                        onTransferCreated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_BulkUpdateModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        isOpen: isBulkUpdateModalOpen,\n                        onClose: ()=>setIsBulkUpdateModalOpen(false),\n                        items: inventory,\n                        onBulkUpdated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, this);\n}\n_s(InventoryPage, \"BwHEq5rByXHqlQ5sYvuA4SMiFJg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = InventoryPage;\nvar _c;\n$RefreshReg$(_c, \"InventoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvaW52ZW50b3J5L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXdEO0FBQ1I7QUFDYTtBQUNJO0FBQ0Q7QUFDTTtBQUNJO0FBQ047QUFDRDtBQXdEcEQsU0FBU1k7UUF1V0RDOztJQXRXckIsTUFBTSxFQUFFQyxPQUFPLEVBQUUsR0FBR1gsOERBQU9BO0lBQzNCLE1BQU0sQ0FBQ1ksV0FBV0MsYUFBYSxHQUFHaEIsK0NBQVFBLENBQWtCLEVBQUU7SUFDOUQsTUFBTSxDQUFDaUIsZUFBZUMsaUJBQWlCLEdBQUdsQiwrQ0FBUUEsQ0FBc0IsRUFBRTtJQUMxRSxNQUFNLENBQUNhLFlBQVlNLGNBQWMsR0FBR25CLCtDQUFRQSxDQUFjLEVBQUU7SUFDNUQsTUFBTSxDQUFDb0IsbUJBQW1CQyxxQkFBcUIsR0FBR3JCLCtDQUFRQSxDQUFTO0lBQ25FLE1BQU0sQ0FBQ3NCLGVBQWVDLGlCQUFpQixHQUFHdkIsK0NBQVFBLENBQWtCLEVBQUU7SUFDdEUsTUFBTSxDQUFDd0IsV0FBV0MsYUFBYSxHQUFHekIsK0NBQVFBLENBQXVDO0lBQ2pGLE1BQU0sQ0FBQzBCLFdBQVdDLGFBQWEsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzRCLG9CQUFvQkMsc0JBQXNCLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUM4QixPQUFPQyxTQUFTLEdBQUcvQiwrQ0FBUUEsQ0FBZ0I7SUFFbEQsZUFBZTtJQUNmLE1BQU0sQ0FBQ2dDLHFCQUFxQkMsdUJBQXVCLEdBQUdqQywrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNrQyx3QkFBd0JDLDBCQUEwQixHQUFHbkMsK0NBQVFBLENBQUM7SUFDckUsTUFBTSxDQUFDb0MsMEJBQTBCQyw0QkFBNEIsR0FBR3JDLCtDQUFRQSxDQUFDO0lBQ3pFLE1BQU0sQ0FBQ3NDLHVCQUF1QkMseUJBQXlCLEdBQUd2QywrQ0FBUUEsQ0FBQztJQUNuRSxNQUFNLENBQUN3QyxjQUFjQyxnQkFBZ0IsR0FBR3pDLCtDQUFRQSxDQUF1QjtJQUV2RSxNQUFNMEMsaUJBQWlCeEMsa0RBQVdBO3FEQUFDO1lBQ2pDLElBQUk7Z0JBQ0YsTUFBTXlDLE9BQU8sTUFBTWhDLDJEQUFnQkEsQ0FBQ2lDLGdCQUFnQjtnQkFDcER6QixjQUFjd0I7Z0JBQ2Qsd0VBQXdFO2dCQUN4RSxJQUFJQSxLQUFLRSxNQUFNLEdBQUcsS0FBSyxDQUFDekIsbUJBQW1CO29CQUN6Q0MscUJBQXFCc0IsSUFBSSxDQUFDLEVBQUUsQ0FBQ0csRUFBRTtnQkFDakM7WUFDRixFQUFFLE9BQU9DLEtBQUs7Z0JBQ1osTUFBTUMsZUFBZUQsZUFBZUUsUUFBUUYsSUFBSUcsT0FBTyxHQUFHO2dCQUMxRG5CLFNBQVNpQjtnQkFDVEcsUUFBUXJCLEtBQUssQ0FBQyw2QkFBNkJpQjtZQUM3QztRQUNGO29EQUFHO1FBQUMzQjtLQUFrQjtJQUV0QixNQUFNZ0MsZ0JBQWdCbEQsa0RBQVdBO29EQUFDLE9BQU9tRDtZQUN2QyxJQUFJLENBQUNBLGFBQWE7WUFFbEIsSUFBSTtnQkFDRnhCLHNCQUFzQjtnQkFDdEJFLFNBQVMsTUFBTSx3QkFBd0I7O2dCQUN2QyxNQUFNWSxPQUFPLE1BQU1qQywyREFBZ0JBLENBQUM0QyxxQkFBcUIsQ0FBQ0Q7Z0JBQzFEckMsYUFBYTJCLFFBQVEsRUFBRSxFQUFFLGlDQUFpQzs7WUFDNUQsRUFBRSxPQUFPSSxLQUFLO2dCQUNaLE1BQU1DLGVBQWVELGVBQWVFLFFBQVFGLElBQUlHLE9BQU8sR0FBRztnQkFDMURuQixTQUFTaUI7Z0JBQ1RHLFFBQVFyQixLQUFLLENBQUMsNEJBQTRCaUI7Z0JBQzFDL0IsYUFBYSxFQUFFLEVBQUUsMkJBQTJCOztZQUM5QyxTQUFVO2dCQUNSYSxzQkFBc0I7WUFDeEI7UUFDRjttREFBRyxFQUFFO0lBRUwsTUFBTTBCLG9CQUFvQnJELGtEQUFXQTt3REFBQyxPQUFPbUQ7WUFDM0MsSUFBSSxDQUFDQSxhQUFhO1lBRWxCLElBQUk7Z0JBQ0Z4QixzQkFBc0I7Z0JBQ3RCRSxTQUFTO2dCQUNULHFFQUFxRTtnQkFDckUsTUFBTSxFQUFFWSxJQUFJLEVBQUViLEtBQUssRUFBRSxHQUFHLE1BQU0wQixTQUMzQkMsSUFBSSxDQUFDLDRCQUNMQyxNQUFNLENBQUUsaUlBS1JDLEVBQUUsQ0FBQyxnQkFBZ0JOO2dCQUV0QixJQUFJdkIsT0FBTyxNQUFNQTtnQkFDakJaLGlCQUFpQnlCLFFBQVEsRUFBRTtZQUM3QixFQUFFLE9BQU9JLEtBQUs7Z0JBQ1osTUFBTUMsZUFBZUQsZUFBZUUsUUFBUUYsSUFBSUcsT0FBTyxHQUFHO2dCQUMxRG5CLFNBQVNpQjtnQkFDVEcsUUFBUXJCLEtBQUssQ0FBQyxpQ0FBaUNpQjtnQkFDL0M3QixpQkFBaUIsRUFBRTtZQUNyQixTQUFVO2dCQUNSVyxzQkFBc0I7WUFDeEI7UUFDRjt1REFBRyxFQUFFO0lBRUwsTUFBTStCLG9CQUFvQjFELGtEQUFXQTt3REFBQztZQUNwQyxJQUFJO2dCQUNGLE1BQU15QyxPQUFPLE1BQU1qQywyREFBZ0JBLENBQUNtRCxnQkFBZ0I7Z0JBQ3BEdEMsaUJBQWlCb0IsUUFBUSxFQUFFO1lBQzdCLEVBQUUsT0FBT0ksS0FBSztnQkFDWkksUUFBUXJCLEtBQUssQ0FBQyxtQ0FBbUNpQjtZQUNqRCxzREFBc0Q7WUFDeEQ7UUFDRjt1REFBRyxFQUFFO0lBRUwsZUFBZTtJQUNmOUMsZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTTZEOzBEQUFpQjtvQkFDckJuQyxhQUFhO29CQUNiLE1BQU1vQyxRQUFRQyxHQUFHLENBQUM7d0JBQ2hCdEI7d0JBQ0FrQjtxQkFDRDtvQkFDRGpDLGFBQWE7Z0JBQ2Y7O1lBRUFtQztRQUNGO2tDQUFHO1FBQUNwQjtRQUFnQmtCO0tBQWtCO0lBRXRDLHdDQUF3QztJQUN4QzNELGdEQUFTQTttQ0FBQztZQUNSLElBQUltQixtQkFBbUI7Z0JBQ3JCZ0MsY0FBY2hDO1lBQ2hCLE9BQU87Z0JBQ0xKLGFBQWEsRUFBRTtZQUNqQjtRQUNGO2tDQUFHO1FBQUNJO1FBQW1CZ0M7S0FBYztJQUVyQyxNQUFNYSxpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsS0FBS0MsYUFBYSxJQUFJRCxLQUFLRSxhQUFhLEVBQUU7WUFDNUMsT0FBTztnQkFBRUMsUUFBUTtnQkFBWUMsT0FBTztZQUEwQjtRQUNoRSxPQUFPLElBQUlKLEtBQUtDLGFBQWEsSUFBSUQsS0FBS0ssYUFBYSxFQUFFO1lBQ25ELE9BQU87Z0JBQUVGLFFBQVE7Z0JBQU9DLE9BQU87WUFBZ0M7UUFDakUsT0FBTztZQUNMLE9BQU87Z0JBQUVELFFBQVE7Z0JBQVFDLE9BQU87WUFBOEI7UUFDaEU7SUFDRjtJQUVBLE1BQU1FLGlCQUFpQixDQUFDQztRQUN0QixPQUFPLElBQUlDLEtBQUtDLFlBQVksQ0FBQyxTQUFTO1lBQ3BDQyxPQUFPO1lBQ1BDLFVBQVU7WUFDVkMsdUJBQXVCO1lBQ3ZCQyx1QkFBdUI7UUFDekIsR0FBR0MsTUFBTSxDQUFDUDtJQUNaO0lBRUEsTUFBTVEsMEJBQTBCO1FBQzlCLE1BQU1DLGFBQWFuRSxVQUFVOEIsTUFBTTtRQUNuQyxNQUFNc0MsZ0JBQWdCcEUsVUFBVXFFLE1BQU0sQ0FBQ2xCLENBQUFBLE9BQVFBLEtBQUtDLGFBQWEsSUFBSUQsS0FBS0UsYUFBYSxFQUFFdkIsTUFBTTtRQUMvRixNQUFNd0MscUJBQXFCdEUsVUFBVXFFLE1BQU0sQ0FBQ2xCLENBQUFBLE9BQVFBLEtBQUtDLGFBQWEsSUFBSUQsS0FBS0ssYUFBYSxFQUFFMUIsTUFBTTtRQUNwRyxNQUFNeUMsYUFBYXZFLFVBQVV3RSxNQUFNLENBQUMsQ0FBQ0MsT0FBT3RCO1lBQzFDLE1BQU11QixZQUFZdkIsS0FBS0MsYUFBYSxHQUFJRCxDQUFBQSxLQUFLd0IsVUFBVSxDQUFDQyxhQUFhLElBQUk7WUFDekUsT0FBT0gsUUFBUUM7UUFDakIsR0FBRztRQUVILE9BQU87WUFDTFA7WUFDQUM7WUFDQUU7WUFDQUM7UUFDRjtJQUNGO0lBRUEsTUFBTU0sd0JBQXdCLENBQUNDO1FBQzdCeEUscUJBQXFCd0UsTUFBTUMsTUFBTSxDQUFDQyxLQUFLO0lBQ3pDO0lBRUEsTUFBTUMsaUJBQWlCO1FBQ3JCL0QsdUJBQXVCO0lBQ3pCO0lBRUEsTUFBTWdFLG1CQUFtQjtRQUN2QjFELHlCQUF5QjtJQUMzQjtJQUVBLE1BQU0yRCxvQkFBb0IsQ0FBQ0M7UUFDekIsTUFBTWpDLE9BQU9uRCxVQUFVcUYsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFdkQsRUFBRSxLQUFLcUQ7UUFDMUMsSUFBSWpDLE1BQU07WUFDUnpCLGdCQUFnQnlCO1lBQ2hCL0IsMEJBQTBCO1FBQzVCO0lBQ0Y7SUFFQSxNQUFNbUUsc0JBQXNCLENBQUNIO1FBQzNCLE1BQU1qQyxPQUFPbkQsVUFBVXFGLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXZELEVBQUUsS0FBS3FEO1FBQzFDLElBQUlqQyxNQUFNO1lBQ1J6QixnQkFBZ0J5QjtZQUNoQjdCLDRCQUE0QjtRQUM5QjtJQUNGO0lBRUEsTUFBTWtFLHFCQUFxQjtRQUN6QixJQUFJbkYsbUJBQW1CO1lBQ3JCZ0MsY0FBY2hDO1FBQ2hCO1FBQ0F3QztJQUNGO0lBRUEsTUFBTTRDLFFBQVF2QjtJQUVkLDJDQUEyQztJQUMzQyxJQUFJdkQsV0FBVztRQUNiLHFCQUNFLDhEQUFDdEIsdUVBQWNBO1lBQUNxRyxjQUFjO2dCQUFDO2dCQUFTO2dCQUFXO2FBQVE7c0JBQ3pELDRFQUFDcEcsMEVBQWVBOzBCQUNkLDRFQUFDcUc7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLekI7SUFFQSxxQkFDRSw4REFBQ3ZHLHVFQUFjQTtRQUFDcUcsY0FBYztZQUFDO1lBQVM7WUFBVztTQUFRO2tCQUN6RCw0RUFBQ3BHLDBFQUFlQTtzQkFDZCw0RUFBQ3FHO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBR0QsV0FBVTswQ0FBbUM7Ozs7OzswQ0FDakQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2pEO3dDQUNDcUMsT0FBTzNFO3dDQUNQeUYsVUFBVWpCO3dDQUNWZSxXQUFVO3dDQUNWRyxVQUFVakcsV0FBV2dDLE1BQU0sS0FBSzs7MERBRWhDLDhEQUFDa0U7Z0RBQU9oQixPQUFNOzBEQUFHOzs7Ozs7NENBQ2hCbEYsV0FBV21HLEdBQUcsQ0FBQyxDQUFDQywwQkFDZiw4REFBQ0Y7b0RBQTBCaEIsT0FBT2tCLFVBQVVuRSxFQUFFOzt3REFDM0NtRSxVQUFVQyxJQUFJO3dEQUFDO3dEQUFHRCxVQUFVRSxJQUFJO3dEQUFDOzttREFEdkJGLFVBQVVuRSxFQUFFOzs7Ozs7Ozs7OztrREFLN0IsOERBQUM0RDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNTO2dEQUNDQyxTQUFTckI7Z0RBQ1RjLFVBQVUsQ0FBQzFGO2dEQUNYdUYsV0FBVTswREFDWDs7Ozs7OzBEQUdELDhEQUFDUztnREFDQ0MsU0FBU3BCO2dEQUNUYSxVQUFVLENBQUMxRixxQkFBcUJMLFVBQVU4QixNQUFNLEtBQUs7Z0RBQ3JEOEQsV0FBVTswREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVFON0UsdUJBQ0MsOERBQUM0RTt3QkFBSUMsV0FBVTt3QkFBMkVXLE1BQUs7OzBDQUM3Riw4REFBQ0M7Z0NBQUtaLFdBQVU7MENBQW1CN0U7Ozs7OzswQ0FDbkMsOERBQUNzRjtnQ0FDQ0MsU0FBUyxJQUFNdEYsU0FBUztnQ0FDeEI0RSxXQUFVO2dDQUNWYSxjQUFXOzBDQUVYLDRFQUFDRDtvQ0FBS1osV0FBVTs4Q0FBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBTXBDckYsY0FBY3VCLE1BQU0sR0FBRyxtQkFDdEIsOERBQUM2RDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTtvQ0FBNEJjLGVBQVk7OENBQU87Ozs7Ozs4Q0FDOUQsOERBQUNmOztzREFDQyw4REFBQ2dCOzRDQUFHZixXQUFVO3NEQUFtQzs7Ozs7O3NEQUNqRCw4REFBQ2dCOzRDQUFFaEIsV0FBVTs7Z0RBQ1ZyRixjQUFjdUIsTUFBTTtnREFBQztnREFBTXZCLGNBQWN1QixNQUFNLEtBQUssSUFBSSxNQUFNO2dEQUM5RHZCLGNBQWN1QixNQUFNLEtBQUssSUFBSSxRQUFRO2dEQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUXZELDhEQUFDNkQ7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNEO29EQUFJQyxXQUFVO29EQUFXYyxlQUFZOzhEQUFPOzs7Ozs7Ozs7OzswREFFL0MsOERBQUNmO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDaUI7O3NFQUNDLDhEQUFDQzs0REFBR2xCLFdBQVU7c0VBQTZDOzs7Ozs7c0VBRzNELDhEQUFDbUI7NERBQUduQixXQUFVO3NFQUNYSCxNQUFNdEIsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVE3Qiw4REFBQ3dCO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlDLFdBQVU7b0RBQVdjLGVBQVk7OERBQU87Ozs7Ozs7Ozs7OzBEQUUvQyw4REFBQ2Y7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNpQjs7c0VBQ0MsOERBQUNDOzREQUFHbEIsV0FBVTtzRUFBNkM7Ozs7OztzRUFHM0QsOERBQUNtQjs0REFBR25CLFdBQVU7c0VBQ1hILE1BQU1yQixhQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBUWhDLDhEQUFDdUI7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTtvREFBV2MsZUFBWTs4REFBTzs7Ozs7Ozs7Ozs7MERBRS9DLDhEQUFDZjtnREFBSUMsV0FBVTswREFDYiw0RUFBQ2lCOztzRUFDQyw4REFBQ0M7NERBQUdsQixXQUFVO3NFQUE2Qzs7Ozs7O3NFQUczRCw4REFBQ21COzREQUFHbkIsV0FBVTtzRUFDWG5DLGVBQWVnQyxNQUFNbEIsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVE1Qyw4REFBQ29CO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlDLFdBQVU7b0RBQVdjLGVBQVk7OERBQU87Ozs7Ozs7Ozs7OzBEQUUvQyw4REFBQ2Y7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNpQjs7c0VBQ0MsOERBQUNDOzREQUFHbEIsV0FBVTtzRUFBNkM7Ozs7OztzRUFHM0QsOERBQUNtQjs0REFBR25CLFdBQVU7c0VBQ1hILE1BQU1uQixrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FVdkMsOERBQUNxQjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDZTtvQ0FBR2YsV0FBVTs7d0NBQThDO3dDQUV6RHZGLG1DQUNDLDhEQUFDbUc7NENBQUtaLFdBQVU7O2dEQUE2QjtnREFDeEM5RixFQUFBQSxtQkFBQUEsV0FBV3VGLElBQUksQ0FBQzJCLENBQUFBLElBQUtBLEVBQUVqRixFQUFFLEtBQUsxQixnQ0FBOUJQLHVDQUFBQSxpQkFBa0RxRyxJQUFJLEtBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFNcEV0RixtQ0FDQyw4REFBQzhFO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7Ozs7Ozt1Q0FFZixDQUFDdkYsa0NBQ0gsOERBQUNzRjtnQ0FBSUMsV0FBVTswQ0FBc0M7Ozs7O3VDQUduRDVGLFVBQVU4QixNQUFNLEtBQUssa0JBQ3ZCLDhEQUFDNkQ7Z0NBQUlDLFdBQVU7MENBQXNDOzs7OztxREFJckQsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDcUI7b0NBQU1yQixXQUFVOztzREFDZiw4REFBQ3NCOzRDQUFNdEIsV0FBVTtzREFDZiw0RUFBQ3VCOztrRUFDQyw4REFBQ0M7d0RBQUd4QixXQUFVO2tFQUFpRjs7Ozs7O2tFQUcvRiw4REFBQ3dCO3dEQUFHeEIsV0FBVTtrRUFBaUY7Ozs7OztrRUFHL0YsOERBQUN3Qjt3REFBR3hCLFdBQVU7a0VBQWlGOzs7Ozs7a0VBRy9GLDhEQUFDd0I7d0RBQUd4QixXQUFVO2tFQUFpRjs7Ozs7O2tFQUcvRiw4REFBQ3dCO3dEQUFHeEIsV0FBVTtrRUFBaUY7Ozs7OztrRUFHL0YsOERBQUN3Qjt3REFBR3hCLFdBQVU7a0VBQWlGOzs7Ozs7Ozs7Ozs7Ozs7OztzREFLbkcsOERBQUN5Qjs0Q0FBTXpCLFdBQVU7c0RBQ2Q1RixVQUFVaUcsR0FBRyxDQUFDLENBQUM5QztnREFDZCxNQUFNbUUsY0FBY3BFLGVBQWVDO2dEQUNuQyxNQUFNdUIsWUFBWXZCLEtBQUtDLGFBQWEsR0FBSUQsQ0FBQUEsS0FBS3dCLFVBQVUsQ0FBQ0MsYUFBYSxJQUFJO2dEQUV6RSxxQkFDRSw4REFBQ3VDO29EQUFpQnZCLFdBQVU7O3NFQUMxQiw4REFBQzJCOzREQUFHM0IsV0FBVTtzRUFDWiw0RUFBQ0Q7O2tGQUNDLDhEQUFDQTt3RUFBSUMsV0FBVTtrRkFDWnpDLEtBQUt3QixVQUFVLENBQUN3QixJQUFJOzs7Ozs7a0ZBRXZCLDhEQUFDUjt3RUFBSUMsV0FBVTtrRkFDWnpDLEtBQUt3QixVQUFVLENBQUN5QixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OztzRUFJM0IsOERBQUNtQjs0REFBRzNCLFdBQVU7c0VBQ1osNEVBQUNEO2dFQUFJQyxXQUFVOztvRUFDWnpDLEtBQUtDLGFBQWEsQ0FBQ29FLGNBQWM7b0VBQUc7b0VBQUVyRSxLQUFLd0IsVUFBVSxDQUFDOEMsSUFBSTs7Ozs7Ozs7Ozs7O3NFQUcvRCw4REFBQ0Y7NERBQUczQixXQUFVO3NFQUNaLDRFQUFDWTtnRUFBS1osV0FBVyw0REFBOEUsT0FBbEIwQixZQUFZL0QsS0FBSzswRUFDM0YrRCxZQUFZaEUsTUFBTTs7Ozs7Ozs7Ozs7c0VBR3ZCLDhEQUFDaUU7NERBQUczQixXQUFVOztnRUFDWHpDLEtBQUtFLGFBQWEsQ0FBQ21FLGNBQWM7Z0VBQUc7Z0VBQUlyRSxLQUFLdUUsYUFBYSxDQUFDRixjQUFjOzs7Ozs7O3NFQUU1RSw4REFBQ0Q7NERBQUczQixXQUFVO3NFQUNYbkMsZUFBZWlCOzs7Ozs7c0VBRWxCLDhEQUFDNkM7NERBQUczQixXQUFVOzs4RUFDWiw4REFBQ1M7b0VBQ0NDLFNBQVMsSUFBTW5CLGtCQUFrQmhDLEtBQUtwQixFQUFFO29FQUN4QzZELFdBQVU7OEVBQ1g7Ozs7Ozs4RUFHRCw4REFBQ1M7b0VBQ0NDLFNBQVMsSUFBTWYsb0JBQW9CcEMsS0FBS3BCLEVBQUU7b0VBQzFDNkQsV0FBVTs4RUFDWDs7Ozs7Ozs7Ozs7OzttREFyQ0l6QyxLQUFLcEIsRUFBRTs7Ozs7NENBMkNwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUVYsOERBQUN4QywyRUFBYUE7d0JBQ1pvSSxRQUFRMUc7d0JBQ1IyRyxTQUFTLElBQU0xRyx1QkFBdUI7d0JBQ3RDb0IsYUFBYWpDO3dCQUNid0gsY0FBY3JDOzs7Ozs7a0NBR2hCLDhEQUFDaEcsOEVBQWdCQTt3QkFDZm1JLFFBQVF4Rzt3QkFDUnlHLFNBQVMsSUFBTXhHLDBCQUEwQjt3QkFDekMrQixNQUFNMUI7d0JBQ05xRyxnQkFBZ0J0Qzs7Ozs7O2tDQUdsQiw4REFBQy9GLGdGQUFrQkE7d0JBQ2pCa0ksUUFBUXRHO3dCQUNSdUcsU0FBUyxJQUFNdEcsNEJBQTRCO3dCQUMzQzZCLE1BQU0xQjt3QkFDTnNHLG1CQUFtQnZDOzs7Ozs7a0NBR3JCLDhEQUFDOUYsNkVBQWVBO3dCQUNkaUksUUFBUXBHO3dCQUNScUcsU0FBUyxJQUFNcEcseUJBQXlCO3dCQUN4Q3dHLE9BQU9oSTt3QkFDUGlJLGVBQWV6Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU0zQjtHQTFld0IzRjs7UUFDRlQsMERBQU9BOzs7S0FETFMiLCJzb3VyY2VzIjpbIkQ6XFxwYWRhbmdodWJfc3VwYWJhc2VcXHBhZGFuZy1yZXN0YXVyYW50LXN5c3RlbVxcc3JjXFxhcHBcXGludmVudG9yeVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcbmltcG9ydCBQcm90ZWN0ZWRSb3V0ZSBmcm9tICdAL2NvbXBvbmVudHMvYXV0aC9Qcm90ZWN0ZWRSb3V0ZSdcbmltcG9ydCBEYXNoYm9hcmRMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL2xheW91dC9EYXNoYm9hcmRMYXlvdXQnXG5pbXBvcnQgQWRkU3RvY2tNb2RhbCBmcm9tICdAL2NvbXBvbmVudHMvaW52ZW50b3J5L0FkZFN0b2NrTW9kYWwnXG5pbXBvcnQgVXBkYXRlU3RvY2tNb2RhbCBmcm9tICdAL2NvbXBvbmVudHMvaW52ZW50b3J5L1VwZGF0ZVN0b2NrTW9kYWwnXG5pbXBvcnQgVHJhbnNmZXJTdG9ja01vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9pbnZlbnRvcnkvVHJhbnNmZXJTdG9ja01vZGFsJ1xuaW1wb3J0IEJ1bGtVcGRhdGVNb2RhbCBmcm9tICdAL2NvbXBvbmVudHMvaW52ZW50b3J5L0J1bGtVcGRhdGVNb2RhbCdcbmltcG9ydCB7IGludmVudG9yeVNlcnZpY2UsIHdhcmVob3VzZVNlcnZpY2UgfSBmcm9tICdAL2xpYi9kYXRhYmFzZSdcblxuaW50ZXJmYWNlIEludmVudG9yeUl0ZW0ge1xuICBpZDogc3RyaW5nXG4gIGN1cnJlbnRfc3RvY2s6IG51bWJlclxuICBtaW5pbXVtX3N0b2NrOiBudW1iZXJcbiAgbWF4aW11bV9zdG9jazogbnVtYmVyXG4gIHJlb3JkZXJfcG9pbnQ6IG51bWJlclxuICBpbmdyZWRpZW50OiB7XG4gICAgaWQ6IHN0cmluZ1xuICAgIG5hbWU6IHN0cmluZ1xuICAgIGNvZGU6IHN0cmluZ1xuICAgIHVuaXQ6IHN0cmluZ1xuICAgIGNvc3RfcGVyX3VuaXQ6IG51bWJlclxuICB9XG4gIHdhcmVob3VzZToge1xuICAgIGlkOiBzdHJpbmdcbiAgICBuYW1lOiBzdHJpbmdcbiAgICBjb2RlOiBzdHJpbmdcbiAgfVxufVxuXG5pbnRlcmZhY2UgRmluaXNoZWRHb29kc0l0ZW0ge1xuICBpZDogc3RyaW5nXG4gIGN1cnJlbnRfc3RvY2s6IG51bWJlclxuICBtaW5pbXVtX3N0b2NrOiBudW1iZXJcbiAgbWF4aW11bV9zdG9jazogbnVtYmVyXG4gIHJlb3JkZXJfcG9pbnQ6IG51bWJlclxuICBjb3N0X3Blcl91bml0OiBudW1iZXJcbiAgbGFzdF9wcm9kdWNlZF9hdDogc3RyaW5nXG4gIG1lbnVfaXRlbToge1xuICAgIGlkOiBzdHJpbmdcbiAgICBuYW1lOiBzdHJpbmdcbiAgICBjYXRlZ29yeTogc3RyaW5nXG4gIH1cbiAgd2FyZWhvdXNlOiB7XG4gICAgaWQ6IHN0cmluZ1xuICAgIG5hbWU6IHN0cmluZ1xuICAgIGNvZGU6IHN0cmluZ1xuICB9XG59XG5cbmludGVyZmFjZSBXYXJlaG91c2Uge1xuICBpZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBjb2RlOiBzdHJpbmdcbiAgdHlwZTogc3RyaW5nXG59XG5cbnR5cGUgU3RvY2tTdGF0dXMgPSAnY3JpdGljYWwnIHwgJ2xvdycgfCAnZ29vZCdcblxuaW50ZXJmYWNlIFN0b2NrU3RhdHVzSW5mbyB7XG4gIHN0YXR1czogU3RvY2tTdGF0dXNcbiAgY29sb3I6IHN0cmluZ1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBJbnZlbnRvcnlQYWdlKCkge1xuICBjb25zdCB7IHByb2ZpbGUgfSA9IHVzZUF1dGgoKVxuICBjb25zdCBbaW52ZW50b3J5LCBzZXRJbnZlbnRvcnldID0gdXNlU3RhdGU8SW52ZW50b3J5SXRlbVtdPihbXSlcbiAgY29uc3QgW2ZpbmlzaGVkR29vZHMsIHNldEZpbmlzaGVkR29vZHNdID0gdXNlU3RhdGU8RmluaXNoZWRHb29kc0l0ZW1bXT4oW10pXG4gIGNvbnN0IFt3YXJlaG91c2VzLCBzZXRXYXJlaG91c2VzXSA9IHVzZVN0YXRlPFdhcmVob3VzZVtdPihbXSlcbiAgY29uc3QgW3NlbGVjdGVkV2FyZWhvdXNlLCBzZXRTZWxlY3RlZFdhcmVob3VzZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKVxuICBjb25zdCBbbG93U3RvY2tJdGVtcywgc2V0TG93U3RvY2tJdGVtc10gPSB1c2VTdGF0ZTxJbnZlbnRvcnlJdGVtW10+KFtdKVxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGU8J3Jhd19pbmdyZWRpZW50cycgfCAnZmluaXNoZWRfZ29vZHMnPigncmF3X2luZ3JlZGllbnRzJylcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtpc0ludmVudG9yeUxvYWRpbmcsIHNldElzSW52ZW50b3J5TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuXG4gIC8vIE1vZGFsIHN0YXRlc1xuICBjb25zdCBbaXNBZGRTdG9ja01vZGFsT3Blbiwgc2V0SXNBZGRTdG9ja01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2lzVXBkYXRlU3RvY2tNb2RhbE9wZW4sIHNldElzVXBkYXRlU3RvY2tNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpc1RyYW5zZmVyU3RvY2tNb2RhbE9wZW4sIHNldElzVHJhbnNmZXJTdG9ja01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2lzQnVsa1VwZGF0ZU1vZGFsT3Blbiwgc2V0SXNCdWxrVXBkYXRlTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2VsZWN0ZWRJdGVtLCBzZXRTZWxlY3RlZEl0ZW1dID0gdXNlU3RhdGU8SW52ZW50b3J5SXRlbSB8IG51bGw+KG51bGwpXG5cbiAgY29uc3QgbG9hZFdhcmVob3VzZXMgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB3YXJlaG91c2VTZXJ2aWNlLmdldEFsbFdhcmVob3VzZXMoKVxuICAgICAgc2V0V2FyZWhvdXNlcyhkYXRhKVxuICAgICAgLy8gQXV0by1zZWxlY3QgZmlyc3Qgd2FyZWhvdXNlIGlmIGF2YWlsYWJsZSBhbmQgbm8gd2FyZWhvdXNlIGlzIHNlbGVjdGVkXG4gICAgICBpZiAoZGF0YS5sZW5ndGggPiAwICYmICFzZWxlY3RlZFdhcmVob3VzZSkge1xuICAgICAgICBzZXRTZWxlY3RlZFdhcmVob3VzZShkYXRhWzBdLmlkKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gbG9hZCB3YXJlaG91c2VzJ1xuICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKVxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB3YXJlaG91c2VzOicsIGVycilcbiAgICB9XG4gIH0sIFtzZWxlY3RlZFdhcmVob3VzZV0pXG5cbiAgY29uc3QgbG9hZEludmVudG9yeSA9IHVzZUNhbGxiYWNrKGFzeW5jICh3YXJlaG91c2VJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCF3YXJlaG91c2VJZCkgcmV0dXJuXG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIHNldElzSW52ZW50b3J5TG9hZGluZyh0cnVlKVxuICAgICAgc2V0RXJyb3IobnVsbCkgLy8gQ2xlYXIgcHJldmlvdXMgZXJyb3JzXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgaW52ZW50b3J5U2VydmljZS5nZXRXYXJlaG91c2VJbnZlbnRvcnkod2FyZWhvdXNlSWQpXG4gICAgICBzZXRJbnZlbnRvcnkoZGF0YSB8fCBbXSkgLy8gRW5zdXJlIHdlIGFsd2F5cyBoYXZlIGFuIGFycmF5XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBsb2FkIGludmVudG9yeSdcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSlcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgaW52ZW50b3J5OicsIGVycilcbiAgICAgIHNldEludmVudG9yeShbXSkgLy8gUmVzZXQgaW52ZW50b3J5IG9uIGVycm9yXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzSW52ZW50b3J5TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH0sIFtdKVxuXG4gIGNvbnN0IGxvYWRGaW5pc2hlZEdvb2RzID0gdXNlQ2FsbGJhY2soYXN5bmMgKHdhcmVob3VzZUlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXdhcmVob3VzZUlkKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBzZXRJc0ludmVudG9yeUxvYWRpbmcodHJ1ZSlcbiAgICAgIHNldEVycm9yKG51bGwpXG4gICAgICAvLyBUaGlzIHdvdWxkIGJlIGEgbmV3IHNlcnZpY2UgbWV0aG9kIHRvIGdldCBmaW5pc2hlZCBnb29kcyBpbnZlbnRvcnlcbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCdmaW5pc2hlZF9nb29kc19pbnZlbnRvcnknKVxuICAgICAgICAuc2VsZWN0KGBcbiAgICAgICAgICAqLFxuICAgICAgICAgIG1lbnVfaXRlbTptZW51X2l0ZW1zKGlkLCBuYW1lLCBjYXRlZ29yeSksXG4gICAgICAgICAgd2FyZWhvdXNlOndhcmVob3VzZXMoaWQsIG5hbWUsIGNvZGUpXG4gICAgICAgIGApXG4gICAgICAgIC5lcSgnd2FyZWhvdXNlX2lkJywgd2FyZWhvdXNlSWQpXG5cbiAgICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgICAgIHNldEZpbmlzaGVkR29vZHMoZGF0YSB8fCBbXSlcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGxvYWQgZmluaXNoZWQgZ29vZHMnXG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGZpbmlzaGVkIGdvb2RzOicsIGVycilcbiAgICAgIHNldEZpbmlzaGVkR29vZHMoW10pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzSW52ZW50b3J5TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH0sIFtdKVxuXG4gIGNvbnN0IGxvYWRMb3dTdG9ja0l0ZW1zID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgaW52ZW50b3J5U2VydmljZS5nZXRMb3dTdG9ja0l0ZW1zKClcbiAgICAgIHNldExvd1N0b2NrSXRlbXMoZGF0YSB8fCBbXSlcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGxvdyBzdG9jayBpdGVtczonLCBlcnIpXG4gICAgICAvLyBEb24ndCBzZXQgZXJyb3Igc3RhdGUgZm9yIHRoaXMgYXMgaXQncyBub3QgY3JpdGljYWxcbiAgICB9XG4gIH0sIFtdKVxuXG4gIC8vIEluaXRpYWwgbG9hZFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRpYWxpemVEYXRhID0gYXN5bmMgKCkgPT4ge1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpXG4gICAgICBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgIGxvYWRXYXJlaG91c2VzKCksXG4gICAgICAgIGxvYWRMb3dTdG9ja0l0ZW1zKClcbiAgICAgIF0pXG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICAgIFxuICAgIGluaXRpYWxpemVEYXRhKClcbiAgfSwgW2xvYWRXYXJlaG91c2VzLCBsb2FkTG93U3RvY2tJdGVtc10pXG5cbiAgLy8gTG9hZCBpbnZlbnRvcnkgd2hlbiB3YXJlaG91c2UgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZFdhcmVob3VzZSkge1xuICAgICAgbG9hZEludmVudG9yeShzZWxlY3RlZFdhcmVob3VzZSlcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0SW52ZW50b3J5KFtdKVxuICAgIH1cbiAgfSwgW3NlbGVjdGVkV2FyZWhvdXNlLCBsb2FkSW52ZW50b3J5XSlcblxuICBjb25zdCBnZXRTdG9ja1N0YXR1cyA9IChpdGVtOiBJbnZlbnRvcnlJdGVtKTogU3RvY2tTdGF0dXNJbmZvID0+IHtcbiAgICBpZiAoaXRlbS5jdXJyZW50X3N0b2NrIDw9IGl0ZW0ubWluaW11bV9zdG9jaykge1xuICAgICAgcmV0dXJuIHsgc3RhdHVzOiAnY3JpdGljYWwnLCBjb2xvcjogJ3RleHQtcmVkLTYwMCBiZy1yZWQtMTAwJyB9XG4gICAgfSBlbHNlIGlmIChpdGVtLmN1cnJlbnRfc3RvY2sgPD0gaXRlbS5yZW9yZGVyX3BvaW50KSB7XG4gICAgICByZXR1cm4geyBzdGF0dXM6ICdsb3cnLCBjb2xvcjogJ3RleHQteWVsbG93LTYwMCBiZy15ZWxsb3ctMTAwJyB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiB7IHN0YXR1czogJ2dvb2QnLCBjb2xvcjogJ3RleHQtZ3JlZW4tNjAwIGJnLWdyZWVuLTEwMCcgfVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGZvcm1hdEN1cnJlbmN5ID0gKGFtb3VudDogbnVtYmVyKTogc3RyaW5nID0+IHtcbiAgICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KCdpZC1JRCcsIHtcbiAgICAgIHN0eWxlOiAnY3VycmVuY3knLFxuICAgICAgY3VycmVuY3k6ICdJRFInLFxuICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAwLFxuICAgICAgbWF4aW11bUZyYWN0aW9uRGlnaXRzOiAwXG4gICAgfSkuZm9ybWF0KGFtb3VudClcbiAgfVxuXG4gIGNvbnN0IGNhbGN1bGF0ZUludmVudG9yeVN0YXRzID0gKCkgPT4ge1xuICAgIGNvbnN0IHRvdGFsSXRlbXMgPSBpbnZlbnRvcnkubGVuZ3RoXG4gICAgY29uc3QgbG93U3RvY2tDb3VudCA9IGludmVudG9yeS5maWx0ZXIoaXRlbSA9PiBpdGVtLmN1cnJlbnRfc3RvY2sgPD0gaXRlbS5taW5pbXVtX3N0b2NrKS5sZW5ndGhcbiAgICBjb25zdCByZW9yZGVyTmVlZGVkQ291bnQgPSBpbnZlbnRvcnkuZmlsdGVyKGl0ZW0gPT4gaXRlbS5jdXJyZW50X3N0b2NrIDw9IGl0ZW0ucmVvcmRlcl9wb2ludCkubGVuZ3RoXG4gICAgY29uc3QgdG90YWxWYWx1ZSA9IGludmVudG9yeS5yZWR1Y2UoKHRvdGFsLCBpdGVtKSA9PiB7XG4gICAgICBjb25zdCBpdGVtVmFsdWUgPSBpdGVtLmN1cnJlbnRfc3RvY2sgKiAoaXRlbS5pbmdyZWRpZW50LmNvc3RfcGVyX3VuaXQgfHwgMClcbiAgICAgIHJldHVybiB0b3RhbCArIGl0ZW1WYWx1ZVxuICAgIH0sIDApXG5cbiAgICByZXR1cm4ge1xuICAgICAgdG90YWxJdGVtcyxcbiAgICAgIGxvd1N0b2NrQ291bnQsXG4gICAgICByZW9yZGVyTmVlZGVkQ291bnQsXG4gICAgICB0b3RhbFZhbHVlXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlV2FyZWhvdXNlQ2hhbmdlID0gKGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MU2VsZWN0RWxlbWVudD4pID0+IHtcbiAgICBzZXRTZWxlY3RlZFdhcmVob3VzZShldmVudC50YXJnZXQudmFsdWUpXG4gIH1cblxuICBjb25zdCBoYW5kbGVBZGRTdG9jayA9ICgpID0+IHtcbiAgICBzZXRJc0FkZFN0b2NrTW9kYWxPcGVuKHRydWUpXG4gIH1cblxuICBjb25zdCBoYW5kbGVCdWxrVXBkYXRlID0gKCkgPT4ge1xuICAgIHNldElzQnVsa1VwZGF0ZU1vZGFsT3Blbih0cnVlKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlVXBkYXRlU3RvY2sgPSAoaXRlbUlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBpdGVtID0gaW52ZW50b3J5LmZpbmQoaSA9PiBpLmlkID09PSBpdGVtSWQpXG4gICAgaWYgKGl0ZW0pIHtcbiAgICAgIHNldFNlbGVjdGVkSXRlbShpdGVtKVxuICAgICAgc2V0SXNVcGRhdGVTdG9ja01vZGFsT3Blbih0cnVlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVRyYW5zZmVyU3RvY2sgPSAoaXRlbUlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBpdGVtID0gaW52ZW50b3J5LmZpbmQoaSA9PiBpLmlkID09PSBpdGVtSWQpXG4gICAgaWYgKGl0ZW0pIHtcbiAgICAgIHNldFNlbGVjdGVkSXRlbShpdGVtKVxuICAgICAgc2V0SXNUcmFuc2ZlclN0b2NrTW9kYWxPcGVuKHRydWUpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU3RvY2tVcGRhdGVkID0gKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZFdhcmVob3VzZSkge1xuICAgICAgbG9hZEludmVudG9yeShzZWxlY3RlZFdhcmVob3VzZSlcbiAgICB9XG4gICAgbG9hZExvd1N0b2NrSXRlbXMoKVxuICB9XG5cbiAgY29uc3Qgc3RhdHMgPSBjYWxjdWxhdGVJbnZlbnRvcnlTdGF0cygpXG5cbiAgLy8gU2hvdyBsb2FkaW5nIHNwaW5uZXIgZHVyaW5nIGluaXRpYWwgbG9hZFxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxQcm90ZWN0ZWRSb3V0ZSBhbGxvd2VkUm9sZXM9e1snYWRtaW4nLCAnbWFuYWdlcicsICdzdGFmZiddfT5cbiAgICAgICAgPERhc2hib2FyZExheW91dD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTMyIHctMzIgYm9yZGVyLWItMiBib3JkZXItaW5kaWdvLTYwMFwiPjwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0Rhc2hib2FyZExheW91dD5cbiAgICAgIDwvUHJvdGVjdGVkUm91dGU+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8UHJvdGVjdGVkUm91dGUgYWxsb3dlZFJvbGVzPXtbJ2FkbWluJywgJ21hbmFnZXInLCAnc3RhZmYnXX0+XG4gICAgICA8RGFzaGJvYXJkTGF5b3V0PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IHNtOmp1c3RpZnktYmV0d2VlbiBzbTppdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPkludmVudG9yeSBNYW5hZ2VtZW50PC9oMT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNFwiPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkV2FyZWhvdXNlfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVXYXJlaG91c2VDaGFuZ2V9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMiBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctaW5kaWdvLTUwMCBmb2N1czpib3JkZXItaW5kaWdvLTUwMFwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3dhcmVob3VzZXMubGVuZ3RoID09PSAwfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlNlbGVjdCBXYXJlaG91c2U8L29wdGlvbj5cbiAgICAgICAgICAgICAgICB7d2FyZWhvdXNlcy5tYXAoKHdhcmVob3VzZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e3dhcmVob3VzZS5pZH0gdmFsdWU9e3dhcmVob3VzZS5pZH0+XG4gICAgICAgICAgICAgICAgICAgIHt3YXJlaG91c2UubmFtZX0gKHt3YXJlaG91c2UuY29kZX0pXG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkU3RvY2t9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IXNlbGVjdGVkV2FyZWhvdXNlfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctaW5kaWdvLTYwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiByb3VuZGVkLW1kIGhvdmVyOmJnLWluZGlnby03MDAgZGlzYWJsZWQ6YmctZ3JheS00MDAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBBZGQgU3RvY2tcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVCdWxrVXBkYXRlfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZFdhcmVob3VzZSB8fCBpbnZlbnRvcnkubGVuZ3RoID09PSAwfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHVycGxlLTYwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiByb3VuZGVkLW1kIGhvdmVyOmJnLXB1cnBsZS03MDAgZGlzYWJsZWQ6YmctZ3JheS00MDAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBCdWxrIFVwZGF0ZVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEVycm9yIEFsZXJ0ICovfVxuICAgICAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC0xMDAgYm9yZGVyIGJvcmRlci1yZWQtNDAwIHRleHQtcmVkLTcwMCBweC00IHB5LTMgcm91bmRlZCByZWxhdGl2ZVwiIHJvbGU9XCJhbGVydFwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9jayBzbTppbmxpbmVcIj57ZXJyb3J9PC9zcGFuPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RXJyb3IobnVsbCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgYm90dG9tLTAgcmlnaHQtMCBweC00IHB5LTNcIlxuICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJDbG9zZSBlcnJvciBtZXNzYWdlXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPiZ0aW1lczs8L3NwYW4+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiBMb3cgU3RvY2sgQWxlcnQgKi99XG4gICAgICAgICAge2xvd1N0b2NrSXRlbXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yZWQtNDAwIHRleHQteGwgbXItM1wiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiPuKaoO+4jzwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LXJlZC04MDBcIj5Mb3cgU3RvY2sgQWxlcnQ8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtsb3dTdG9ja0l0ZW1zLmxlbmd0aH0gaXRlbXtsb3dTdG9ja0l0ZW1zLmxlbmd0aCAhPT0gMSA/ICdzJyA6ICcnfSBcbiAgICAgICAgICAgICAgICAgICAge2xvd1N0b2NrSXRlbXMubGVuZ3RoID09PSAxID8gJyBpcycgOiAnIGFyZSd9IHJ1bm5pbmcgbG93IGFjcm9zcyBhbGwgd2FyZWhvdXNlc1xuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogSW52ZW50b3J5IFN0YXRzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBnYXAtNSBzbTpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBvdmVyZmxvdy1oaWRkZW4gc2hhZG93IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTVcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bFwiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiPvCfk6Y8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC01IHctMCBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkdCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFRvdGFsIEl0ZW1zXG4gICAgICAgICAgICAgICAgICAgICAgPC9kdD5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7c3RhdHMudG90YWxJdGVtc31cbiAgICAgICAgICAgICAgICAgICAgICA8L2RkPlxuICAgICAgICAgICAgICAgICAgICA8L2RsPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgb3ZlcmZsb3ctaGlkZGVuIHNoYWRvdyByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC01XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGxcIiBhcmlhLWhpZGRlbj1cInRydWVcIj7imqDvuI88L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC01IHctMCBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkdCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIExvdyBTdG9jayBJdGVtc1xuICAgICAgICAgICAgICAgICAgICAgIDwvZHQ+XG4gICAgICAgICAgICAgICAgICAgICAgPGRkIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1yZWQtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7c3RhdHMubG93U3RvY2tDb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICA8L2RkPlxuICAgICAgICAgICAgICAgICAgICA8L2RsPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgb3ZlcmZsb3ctaGlkZGVuIHNoYWRvdyByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC01XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGxcIiBhcmlhLWhpZGRlbj1cInRydWVcIj7wn5KwPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNSB3LTAgZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkbD5cbiAgICAgICAgICAgICAgICAgICAgICA8ZHQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBUb3RhbCBWYWx1ZVxuICAgICAgICAgICAgICAgICAgICAgIDwvZHQ+XG4gICAgICAgICAgICAgICAgICAgICAgPGRkIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdEN1cnJlbmN5KHN0YXRzLnRvdGFsVmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGQ+XG4gICAgICAgICAgICAgICAgICAgIDwvZGw+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBvdmVyZmxvdy1oaWRkZW4gc2hhZG93IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTVcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bFwiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiPvCflIQ8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC01IHctMCBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkdCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFJlb3JkZXIgTmVlZGVkXG4gICAgICAgICAgICAgICAgICAgICAgPC9kdD5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LXllbGxvdy02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzdGF0cy5yZW9yZGVyTmVlZGVkQ291bnR9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kZD5cbiAgICAgICAgICAgICAgICAgICAgPC9kbD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEludmVudG9yeSBUYWJsZSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdyBvdmVyZmxvdy1oaWRkZW4gc206cm91bmRlZC1tZFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHB5LTUgc206cHgtNlwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBsZWFkaW5nLTYgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgIEN1cnJlbnQgSW52ZW50b3J5XG4gICAgICAgICAgICAgICAge3NlbGVjdGVkV2FyZWhvdXNlICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBtbC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIC0ge3dhcmVob3VzZXMuZmluZCh3ID0+IHcuaWQgPT09IHNlbGVjdGVkV2FyZWhvdXNlKT8ubmFtZSB8fCAnVW5rbm93biBXYXJlaG91c2UnfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAge2lzSW52ZW50b3J5TG9hZGluZyA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTMyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItaW5kaWdvLTYwMFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAhc2VsZWN0ZWRXYXJlaG91c2UgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS04IHRleHQtY2VudGVyIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICBQbGVhc2Ugc2VsZWN0IGEgd2FyZWhvdXNlIHRvIHZpZXcgaW52ZW50b3J5XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IGludmVudG9yeS5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS04IHRleHQtY2VudGVyIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICBObyBpbnZlbnRvcnkgaXRlbXMgZm91bmQgZm9yIHRoaXMgd2FyZWhvdXNlXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBJbmdyZWRpZW50XG4gICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBDdXJyZW50IFN0b2NrXG4gICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBTdGF0dXNcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIE1pbi9NYXhcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFZhbHVlXG4gICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBBY3Rpb25zXG4gICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtpbnZlbnRvcnkubWFwKChpdGVtKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RvY2tTdGF0dXMgPSBnZXRTdG9ja1N0YXR1cyhpdGVtKVxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGl0ZW1WYWx1ZSA9IGl0ZW0uY3VycmVudF9zdG9jayAqIChpdGVtLmluZ3JlZGllbnQuY29zdF9wZXJfdW5pdCB8fCAwKVxuICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8dHIga2V5PXtpdGVtLmlkfSBjbGFzc05hbWU9XCJob3ZlcjpiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uaW5ncmVkaWVudC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5pbmdyZWRpZW50LmNvZGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5jdXJyZW50X3N0b2NrLnRvTG9jYWxlU3RyaW5nKCl9IHtpdGVtLmluZ3JlZGllbnQudW5pdH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1mdWxsICR7c3RvY2tTdGF0dXMuY29sb3J9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RvY2tTdGF0dXMuc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5taW5pbXVtX3N0b2NrLnRvTG9jYWxlU3RyaW5nKCl9IC8ge2l0ZW0ubWF4aW11bV9zdG9jay50b0xvY2FsZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRDdXJyZW5jeShpdGVtVmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVXBkYXRlU3RvY2soaXRlbS5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWluZGlnby02MDAgaG92ZXI6dGV4dC1pbmRpZ28tOTAwIG1yLTMgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFVwZGF0ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVUcmFuc2ZlclN0b2NrKGl0ZW0uaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDAgaG92ZXI6dGV4dC1ncmVlbi05MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRyYW5zZmVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBNb2RhbHMgKi99XG4gICAgICAgICAgPEFkZFN0b2NrTW9kYWxcbiAgICAgICAgICAgIGlzT3Blbj17aXNBZGRTdG9ja01vZGFsT3Blbn1cbiAgICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzQWRkU3RvY2tNb2RhbE9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgd2FyZWhvdXNlSWQ9e3NlbGVjdGVkV2FyZWhvdXNlfVxuICAgICAgICAgICAgb25TdG9ja0FkZGVkPXtoYW5kbGVTdG9ja1VwZGF0ZWR9XG4gICAgICAgICAgLz5cblxuICAgICAgICAgIDxVcGRhdGVTdG9ja01vZGFsXG4gICAgICAgICAgICBpc09wZW49e2lzVXBkYXRlU3RvY2tNb2RhbE9wZW59XG4gICAgICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRJc1VwZGF0ZVN0b2NrTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgIGl0ZW09e3NlbGVjdGVkSXRlbX1cbiAgICAgICAgICAgIG9uU3RvY2tVcGRhdGVkPXtoYW5kbGVTdG9ja1VwZGF0ZWR9XG4gICAgICAgICAgLz5cblxuICAgICAgICAgIDxUcmFuc2ZlclN0b2NrTW9kYWxcbiAgICAgICAgICAgIGlzT3Blbj17aXNUcmFuc2ZlclN0b2NrTW9kYWxPcGVufVxuICAgICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNUcmFuc2ZlclN0b2NrTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgIGl0ZW09e3NlbGVjdGVkSXRlbX1cbiAgICAgICAgICAgIG9uVHJhbnNmZXJDcmVhdGVkPXtoYW5kbGVTdG9ja1VwZGF0ZWR9XG4gICAgICAgICAgLz5cblxuICAgICAgICAgIDxCdWxrVXBkYXRlTW9kYWxcbiAgICAgICAgICAgIGlzT3Blbj17aXNCdWxrVXBkYXRlTW9kYWxPcGVufVxuICAgICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNCdWxrVXBkYXRlTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgIGl0ZW1zPXtpbnZlbnRvcnl9XG4gICAgICAgICAgICBvbkJ1bGtVcGRhdGVkPXtoYW5kbGVTdG9ja1VwZGF0ZWR9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0Rhc2hib2FyZExheW91dD5cbiAgICA8L1Byb3RlY3RlZFJvdXRlPlxuICApXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJ1c2VBdXRoIiwiUHJvdGVjdGVkUm91dGUiLCJEYXNoYm9hcmRMYXlvdXQiLCJBZGRTdG9ja01vZGFsIiwiVXBkYXRlU3RvY2tNb2RhbCIsIlRyYW5zZmVyU3RvY2tNb2RhbCIsIkJ1bGtVcGRhdGVNb2RhbCIsImludmVudG9yeVNlcnZpY2UiLCJ3YXJlaG91c2VTZXJ2aWNlIiwiSW52ZW50b3J5UGFnZSIsIndhcmVob3VzZXMiLCJwcm9maWxlIiwiaW52ZW50b3J5Iiwic2V0SW52ZW50b3J5IiwiZmluaXNoZWRHb29kcyIsInNldEZpbmlzaGVkR29vZHMiLCJzZXRXYXJlaG91c2VzIiwic2VsZWN0ZWRXYXJlaG91c2UiLCJzZXRTZWxlY3RlZFdhcmVob3VzZSIsImxvd1N0b2NrSXRlbXMiLCJzZXRMb3dTdG9ja0l0ZW1zIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNJbnZlbnRvcnlMb2FkaW5nIiwic2V0SXNJbnZlbnRvcnlMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImlzQWRkU3RvY2tNb2RhbE9wZW4iLCJzZXRJc0FkZFN0b2NrTW9kYWxPcGVuIiwiaXNVcGRhdGVTdG9ja01vZGFsT3BlbiIsInNldElzVXBkYXRlU3RvY2tNb2RhbE9wZW4iLCJpc1RyYW5zZmVyU3RvY2tNb2RhbE9wZW4iLCJzZXRJc1RyYW5zZmVyU3RvY2tNb2RhbE9wZW4iLCJpc0J1bGtVcGRhdGVNb2RhbE9wZW4iLCJzZXRJc0J1bGtVcGRhdGVNb2RhbE9wZW4iLCJzZWxlY3RlZEl0ZW0iLCJzZXRTZWxlY3RlZEl0ZW0iLCJsb2FkV2FyZWhvdXNlcyIsImRhdGEiLCJnZXRBbGxXYXJlaG91c2VzIiwibGVuZ3RoIiwiaWQiLCJlcnIiLCJlcnJvck1lc3NhZ2UiLCJFcnJvciIsIm1lc3NhZ2UiLCJjb25zb2xlIiwibG9hZEludmVudG9yeSIsIndhcmVob3VzZUlkIiwiZ2V0V2FyZWhvdXNlSW52ZW50b3J5IiwibG9hZEZpbmlzaGVkR29vZHMiLCJzdXBhYmFzZSIsImZyb20iLCJzZWxlY3QiLCJlcSIsImxvYWRMb3dTdG9ja0l0ZW1zIiwiZ2V0TG93U3RvY2tJdGVtcyIsImluaXRpYWxpemVEYXRhIiwiUHJvbWlzZSIsImFsbCIsImdldFN0b2NrU3RhdHVzIiwiaXRlbSIsImN1cnJlbnRfc3RvY2siLCJtaW5pbXVtX3N0b2NrIiwic3RhdHVzIiwiY29sb3IiLCJyZW9yZGVyX3BvaW50IiwiZm9ybWF0Q3VycmVuY3kiLCJhbW91bnQiLCJJbnRsIiwiTnVtYmVyRm9ybWF0Iiwic3R5bGUiLCJjdXJyZW5jeSIsIm1pbmltdW1GcmFjdGlvbkRpZ2l0cyIsIm1heGltdW1GcmFjdGlvbkRpZ2l0cyIsImZvcm1hdCIsImNhbGN1bGF0ZUludmVudG9yeVN0YXRzIiwidG90YWxJdGVtcyIsImxvd1N0b2NrQ291bnQiLCJmaWx0ZXIiLCJyZW9yZGVyTmVlZGVkQ291bnQiLCJ0b3RhbFZhbHVlIiwicmVkdWNlIiwidG90YWwiLCJpdGVtVmFsdWUiLCJpbmdyZWRpZW50IiwiY29zdF9wZXJfdW5pdCIsImhhbmRsZVdhcmVob3VzZUNoYW5nZSIsImV2ZW50IiwidGFyZ2V0IiwidmFsdWUiLCJoYW5kbGVBZGRTdG9jayIsImhhbmRsZUJ1bGtVcGRhdGUiLCJoYW5kbGVVcGRhdGVTdG9jayIsIml0ZW1JZCIsImZpbmQiLCJpIiwiaGFuZGxlVHJhbnNmZXJTdG9jayIsImhhbmRsZVN0b2NrVXBkYXRlZCIsInN0YXRzIiwiYWxsb3dlZFJvbGVzIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJvbkNoYW5nZSIsImRpc2FibGVkIiwib3B0aW9uIiwibWFwIiwid2FyZWhvdXNlIiwibmFtZSIsImNvZGUiLCJidXR0b24iLCJvbkNsaWNrIiwicm9sZSIsInNwYW4iLCJhcmlhLWxhYmVsIiwiYXJpYS1oaWRkZW4iLCJoMyIsInAiLCJkbCIsImR0IiwiZGQiLCJ3IiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsInN0b2NrU3RhdHVzIiwidGQiLCJ0b0xvY2FsZVN0cmluZyIsInVuaXQiLCJtYXhpbXVtX3N0b2NrIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uU3RvY2tBZGRlZCIsIm9uU3RvY2tVcGRhdGVkIiwib25UcmFuc2ZlckNyZWF0ZWQiLCJpdGVtcyIsIm9uQnVsa1VwZGF0ZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inventory/page.tsx\n"));

/***/ })

});