"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsService: () => (/* binding */ analyticsService),\n/* harmony export */   branchService: () => (/* binding */ branchService),\n/* harmony export */   ingredientService: () => (/* binding */ ingredientService),\n/* harmony export */   inventoryService: () => (/* binding */ inventoryService),\n/* harmony export */   kitchenService: () => (/* binding */ kitchenService),\n/* harmony export */   menuService: () => (/* binding */ menuService),\n/* harmony export */   productionService: () => (/* binding */ productionService),\n/* harmony export */   salesService: () => (/* binding */ salesService),\n/* harmony export */   warehouseService: () => (/* binding */ warehouseService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _auditLog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auditLog */ \"(app-pages-browser)/./src/lib/auditLog.ts\");\n\n\n// Inventory Management Functions\nconst inventoryService = {\n    // Get all inventory items for a warehouse\n    async getWarehouseInventory (warehouseId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*)\\n      \").eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get low stock items across all warehouses (optimized with database filtering)\n    async getLowStockItems (branchId) {\n        // Use a more efficient query with database-side filtering\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('get_low_stock_items_optimized', {\n            branch_id_param: branchId || null\n        });\n        if (error) {\n            // Fallback to client-side filtering if RPC function doesn't exist\n            console.warn('RPC function not available, using fallback query');\n            return this.getLowStockItemsFallback(branchId);\n        }\n        return data || [];\n    },\n    // Fallback method for low stock items\n    async getLowStockItemsFallback (branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*)\\n      \").filter('current_stock', 'lt', 'minimum_stock') // Database-side filtering\n        ;\n        if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query.order('current_stock', {\n            ascending: true\n        }).limit(100) // Limit results for performance\n        ;\n        if (error) throw error;\n        // Filter low stock items in JavaScript\n        // const lowStockItems = data?.filter(item =>\n        //   item.current_stock < item.minimum_stock\n        // ).sort((a, b) => a.current_stock - b.current_stock)\n        // return lowStockItems\n        return data || [];\n    },\n    // async getLowStockItems(branchId?: string) {\n    //   let query = supabase\n    //     .from('inventory')\n    //     .select(`\n    //       *,\n    //       ingredient:ingredients(*),\n    //       warehouse:warehouses(*)\n    //     `)\n    //     .filter('current_stock', 'lt', 10)\n    //   if (branchId) {\n    //     query = query.eq('warehouses.branch_id', branchId)\n    //   }\n    //   const { data, error } = await query.order('current_stock', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    // Update stock levels with enhanced validation and transaction safety\n    async updateStock (inventoryId, newStock, movementType, notes, performedBy) {\n        // Input validation\n        if (!inventoryId || newStock < 0) {\n            throw new Error('Invalid input: inventory ID is required and stock cannot be negative');\n        }\n        if (![\n            'in',\n            'out',\n            'adjustment',\n            'waste'\n        ].includes(movementType)) {\n            throw new Error('Invalid movement type');\n        }\n        // Fetch current inventory with detailed information\n        const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(unit, name),\\n        warehouse:warehouses(name, code)\\n      \").eq('id', inventoryId).single();\n        if (fetchError) {\n            throw new Error(\"Failed to fetch inventory: \".concat(fetchError.message));\n        }\n        const quantity = newStock - inventory.current_stock;\n        const isIncrease = quantity > 0;\n        // Business rule validation\n        if (newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n            var _inventory_ingredient;\n            throw new Error(\"Stock cannot exceed maximum limit of \".concat(inventory.maximum_stock, \" \").concat((_inventory_ingredient = inventory.ingredient) === null || _inventory_ingredient === void 0 ? void 0 : _inventory_ingredient.unit));\n        }\n        // Check if this would create negative stock for outbound movements\n        if (movementType === 'out' && newStock < 0) {\n            throw new Error('Cannot reduce stock below zero');\n        }\n        try {\n            var _inventory_ingredient1, _inventory_ingredient2, _inventory_warehouse;\n            // Use a transaction-like approach with error handling\n            const timestamp = new Date().toISOString();\n            // Update inventory record\n            const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                current_stock: newStock,\n                updated_at: timestamp,\n                last_restocked_at: isIncrease ? timestamp : inventory.last_restocked_at\n            }).eq('id', inventoryId);\n            if (updateError) {\n                throw new Error(\"Failed to update inventory: \".concat(updateError.message));\n            }\n            // Record stock movement with enhanced details\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n                warehouse_id: inventory.warehouse_id,\n                ingredient_id: inventory.ingredient_id,\n                movement_type: movementType,\n                quantity: Math.abs(quantity),\n                unit: ((_inventory_ingredient1 = inventory.ingredient) === null || _inventory_ingredient1 === void 0 ? void 0 : _inventory_ingredient1.unit) || 'kg',\n                reference_type: 'manual_adjustment',\n                notes: notes || \"Stock \".concat(movementType, \" - \").concat((_inventory_ingredient2 = inventory.ingredient) === null || _inventory_ingredient2 === void 0 ? void 0 : _inventory_ingredient2.name, \" at \").concat((_inventory_warehouse = inventory.warehouse) === null || _inventory_warehouse === void 0 ? void 0 : _inventory_warehouse.name),\n                performed_by: performedBy,\n                created_at: timestamp\n            });\n            if (movementError) {\n                // Attempt to rollback the inventory update\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: inventory.current_stock,\n                    updated_at: inventory.updated_at\n                }).eq('id', inventoryId);\n                throw new Error(\"Failed to record stock movement: \".concat(movementError.message));\n            }\n            // Log the stock change for audit trail\n            if (performedBy) {\n                try {\n                    var _inventory_ingredient3, _inventory_warehouse1, _inventory_ingredient4;\n                    await (0,_auditLog__WEBPACK_IMPORTED_MODULE_1__.logStockChange)(movementType, inventoryId, performedBy, 'staff', {\n                        ingredient_name: ((_inventory_ingredient3 = inventory.ingredient) === null || _inventory_ingredient3 === void 0 ? void 0 : _inventory_ingredient3.name) || 'Unknown',\n                        warehouse_name: ((_inventory_warehouse1 = inventory.warehouse) === null || _inventory_warehouse1 === void 0 ? void 0 : _inventory_warehouse1.name) || 'Unknown',\n                        previous_stock: inventory.current_stock,\n                        new_stock: newStock,\n                        quantity_changed: Math.abs(quantity),\n                        unit: ((_inventory_ingredient4 = inventory.ingredient) === null || _inventory_ingredient4 === void 0 ? void 0 : _inventory_ingredient4.unit) || 'kg',\n                        notes: notes\n                    }, inventory.warehouse_id);\n                } catch (auditError) {\n                    console.error('Failed to log stock change:', auditError);\n                // Don't fail the operation if audit logging fails\n                }\n            }\n            return {\n                success: true,\n                previousStock: inventory.current_stock,\n                newStock: newStock,\n                quantity: Math.abs(quantity),\n                movementType,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(\"Stock update failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Create new inventory record\n    async createInventoryRecord (warehouseId, ingredientId, initialStock, notes) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            current_stock: initialStock,\n            minimum_stock: 0,\n            maximum_stock: initialStock * 10,\n            reorder_point: initialStock * 0.2\n        }).select().single();\n        if (error) throw error;\n        // Record initial stock movement\n        const { data: ingredient } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select('unit').eq('id', ingredientId).single();\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            movement_type: 'in',\n            quantity: initialStock,\n            unit: (ingredient === null || ingredient === void 0 ? void 0 : ingredient.unit) || 'kg',\n            reference_type: 'initial_stock',\n            notes: notes || 'Initial stock entry'\n        });\n        return data;\n    },\n    // Get stock movements for a warehouse\n    async getStockMovements (warehouseId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*),\\n        performer:profiles(full_name)\\n      \").eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    },\n    // Create warehouse transfer\n    async createTransfer (fromWarehouseId, toWarehouseId, items, requestedBy, notes) {\n        // Create transfer record\n        const { data: transfer, error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').insert({\n            from_warehouse_id: fromWarehouseId,\n            to_warehouse_id: toWarehouseId,\n            requested_by: requestedBy,\n            total_items: items.length,\n            notes,\n            status: 'pending'\n        }).select().single();\n        if (transferError) throw transferError;\n        // Create transfer items\n        const transferItems = items.map((item)=>({\n                transfer_id: transfer.id,\n                ingredient_id: item.ingredient_id,\n                requested_quantity: item.quantity,\n                unit: item.unit,\n                notes: item.notes\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').insert(transferItems);\n        if (itemsError) throw itemsError;\n        return transfer;\n    },\n    // Get pending transfers\n    async getPendingTransfers (warehouseId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(\"\\n        *,\\n        from_warehouse:warehouses!from_warehouse_id(*),\\n        to_warehouse:warehouses!to_warehouse_id(*),\\n        requester:profiles!requested_by(*),\\n        transfer_items(*, ingredient:ingredients(*))\\n      \").eq('status', 'pending');\n        if (warehouseId) {\n            query = query.or(\"from_warehouse_id.eq.\".concat(warehouseId, \",to_warehouse_id.eq.\").concat(warehouseId));\n        }\n        const { data, error } = await query.order('requested_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Approve transfer with enhanced validation\n    async approveTransfer (transferId, approvedBy, approvedItems) {\n        let autoComplete = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        try {\n            // Validate transfer exists and is in pending status\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(\"\\n          *,\\n          from_warehouse:warehouses!from_warehouse_id(*),\\n          to_warehouse:warehouses!to_warehouse_id(*),\\n          transfer_items(*, ingredient:ingredients(*))\\n        \").eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(\"Transfer not found: \".concat(fetchError.message));\n            }\n            if (transfer.status !== 'pending') {\n                throw new Error(\"Cannot approve transfer with status: \".concat(transfer.status));\n            }\n            // Validate stock availability for approved quantities\n            for (const approvedItem of approvedItems){\n                const transferItem = transfer.transfer_items.find((item)=>item.id === approvedItem.id);\n                if (!transferItem) {\n                    throw new Error(\"Transfer item not found: \".concat(approvedItem.id));\n                }\n                // Check current stock in source warehouse\n                const { data: inventory, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock').eq('warehouse_id', transfer.from_warehouse_id).eq('ingredient_id', transferItem.ingredient_id).single();\n                if (inventoryError) {\n                    throw new Error(\"Cannot verify stock for ingredient: \".concat(transferItem.ingredient.name));\n                }\n                if (inventory.current_stock < approvedItem.approved_quantity) {\n                    throw new Error(\"Insufficient stock for \".concat(transferItem.ingredient.name, \". Available: \").concat(inventory.current_stock, \", Requested: \").concat(approvedItem.approved_quantity));\n                }\n            }\n            const timestamp = new Date().toISOString();\n            // Update transfer status\n            const { error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'approved',\n                approved_by: approvedBy,\n                approved_at: timestamp\n            }).eq('id', transferId);\n            if (transferError) {\n                throw new Error(\"Failed to approve transfer: \".concat(transferError.message));\n            }\n            // Update approved quantities for items\n            for (const item of approvedItems){\n                const { error: itemError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').update({\n                    approved_quantity: item.approved_quantity\n                }).eq('id', item.id);\n                if (itemError) {\n                    throw new Error(\"Failed to update approved quantity: \".concat(itemError.message));\n                }\n            }\n            // Auto-complete the transfer if requested\n            if (autoComplete) {\n                try {\n                    const completionResult = await this.completeTransfer(transferId, approvedBy);\n                    return {\n                        success: true,\n                        transferId,\n                        approvedAt: timestamp,\n                        approvedItems: approvedItems.length,\n                        autoCompleted: true,\n                        completionResult\n                    };\n                } catch (completionError) {\n                    // If auto-completion fails, log the error but don't fail the approval\n                    console.error('Auto-completion failed after approval:', completionError);\n                    return {\n                        success: true,\n                        transferId,\n                        approvedAt: timestamp,\n                        approvedItems: approvedItems.length,\n                        autoCompleted: false,\n                        autoCompletionError: completionError instanceof Error ? completionError.message : 'Unknown error'\n                    };\n                }\n            }\n            return {\n                success: true,\n                transferId,\n                approvedAt: timestamp,\n                approvedItems: approvedItems.length,\n                autoCompleted: false\n            };\n        } catch (error) {\n            throw new Error(\"Transfer approval failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Complete transfer and update inventory levels\n    async completeTransfer (transferId, completedBy) {\n        try {\n            // Fetch transfer with all details\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(\"\\n          *,\\n          from_warehouse:warehouses!from_warehouse_id(*),\\n          to_warehouse:warehouses!to_warehouse_id(*),\\n          transfer_items(*, ingredient:ingredients(*))\\n        \").eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(\"Transfer not found: \".concat(fetchError.message));\n            }\n            if (transfer.status !== 'approved' && transfer.status !== 'in_transit') {\n                throw new Error(\"Cannot complete transfer with status: \".concat(transfer.status));\n            }\n            const timestamp = new Date().toISOString();\n            const stockMovements = [];\n            // Process each transfer item\n            for (const item of transfer.transfer_items){\n                const approvedQty = item.approved_quantity || item.requested_quantity;\n                // Reduce stock from source warehouse\n                const { data: sourceInventory, error: sourceError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', transfer.from_warehouse_id).eq('ingredient_id', item.ingredient_id).single();\n                if (sourceError) {\n                    throw new Error(\"Source inventory not found for \".concat(item.ingredient.name));\n                }\n                if (sourceInventory.current_stock < approvedQty) {\n                    throw new Error(\"Insufficient stock for \".concat(item.ingredient.name, \". Available: \").concat(sourceInventory.current_stock, \", Required: \").concat(approvedQty));\n                }\n                // Update source inventory\n                const { error: sourceUpdateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: sourceInventory.current_stock - approvedQty,\n                    updated_at: timestamp\n                }).eq('id', sourceInventory.id);\n                if (sourceUpdateError) {\n                    throw new Error(\"Failed to update source inventory: \".concat(sourceUpdateError.message));\n                }\n                // Add stock movement for source (outbound)\n                stockMovements.push({\n                    warehouse_id: transfer.from_warehouse_id,\n                    ingredient_id: item.ingredient_id,\n                    movement_type: 'transfer',\n                    quantity: approvedQty,\n                    unit: item.unit,\n                    reference_type: 'transfer_out',\n                    reference_id: transferId,\n                    notes: \"Transfer out to \".concat(transfer.to_warehouse.name, \" - \").concat(item.ingredient.name),\n                    performed_by: completedBy,\n                    created_at: timestamp\n                });\n                // Check if destination inventory exists\n                const { data: destInventory, error: destFetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', transfer.to_warehouse_id).eq('ingredient_id', item.ingredient_id).maybeSingle();\n                if (destFetchError) {\n                    throw new Error(\"Error checking destination inventory: \".concat(destFetchError.message));\n                }\n                if (destInventory) {\n                    // Update existing destination inventory\n                    const { error: destUpdateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: destInventory.current_stock + approvedQty,\n                        updated_at: timestamp,\n                        last_restocked_at: timestamp\n                    }).eq('id', destInventory.id);\n                    if (destUpdateError) {\n                        throw new Error(\"Failed to update destination inventory: \".concat(destUpdateError.message));\n                    }\n                } else {\n                    // Create new destination inventory record\n                    const { error: destCreateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n                        warehouse_id: transfer.to_warehouse_id,\n                        ingredient_id: item.ingredient_id,\n                        current_stock: approvedQty,\n                        minimum_stock: 0,\n                        maximum_stock: approvedQty * 10,\n                        reorder_point: approvedQty * 0.2,\n                        last_restocked_at: timestamp\n                    });\n                    if (destCreateError) {\n                        throw new Error(\"Failed to create destination inventory: \".concat(destCreateError.message));\n                    }\n                }\n                // Add stock movement for destination (inbound)\n                stockMovements.push({\n                    warehouse_id: transfer.to_warehouse_id,\n                    ingredient_id: item.ingredient_id,\n                    movement_type: 'transfer',\n                    quantity: approvedQty,\n                    unit: item.unit,\n                    reference_type: 'transfer_in',\n                    reference_id: transferId,\n                    notes: \"Transfer in from \".concat(transfer.from_warehouse.name, \" - \").concat(item.ingredient.name),\n                    performed_by: completedBy,\n                    created_at: timestamp\n                });\n            }\n            // Insert all stock movements\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n            if (movementError) {\n                throw new Error(\"Failed to record stock movements: \".concat(movementError.message));\n            }\n            // Update transfer status to completed\n            const { error: completeError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'completed',\n                completed_at: timestamp\n            }).eq('id', transferId);\n            if (completeError) {\n                throw new Error(\"Failed to complete transfer: \".concat(completeError.message));\n            }\n            return {\n                success: true,\n                transferId,\n                completedAt: timestamp,\n                itemsTransferred: transfer.transfer_items.length,\n                stockMovements: stockMovements.length\n            };\n        } catch (error) {\n            throw new Error(\"Transfer completion failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Cancel transfer\n    async cancelTransfer (transferId, cancelledBy, reason) {\n        try {\n            // Validate transfer exists and can be cancelled\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select('*').eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(\"Transfer not found: \".concat(fetchError.message));\n            }\n            if (![\n                'pending',\n                'approved',\n                'in_transit'\n            ].includes(transfer.status)) {\n                throw new Error(\"Cannot cancel transfer with status: \".concat(transfer.status));\n            }\n            const timestamp = new Date().toISOString();\n            // Update transfer status\n            const { error: cancelError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'cancelled',\n                notes: transfer.notes ? \"\".concat(transfer.notes, \"\\n\\nCancelled: \").concat(reason || 'No reason provided') : \"Cancelled: \".concat(reason || 'No reason provided'),\n                completed_at: timestamp\n            }).eq('id', transferId);\n            if (cancelError) {\n                throw new Error(\"Failed to cancel transfer: \".concat(cancelError.message));\n            }\n            return {\n                success: true,\n                transferId,\n                cancelledAt: timestamp,\n                reason: reason || 'No reason provided'\n            };\n        } catch (error) {\n            throw new Error(\"Transfer cancellation failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Bulk update stock levels for multiple items\n    async bulkUpdateStock (updates, performedBy) {\n        if (!updates || updates.length === 0) {\n            throw new Error('No updates provided');\n        }\n        const results = [];\n        const timestamp = new Date().toISOString();\n        const stockMovements = [];\n        try {\n            // Process each update\n            for (const update of updates){\n                try {\n                    var _inventory_ingredient, _inventory_ingredient1;\n                    // Validate input\n                    if (!update.inventoryId || update.newStock < 0) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: 'Invalid input: inventory ID is required and stock cannot be negative'\n                        });\n                        continue;\n                    }\n                    // Fetch current inventory\n                    const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n              *,\\n              ingredient:ingredients(unit, name),\\n              warehouse:warehouses(name, code)\\n            \").eq('id', update.inventoryId).single();\n                    if (fetchError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Failed to fetch inventory: \".concat(fetchError.message)\n                        });\n                        continue;\n                    }\n                    const quantity = update.newStock - inventory.current_stock;\n                    // Business rule validation\n                    if (update.newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n                        var _inventory_ingredient2;\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Stock cannot exceed maximum limit of \".concat(inventory.maximum_stock, \" \").concat((_inventory_ingredient2 = inventory.ingredient) === null || _inventory_ingredient2 === void 0 ? void 0 : _inventory_ingredient2.unit)\n                        });\n                        continue;\n                    }\n                    // Update inventory\n                    const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: update.newStock,\n                        updated_at: timestamp,\n                        last_restocked_at: quantity > 0 ? timestamp : inventory.last_restocked_at\n                    }).eq('id', update.inventoryId);\n                    if (updateError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Failed to update inventory: \".concat(updateError.message)\n                        });\n                        continue;\n                    }\n                    // Prepare stock movement record\n                    stockMovements.push({\n                        warehouse_id: inventory.warehouse_id,\n                        ingredient_id: inventory.ingredient_id,\n                        movement_type: update.movementType,\n                        quantity: Math.abs(quantity),\n                        unit: ((_inventory_ingredient = inventory.ingredient) === null || _inventory_ingredient === void 0 ? void 0 : _inventory_ingredient.unit) || 'kg',\n                        reference_type: 'bulk_adjustment',\n                        notes: update.notes || \"Bulk stock \".concat(update.movementType, \" - \").concat((_inventory_ingredient1 = inventory.ingredient) === null || _inventory_ingredient1 === void 0 ? void 0 : _inventory_ingredient1.name),\n                        performed_by: performedBy,\n                        created_at: timestamp\n                    });\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: true,\n                        previousStock: inventory.current_stock,\n                        newStock: update.newStock\n                    });\n                } catch (error) {\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: false,\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    });\n                }\n            }\n            // Insert all stock movements in batch\n            if (stockMovements.length > 0) {\n                const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n                if (movementError) {\n                    throw new Error(\"Failed to record stock movements: \".concat(movementError.message));\n                }\n            }\n            const successCount = results.filter((r)=>r.success).length;\n            const failureCount = results.filter((r)=>!r.success).length;\n            return {\n                success: failureCount === 0,\n                totalUpdates: updates.length,\n                successCount,\n                failureCount,\n                results,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(\"Bulk update failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Get comprehensive low stock alerts with reorder suggestions\n    async getLowStockAlertsWithReorderSuggestions (branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*)\\n      \");\n        if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query;\n        if (error) throw error;\n        // Process and categorize alerts\n        const alerts = data === null || data === void 0 ? void 0 : data.map((item)=>{\n            const stockLevel = item.current_stock;\n            const minStock = item.minimum_stock;\n            const reorderPoint = item.reorder_point;\n            const maxStock = item.maximum_stock;\n            let alertLevel = 'good';\n            let suggestedReorderQuantity = 0;\n            let daysUntilStockout = null;\n            // Determine alert level\n            if (stockLevel <= 0) {\n                alertLevel = 'critical';\n            } else if (stockLevel <= minStock) {\n                alertLevel = 'critical';\n            } else if (stockLevel <= reorderPoint) {\n                alertLevel = 'reorder';\n            } else if (stockLevel <= minStock * 1.5) {\n                alertLevel = 'low';\n            }\n            // Calculate suggested reorder quantity\n            if (alertLevel !== 'good') {\n                const targetStock = maxStock || minStock * 3;\n                suggestedReorderQuantity = Math.max(0, targetStock - stockLevel);\n            }\n            // Estimate days until stockout (simplified calculation)\n            // In a real system, you'd use historical consumption data\n            if (stockLevel > 0 && minStock > 0) {\n                const avgDailyConsumption = minStock / 30 // Rough estimate\n                ;\n                if (avgDailyConsumption > 0) {\n                    daysUntilStockout = Math.floor(stockLevel / avgDailyConsumption);\n                }\n            }\n            return {\n                ...item,\n                alertLevel,\n                suggestedReorderQuantity,\n                daysUntilStockout,\n                stockPercentage: maxStock > 0 ? stockLevel / maxStock * 100 : 0\n            };\n        }).filter((item)=>item.alertLevel !== 'good').sort((a, b)=>{\n            // Sort by alert level priority, then by days until stockout\n            const alertPriority = {\n                critical: 0,\n                low: 1,\n                reorder: 2,\n                good: 3\n            };\n            const aPriority = alertPriority[a.alertLevel];\n            const bPriority = alertPriority[b.alertLevel];\n            if (aPriority !== bPriority) {\n                return aPriority - bPriority;\n            }\n            // If same alert level, sort by days until stockout (ascending)\n            if (a.daysUntilStockout !== null && b.daysUntilStockout !== null) {\n                return a.daysUntilStockout - b.daysUntilStockout;\n            }\n            return 0;\n        });\n        return alerts || [];\n    },\n    // Get inventory performance metrics\n    async getInventoryMetrics (warehouseId, branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(cost_per_unit),\\n        warehouse:warehouses(branch_id)\\n      \");\n        if (warehouseId) {\n            query = query.eq('warehouse_id', warehouseId);\n        } else if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query;\n        if (error) throw error;\n        if (!data || data.length === 0) {\n            return {\n                totalItems: 0,\n                totalValue: 0,\n                lowStockCount: 0,\n                criticalStockCount: 0,\n                reorderNeededCount: 0,\n                averageStockLevel: 0,\n                turnoverRate: 0\n            };\n        }\n        const totalItems = data.length;\n        const totalValue = data.reduce((sum, item)=>{\n            var _item_ingredient;\n            const cost = ((_item_ingredient = item.ingredient) === null || _item_ingredient === void 0 ? void 0 : _item_ingredient.cost_per_unit) || 0;\n            return sum + item.current_stock * cost;\n        }, 0);\n        const lowStockCount = data.filter((item)=>item.current_stock <= item.minimum_stock && item.current_stock > 0).length;\n        const criticalStockCount = data.filter((item)=>item.current_stock <= 0).length;\n        const reorderNeededCount = data.filter((item)=>item.current_stock <= item.reorder_point).length;\n        const averageStockLevel = data.reduce((sum, item)=>{\n            const maxStock = item.maximum_stock || item.minimum_stock * 3;\n            const stockPercentage = maxStock > 0 ? item.current_stock / maxStock * 100 : 0;\n            return sum + stockPercentage;\n        }, 0) / totalItems;\n        return {\n            totalItems,\n            totalValue,\n            lowStockCount,\n            criticalStockCount,\n            reorderNeededCount,\n            averageStockLevel: Math.round(averageStockLevel),\n            turnoverRate: 0 // Would need historical data to calculate\n        };\n    },\n    // Generate automatic reorder suggestions\n    async generateReorderSuggestions (warehouseId, branchId) {\n        const alerts = await this.getLowStockAlertsWithReorderSuggestions(branchId);\n        const suggestions = alerts.filter((item)=>item.suggestedReorderQuantity > 0).map((item)=>({\n                inventoryId: item.id,\n                ingredientId: item.ingredient.id,\n                ingredientName: item.ingredient.name,\n                ingredientCode: item.ingredient.code,\n                warehouseId: item.warehouse_id,\n                warehouseName: item.warehouse.name,\n                currentStock: item.current_stock,\n                minimumStock: item.minimum_stock,\n                reorderPoint: item.reorder_point,\n                maximumStock: item.maximum_stock,\n                suggestedQuantity: item.suggestedReorderQuantity,\n                estimatedCost: (item.ingredient.cost_per_unit || 0) * item.suggestedReorderQuantity,\n                priority: item.alertLevel,\n                daysUntilStockout: item.daysUntilStockout,\n                unit: item.ingredient.unit\n            })).sort((a, b)=>{\n            const priorityOrder = {\n                critical: 0,\n                low: 1,\n                reorder: 2\n            };\n            return priorityOrder[a.priority] - priorityOrder[b.priority];\n        });\n        const totalEstimatedCost = suggestions.reduce((sum, item)=>sum + item.estimatedCost, 0);\n        const criticalCount = suggestions.filter((item)=>item.priority === 'critical').length;\n        const lowCount = suggestions.filter((item)=>item.priority === 'low').length;\n        const reorderCount = suggestions.filter((item)=>item.priority === 'reorder').length;\n        return {\n            suggestions,\n            summary: {\n                totalItems: suggestions.length,\n                totalEstimatedCost,\n                criticalCount,\n                lowCount,\n                reorderCount\n            }\n        };\n    }\n};\n// Branch Management Functions\n// export const branchService = {\n//   // Get all branches\n//   async getAllBranches() {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*)\n//       `)\n//       .eq('is_active', true)\n//       .order('name')\n//     if (error) throw error\n//     return data\n//   },\n//   // Get branch with details\n//   async getBranchDetails(branchId: string) {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*),\n//         warehouses(*),\n//         kitchens(*)\n//       `)\n//       .eq('id', branchId)\n//       .single()\n//     if (error) throw error\n//     return data\n//   }\n// }\nconst branchService = {\n    // Get all branches\n    async getAllBranches () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(\"\\n        *,\\n        manager:profiles!fk_branches_manager(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get branch with details\n    async getBranchDetails (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(\"\\n        *,\\n        manager:profiles!fk_branches_manager(*),\\n        warehouses(*),\\n        kitchens(*)\\n      \").eq('id', branchId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Warehouse Management Functions\nconst warehouseService = {\n    // Get all warehouses\n    async getAllWarehouses () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(\"\\n        *,\\n        branch:branches(*),\\n        manager:profiles(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get warehouses by branch\n    async getWarehousesByBranch (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(\"\\n        *,\\n        manager:profiles(*)\\n      \").eq('branch_id', branchId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Ingredient Management Functions\nconst ingredientService = {\n    // Get all ingredients\n    async getAllIngredients () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(\"\\n        *,\\n        category:ingredient_categories(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get ingredients by category\n    async getIngredientsByCategory (categoryId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(\"\\n        *,\\n        category:ingredient_categories(*)\\n      \").eq('category_id', categoryId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Production Management Functions\nconst productionService = {\n    // Get all recipes\n    async getAllRecipes () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(\"\\n        *,\\n        recipe_ingredients(\\n          *,\\n          ingredient:ingredients(*)\\n        )\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get recipe details\n    async getRecipeDetails (recipeId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(\"\\n        *,\\n        recipe_ingredients(\\n          *,\\n          ingredient:ingredients(*)\\n        )\\n      \").eq('id', recipeId).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create production batch with inventory validation\n    async createProductionBatch (kitchenId, recipeId, plannedQuantity, startedBy, productionPlanId) {\n        const batchNumber = \"BATCH-\".concat(Date.now());\n        // Get recipe details with ingredients\n        const recipe = await this.getRecipeDetails(recipeId);\n        if (!recipe) throw new Error('Recipe not found');\n        // Get kitchen details to find associated warehouse\n        const { data: kitchen, error: kitchenError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select('*, warehouse:warehouses(*)').eq('id', kitchenId).single();\n        if (kitchenError || !kitchen) throw new Error('Kitchen not found');\n        // Check ingredient availability\n        const ingredientChecks = await Promise.all(recipe.recipe_ingredients.map(async (recipeIngredient)=>{\n            var _inventory_ingredient;\n            const requiredQuantity = recipeIngredient.quantity * plannedQuantity;\n            const { data: inventory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock, ingredient:ingredients(name)').eq('warehouse_id', kitchen.warehouse_id).eq('ingredient_id', recipeIngredient.ingredient_id).single();\n            return {\n                ingredient_id: recipeIngredient.ingredient_id,\n                ingredient_name: (inventory === null || inventory === void 0 ? void 0 : (_inventory_ingredient = inventory.ingredient) === null || _inventory_ingredient === void 0 ? void 0 : _inventory_ingredient.name) || 'Unknown',\n                required: requiredQuantity,\n                available: (inventory === null || inventory === void 0 ? void 0 : inventory.current_stock) || 0,\n                sufficient: ((inventory === null || inventory === void 0 ? void 0 : inventory.current_stock) || 0) >= requiredQuantity\n            };\n        }));\n        // Check if all ingredients are available\n        const insufficientIngredients = ingredientChecks.filter((check)=>!check.sufficient);\n        if (insufficientIngredients.length > 0) {\n            const errorMessage = \"Insufficient ingredients: \".concat(insufficientIngredients.map((ing)=>\"\".concat(ing.ingredient_name, \" (need \").concat(ing.required, \", have \").concat(ing.available, \")\")).join(', '));\n            throw new Error(errorMessage);\n        }\n        // Calculate estimated cost\n        const estimatedCost = recipe.recipe_ingredients.reduce((total, recipeIngredient)=>{\n            const ingredientCost = (recipeIngredient.ingredient.cost_per_unit || 0) * (recipeIngredient.quantity * plannedQuantity);\n            return total + ingredientCost;\n        }, 0);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').insert({\n            kitchen_id: kitchenId,\n            recipe_id: recipeId,\n            batch_number: batchNumber,\n            planned_quantity: plannedQuantity,\n            status: 'planned',\n            started_by: startedBy,\n            planned_start_time: new Date().toISOString(),\n            production_plan_id: productionPlanId,\n            estimated_cost: estimatedCost\n        }).select().single();\n        if (error) throw error;\n        // Create planned ingredient usage records\n        const plannedUsage = recipe.recipe_ingredients.map((recipeIngredient)=>({\n                batch_id: data.id,\n                ingredient_id: recipeIngredient.ingredient_id,\n                planned_quantity: recipeIngredient.quantity * plannedQuantity,\n                unit: recipeIngredient.unit,\n                cost_per_unit: recipeIngredient.ingredient.cost_per_unit || 0\n            }));\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('batch_ingredients_used').insert(plannedUsage);\n        return data;\n    },\n    // Get production batches\n    async getProductionBatches (kitchenId, status) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(*),\\n        kitchen:kitchens(*),\\n        starter:profiles!started_by(*),\\n        completer:profiles!completed_by(*)\\n      \");\n        if (kitchenId) {\n            query = query.eq('kitchen_id', kitchenId);\n        }\n        if (status) {\n            query = query.eq('status', status);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update batch status\n    async updateBatchStatus (batchId, status, userId, actualQuantity, qualityScore, qualityNotes) {\n        const updates = {\n            status,\n            updated_at: new Date().toISOString()\n        };\n        if (status === 'in_progress') {\n            updates.actual_start_time = new Date().toISOString();\n        } else if (status === 'completed') {\n            updates.actual_end_time = new Date().toISOString();\n            updates.completed_by = userId;\n            if (actualQuantity) updates.actual_quantity = actualQuantity;\n            if (qualityScore) updates.quality_score = qualityScore;\n            if (qualityNotes) updates.quality_notes = qualityNotes;\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update(updates).eq('id', batchId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Record ingredient usage for batch\n    async recordIngredientUsage (batchId, ingredientUsage) {\n        const usageRecords = ingredientUsage.map((usage)=>({\n                batch_id: batchId,\n                ingredient_id: usage.ingredient_id,\n                planned_quantity: usage.planned_quantity,\n                actual_quantity: usage.actual_quantity,\n                unit: usage.unit,\n                cost_per_unit: usage.cost_per_unit,\n                total_cost: usage.actual_quantity * usage.cost_per_unit\n            }));\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('batch_ingredients_used').insert(usageRecords);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Get kitchen production summary\n    async getKitchenProductionSummary (kitchenId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(name)\\n      \").eq('kitchen_id', kitchenId);\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Start production batch with inventory consumption\n    async startProductionBatch (batchId, userId) {\n        // Get batch details with ingredients\n        const { data: batch, error: batchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(*),\\n        kitchen:kitchens(warehouse_id),\\n        batch_ingredients_used(*)\\n      \").eq('id', batchId).single();\n        if (batchError || !batch) throw new Error('Production batch not found');\n        if (batch.status !== 'planned') {\n            throw new Error('Batch must be in planned status to start production');\n        }\n        // Update batch status to in_progress\n        const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update({\n            status: 'in_progress',\n            actual_start_time: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).eq('id', batchId);\n        if (updateError) throw updateError;\n        // Consume ingredients from inventory\n        const stockMovements = [];\n        for (const ingredientUsed of batch.batch_ingredients_used){\n            // Get current inventory\n            const { data: inventory, error: invError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', batch.kitchen.warehouse_id).eq('ingredient_id', ingredientUsed.ingredient_id).single();\n            if (invError || !inventory) {\n                throw new Error(\"Inventory not found for ingredient \".concat(ingredientUsed.ingredient_id));\n            }\n            // Check if sufficient stock\n            if (inventory.current_stock < ingredientUsed.planned_quantity) {\n                throw new Error(\"Insufficient stock for ingredient \".concat(ingredientUsed.ingredient_id));\n            }\n            // Update inventory\n            const newStock = inventory.current_stock - ingredientUsed.planned_quantity;\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                current_stock: newStock,\n                updated_at: new Date().toISOString()\n            }).eq('id', inventory.id);\n            // Record stock movement (traditional)\n            stockMovements.push({\n                warehouse_id: batch.kitchen.warehouse_id,\n                ingredient_id: ingredientUsed.ingredient_id,\n                movement_type: 'out',\n                quantity: ingredientUsed.planned_quantity,\n                unit: ingredientUsed.unit,\n                reference_type: 'production',\n                reference_id: batchId,\n                notes: \"Production consumption for batch \".concat(batch.batch_number),\n                performed_by: userId\n            });\n            // Record enhanced stock movement for raw ingredients\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('enhanced_stock_movements').insert({\n                warehouse_id: batch.kitchen.warehouse_id,\n                item_type: 'raw_ingredient',\n                item_id: ingredientUsed.ingredient_id,\n                movement_type: 'production_input',\n                quantity: ingredientUsed.planned_quantity,\n                unit: ingredientUsed.unit,\n                reference_type: 'production',\n                reference_id: batchId,\n                notes: \"Production consumption for batch \".concat(batch.batch_number),\n                performed_by: userId\n            });\n        }\n        // Insert stock movements\n        if (stockMovements.length > 0) {\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n        }\n        return {\n            success: true,\n            message: 'Production started successfully'\n        };\n    },\n    // Complete production batch with finished goods\n    async completeProductionBatch (batchId, userId, actualQuantity, qualityScore, qualityNotes, wasteData) {\n        const { data: batch, error: batchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(*),\\n        kitchen:kitchens(warehouse_id)\\n      \").eq('id', batchId).single();\n        if (batchError || !batch) throw new Error('Production batch not found');\n        if (batch.status !== 'in_progress') {\n            throw new Error('Batch must be in progress to complete');\n        }\n        // Calculate yield percentage\n        const yieldPercentage = actualQuantity / batch.planned_quantity * 100;\n        // Update batch status\n        const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update({\n            status: 'completed',\n            actual_quantity: actualQuantity,\n            actual_end_time: new Date().toISOString(),\n            completed_by: userId,\n            quality_score: qualityScore,\n            quality_notes: qualityNotes,\n            yield_percentage: yieldPercentage,\n            updated_at: new Date().toISOString()\n        }).eq('id', batchId);\n        if (updateError) throw updateError;\n        // Record waste if provided\n        if (wasteData && wasteData.length > 0) {\n            const wasteRecords = wasteData.map((waste)=>({\n                    batch_id: batchId,\n                    ingredient_id: waste.ingredient_id,\n                    waste_quantity: waste.waste_quantity,\n                    unit: 'kg',\n                    waste_type: waste.waste_type,\n                    waste_reason: waste.waste_reason,\n                    reported_by: userId\n                }));\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_waste').insert(wasteRecords);\n        }\n        // Add finished goods to inventory if recipe has a corresponding menu item\n        const { data: menuItem } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select('id, name').eq('recipe_id', batch.recipe_id).single();\n        if (menuItem) {\n            // Calculate cost per unit\n            const { data: productionCost } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_costs').select('total_cost').eq('batch_id', batchId).single();\n            const costPerUnit = productionCost ? productionCost.total_cost / actualQuantity : 0;\n            // Check if finished goods inventory exists\n            const { data: existingFinishedGoods } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('finished_goods_inventory').select('*').eq('warehouse_id', batch.kitchen.warehouse_id).eq('menu_item_id', menuItem.id).single();\n            if (existingFinishedGoods) {\n                // Update existing finished goods inventory\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('finished_goods_inventory').update({\n                    current_stock: existingFinishedGoods.current_stock + actualQuantity,\n                    cost_per_unit: costPerUnit,\n                    last_produced_at: new Date().toISOString(),\n                    production_batch_id: batchId,\n                    updated_at: new Date().toISOString()\n                }).eq('id', existingFinishedGoods.id);\n            } else {\n                // Create new finished goods inventory record\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('finished_goods_inventory').insert({\n                    warehouse_id: batch.kitchen.warehouse_id,\n                    menu_item_id: menuItem.id,\n                    current_stock: actualQuantity,\n                    minimum_stock: 10,\n                    maximum_stock: 100,\n                    reorder_point: 20,\n                    cost_per_unit: costPerUnit,\n                    last_produced_at: new Date().toISOString(),\n                    production_batch_id: batchId\n                });\n            }\n            // Record production output\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_finished_goods_output').insert({\n                production_batch_id: batchId,\n                menu_item_id: menuItem.id,\n                warehouse_id: batch.kitchen.warehouse_id,\n                quantity_produced: actualQuantity,\n                cost_per_unit: costPerUnit,\n                total_cost: (productionCost === null || productionCost === void 0 ? void 0 : productionCost.total_cost) || 0,\n                expiry_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours default\n            });\n            // Record enhanced stock movement for finished goods\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('enhanced_stock_movements').insert({\n                warehouse_id: batch.kitchen.warehouse_id,\n                item_type: 'finished_goods',\n                item_id: menuItem.id,\n                movement_type: 'production_output',\n                quantity: actualQuantity,\n                unit: 'portions',\n                reference_type: 'production',\n                reference_id: batchId,\n                cost_per_unit: costPerUnit,\n                total_cost: (productionCost === null || productionCost === void 0 ? void 0 : productionCost.total_cost) || 0,\n                notes: \"Production output for batch \".concat(batch.batch_number, \" - \").concat(menuItem.name),\n                performed_by: userId\n            });\n        }\n        return {\n            success: true,\n            message: 'Production completed successfully'\n        };\n    },\n    // Create production plan based on demand forecast\n    async createProductionPlan (kitchenId, recipeId, plannedDate, plannedQuantity, createdBy, demandForecast) {\n        // Get current stock level for the recipe/menu item\n        const { data: kitchen } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select('warehouse_id').eq('id', kitchenId).single();\n        let currentStockLevel = 0;\n        if (kitchen) {\n            const { data: inventory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock').eq('warehouse_id', kitchen.warehouse_id).eq('ingredient_id', recipeId).single();\n            currentStockLevel = (inventory === null || inventory === void 0 ? void 0 : inventory.current_stock) || 0;\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_plans').insert({\n            kitchen_id: kitchenId,\n            recipe_id: recipeId,\n            planned_date: plannedDate,\n            planned_quantity: plannedQuantity,\n            demand_forecast_quantity: demandForecast,\n            current_stock_level: currentStockLevel,\n            created_by: createdBy,\n            status: 'pending'\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Generate demand forecast based on historical sales\n    async generateDemandForecast (recipeId) {\n        let forecastDays = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 7;\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - forecastDays * 4) // Look back 4 weeks\n        ;\n        const { data: salesData, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').select(\"\\n        quantity,\\n        created_at,\\n        menu_item:menu_items!inner(recipe_id)\\n      \").eq('menu_item.recipe_id', recipeId).gte('created_at', startDate.toISOString());\n        if (error) throw error;\n        if (!salesData || salesData.length === 0) {\n            return {\n                averageDailyDemand: 0,\n                forecastedDemand: 0,\n                confidence: 'low',\n                historicalData: []\n            };\n        }\n        // Group by day and calculate daily totals\n        const dailyTotals = {};\n        salesData.forEach((item)=>{\n            const date = new Date(item.created_at).toISOString().split('T')[0];\n            dailyTotals[date] = (dailyTotals[date] || 0) + item.quantity;\n        });\n        const dailyValues = Object.values(dailyTotals);\n        const averageDailyDemand = dailyValues.reduce((sum, val)=>sum + val, 0) / dailyValues.length;\n        const forecastedDemand = Math.ceil(averageDailyDemand * forecastDays);\n        // Calculate confidence based on data consistency\n        const variance = dailyValues.reduce((sum, val)=>sum + Math.pow(val - averageDailyDemand, 2), 0) / dailyValues.length;\n        const standardDeviation = Math.sqrt(variance);\n        const coefficientOfVariation = standardDeviation / averageDailyDemand;\n        let confidence = 'high';\n        if (coefficientOfVariation > 0.5) confidence = 'low';\n        else if (coefficientOfVariation > 0.3) confidence = 'medium';\n        return {\n            averageDailyDemand: Math.round(averageDailyDemand * 100) / 100,\n            forecastedDemand,\n            confidence,\n            historicalData: dailyTotals,\n            dataPoints: dailyValues.length\n        };\n    },\n    // Get production plans\n    async getProductionPlans (kitchenId, status, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_plans').select(\"\\n        *,\\n        recipe:recipes(*),\\n        kitchen:kitchens(*),\\n        creator:profiles!created_by(*),\\n        approver:profiles!approved_by(*)\\n      \");\n        if (kitchenId) query = query.eq('kitchen_id', kitchenId);\n        if (status) query = query.eq('status', status);\n        if (startDate) query = query.gte('planned_date', startDate);\n        if (endDate) query = query.lte('planned_date', endDate);\n        const { data, error } = await query.order('planned_date', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Approve production plan\n    async approveProductionPlan (planId, approvedBy) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_plans').update({\n            status: 'approved',\n            approved_by: approvedBy,\n            updated_at: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Get production analytics\n    async getProductionAnalytics (kitchenId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(name, category),\\n        production_costs(*),\\n        production_waste(*)\\n      \");\n        if (kitchenId) query = query.eq('kitchen_id', kitchenId);\n        if (startDate) query = query.gte('created_at', startDate);\n        if (endDate) query = query.lte('created_at', endDate);\n        const { data: batches, error } = await query;\n        if (error) throw error;\n        // Calculate analytics\n        const totalBatches = batches.length;\n        const completedBatches = batches.filter((b)=>b.status === 'completed');\n        const averageYield = completedBatches.reduce((sum, b)=>sum + (b.yield_percentage || 0), 0) / completedBatches.length;\n        const averageQuality = completedBatches.reduce((sum, b)=>sum + (b.quality_score || 0), 0) / completedBatches.length;\n        const totalWaste = batches.reduce((sum, b)=>{\n            var _b_production_waste;\n            return sum + (((_b_production_waste = b.production_waste) === null || _b_production_waste === void 0 ? void 0 : _b_production_waste.reduce((wasteSum, w)=>wasteSum + w.waste_quantity, 0)) || 0);\n        }, 0);\n        return {\n            totalBatches,\n            completedBatches: completedBatches.length,\n            completionRate: completedBatches.length / totalBatches * 100,\n            averageYield: Math.round(averageYield * 100) / 100,\n            averageQuality: Math.round(averageQuality * 100) / 100,\n            totalWaste: Math.round(totalWaste * 100) / 100,\n            batchesByStatus: {\n                planned: batches.filter((b)=>b.status === 'planned').length,\n                in_progress: batches.filter((b)=>b.status === 'in_progress').length,\n                completed: batches.filter((b)=>b.status === 'completed').length,\n                cancelled: batches.filter((b)=>b.status === 'cancelled').length\n            }\n        };\n    },\n    // Quality Control Methods\n    async createQualityCheckpoint (batchId, checkpointName, checkpointType, status, score, notes, checkedBy) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('quality_checkpoints').insert({\n            batch_id: batchId,\n            checkpoint_name: checkpointName,\n            checkpoint_type: checkpointType,\n            status,\n            score,\n            notes,\n            checked_by: checkedBy\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getQualityCheckpoints (batchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('quality_checkpoints').select(\"\\n        *,\\n        checker:profiles!checked_by(full_name)\\n      \").eq('batch_id', batchId).order('checked_at', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Production Cost Tracking\n    async recordProductionCosts (batchId, ingredientCost) {\n        let laborCost = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, overheadCost = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 0;\n        // Get batch details to calculate cost per unit\n        const { data: batch } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select('actual_quantity, planned_quantity').eq('id', batchId).single();\n        const quantity = (batch === null || batch === void 0 ? void 0 : batch.actual_quantity) || (batch === null || batch === void 0 ? void 0 : batch.planned_quantity) || 1;\n        const totalCost = ingredientCost + laborCost + overheadCost;\n        const costPerUnit = totalCost / quantity;\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_costs').insert({\n            batch_id: batchId,\n            ingredient_cost: ingredientCost,\n            labor_cost: laborCost,\n            overhead_cost: overheadCost,\n            cost_per_unit: costPerUnit\n        }).select().single();\n        if (error) throw error;\n        // Update batch with actual cost\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update({\n            actual_cost: totalCost\n        }).eq('id', batchId);\n        return data;\n    },\n    async getProductionCosts (batchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_costs').select('*').eq('batch_id', batchId).single();\n        if (error) throw error;\n        return data;\n    },\n    // Waste Tracking\n    async recordProductionWaste (batchId, ingredientId, wasteQuantity, unit, wasteType, wasteReason, costImpact, reportedBy) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_waste').insert({\n            batch_id: batchId,\n            ingredient_id: ingredientId,\n            waste_quantity: wasteQuantity,\n            unit,\n            waste_type: wasteType,\n            waste_reason: wasteReason,\n            cost_impact: costImpact,\n            reported_by: reportedBy\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getProductionWaste (batchId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_waste').select(\"\\n        *,\\n        ingredient:ingredients(name, unit),\\n        batch:production_batches(batch_number, recipe:recipes(name)),\\n        reporter:profiles!reported_by(full_name)\\n      \");\n        if (batchId) query = query.eq('batch_id', batchId);\n        if (startDate) query = query.gte('created_at', startDate);\n        if (endDate) query = query.lte('created_at', endDate);\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Batch Production (Multiple recipes in one batch)\n    async createMultiRecipeBatch (kitchenId, recipes, startedBy, batchName) {\n        const batchNumber = batchName || \"MULTI-BATCH-\".concat(Date.now());\n        const createdBatches = [];\n        for (const recipe of recipes){\n            const batch = await this.createProductionBatch(kitchenId, recipe.recipe_id, recipe.planned_quantity, startedBy);\n            // Update batch number to group them\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update({\n                batch_number: \"\".concat(batchNumber, \"-\").concat(recipe.recipe_id.slice(-4)),\n                special_instructions: \"Part of multi-recipe batch: \".concat(batchNumber)\n            }).eq('id', batch.id);\n            createdBatches.push(batch);\n        }\n        return {\n            success: true,\n            batchNumber,\n            batches: createdBatches,\n            message: \"Created \".concat(createdBatches.length, \" production batches\")\n        };\n    },\n    // Production Efficiency Metrics\n    async getProductionEfficiencyMetrics (kitchenId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(prep_time_minutes, cook_time_minutes),\\n        production_costs(*)\\n      \").eq('status', 'completed');\n        if (kitchenId) query = query.eq('kitchen_id', kitchenId);\n        if (startDate) query = query.gte('created_at', startDate);\n        if (endDate) query = query.lte('created_at', endDate);\n        const { data: batches, error } = await query;\n        if (error) throw error;\n        if (!batches || batches.length === 0) {\n            return {\n                totalBatches: 0,\n                averageEfficiency: 0,\n                onTimeCompletion: 0,\n                costEfficiency: 0\n            };\n        }\n        // Calculate efficiency metrics\n        let totalEfficiency = 0;\n        let onTimeCount = 0;\n        let totalCostVariance = 0;\n        batches.forEach((batch)=>{\n            // Time efficiency\n            const plannedDuration = (batch.recipe.prep_time_minutes || 0) + (batch.recipe.cook_time_minutes || 0);\n            if (batch.actual_start_time && batch.actual_end_time && plannedDuration > 0) {\n                const actualDuration = (new Date(batch.actual_end_time).getTime() - new Date(batch.actual_start_time).getTime()) / (1000 * 60);\n                const efficiency = plannedDuration / actualDuration * 100;\n                totalEfficiency += Math.min(efficiency, 200) // Cap at 200% efficiency\n                ;\n                if (efficiency >= 90) onTimeCount++;\n            }\n            // Cost efficiency\n            if (batch.estimated_cost && batch.actual_cost) {\n                const costVariance = (batch.actual_cost - batch.estimated_cost) / batch.estimated_cost * 100;\n                totalCostVariance += Math.abs(costVariance);\n            }\n        });\n        return {\n            totalBatches: batches.length,\n            averageEfficiency: Math.round(totalEfficiency / batches.length * 100) / 100,\n            onTimeCompletion: Math.round(onTimeCount / batches.length * 100 * 100) / 100,\n            costEfficiency: Math.round((100 - totalCostVariance / batches.length) * 100) / 100\n        };\n    }\n};\n// Kitchen Management Functions\nconst kitchenService = {\n    // Get all kitchens\n    async getAllKitchens () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(\"\\n        *,\\n        branch:branches(*),\\n        warehouse:warehouses(*),\\n        head_chef:profiles(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get kitchen details\n    async getKitchenDetails (kitchenId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(\"\\n        *,\\n        branch:branches(*),\\n        warehouse:warehouses(*),\\n        head_chef:profiles(*)\\n      \").eq('id', kitchenId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Sales Management Functions\nconst salesService = {\n    // Get sales transactions\n    async getSalesTransactions (branchId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select(\"\\n        *,\\n        branch:branches(*),\\n        server:profiles!served_by(*),\\n        sales_transaction_items(\\n          *,\\n          menu_item:menu_items(*)\\n        )\\n      \");\n        if (branchId) {\n            query = query.eq('branch_id', branchId);\n        }\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get daily sales summary\n    async getDailySalesSummary (branchId, date) {\n        const targetDate = date || new Date().toISOString().split('T')[0];\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('daily_sales_summaries').select('*').eq('branch_id', branchId).eq('date', targetDate).single();\n        if (error && error.code !== 'PGRST116') throw error;\n        return data;\n    },\n    // Create sales transaction\n    async createSalesTransaction (branchId, items, customerInfo, paymentInfo, servedBy) {\n        const transactionNumber = \"TXN-\".concat(Date.now());\n        const totalAmount = items.reduce((sum, item)=>sum + item.quantity * item.unit_price, 0);\n        // Create transaction\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').insert({\n            branch_id: branchId,\n            transaction_number: transactionNumber,\n            customer_name: customerInfo.name,\n            customer_phone: customerInfo.phone,\n            total_amount: totalAmount,\n            tax_amount: paymentInfo.tax_amount || 0,\n            discount_amount: paymentInfo.discount_amount || 0,\n            payment_method: paymentInfo.method,\n            payment_status: 'completed',\n            served_by: servedBy\n        }).select().single();\n        if (transactionError) throw transactionError;\n        // Create transaction items\n        const transactionItems = items.map((item)=>({\n                transaction_id: transaction.id,\n                menu_item_id: item.menu_item_id,\n                quantity: item.quantity,\n                unit_price: item.unit_price,\n                total_price: item.quantity * item.unit_price,\n                special_instructions: item.special_instructions\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').insert(transactionItems);\n        if (itemsError) throw itemsError;\n        return transaction;\n    },\n    // Enhanced sales transaction with finished goods consumption\n    async createSalesTransactionWithFinishedGoods (branchId, items, customerInfo, paymentInfo, servedBy) {\n        const transactionNumber = \"TXN-\".concat(Date.now());\n        const totalAmount = items.reduce((sum, item)=>sum + item.quantity * item.price, 0);\n        // Create transaction\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').insert({\n            branch_id: branchId,\n            transaction_number: transactionNumber,\n            customer_name: customerInfo.name,\n            customer_phone: customerInfo.phone,\n            total_amount: totalAmount,\n            tax_amount: paymentInfo.tax_amount || 0,\n            discount_amount: paymentInfo.discount_amount || 0,\n            payment_method: paymentInfo.method,\n            payment_status: 'completed',\n            served_by: servedBy\n        }).select().single();\n        if (transactionError) throw transactionError;\n        // Create transaction items and update inventory\n        const transactionItems = [];\n        const stockMovements = [];\n        // Get branch warehouses\n        const { data: warehouses } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select('id').eq('branch_id', branchId);\n        const warehouseIds = (warehouses === null || warehouses === void 0 ? void 0 : warehouses.map((w)=>w.id)) || [];\n        for (const item of items){\n            // Create transaction item\n            transactionItems.push({\n                transaction_id: transaction.id,\n                menu_item_id: item.menu_item_id,\n                quantity: item.quantity,\n                unit_price: item.price,\n                total_price: item.quantity * item.price,\n                special_instructions: item.special_instructions\n            });\n            // Try to consume from finished goods first\n            let remainingQuantity = item.quantity;\n            for (const warehouseId of warehouseIds){\n                if (remainingQuantity <= 0) break;\n                const { data: finishedGoods } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('finished_goods_inventory').select('*').eq('warehouse_id', warehouseId).eq('menu_item_id', item.menu_item_id).single();\n                if (finishedGoods && finishedGoods.current_stock > 0) {\n                    const consumeQuantity = Math.min(remainingQuantity, finishedGoods.current_stock);\n                    // Update finished goods inventory\n                    await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('finished_goods_inventory').update({\n                        current_stock: finishedGoods.current_stock - consumeQuantity,\n                        updated_at: new Date().toISOString()\n                    }).eq('id', finishedGoods.id);\n                    // Record enhanced stock movement for finished goods\n                    await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('enhanced_stock_movements').insert({\n                        warehouse_id: warehouseId,\n                        item_type: 'finished_goods',\n                        item_id: item.menu_item_id,\n                        movement_type: 'out',\n                        quantity: consumeQuantity,\n                        unit: 'portions',\n                        reference_type: 'sale',\n                        reference_id: transaction.id,\n                        notes: \"Sale consumption - finished goods\",\n                        performed_by: servedBy\n                    });\n                    remainingQuantity -= consumeQuantity;\n                }\n            }\n            // If we still have remaining quantity, consume raw ingredients (fallback)\n            if (remainingQuantity > 0) {\n                var _menuItem_recipe;\n                // Get menu item recipe to update ingredient inventory\n                const { data: menuItem } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select(\"\\n            *,\\n            recipe:recipes(\\n              recipe_ingredients(\\n                *,\\n                ingredient:ingredients(*)\\n              )\\n            )\\n          \").eq('id', item.menu_item_id).single();\n                if (menuItem === null || menuItem === void 0 ? void 0 : (_menuItem_recipe = menuItem.recipe) === null || _menuItem_recipe === void 0 ? void 0 : _menuItem_recipe.recipe_ingredients) {\n                    // Update inventory for each ingredient used (for remaining quantity)\n                    for (const recipeIngredient of menuItem.recipe.recipe_ingredients){\n                        const consumedQuantity = recipeIngredient.quantity * remainingQuantity;\n                        // Find inventory in branch warehouses\n                        for (const warehouseId of warehouseIds){\n                            const { data: inventory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', warehouseId).eq('ingredient_id', recipeIngredient.ingredient_id).single();\n                            if (inventory && inventory.current_stock >= consumedQuantity) {\n                                // Update inventory\n                                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                                    current_stock: inventory.current_stock - consumedQuantity,\n                                    updated_at: new Date().toISOString()\n                                }).eq('id', inventory.id);\n                                // Record traditional stock movement\n                                stockMovements.push({\n                                    warehouse_id: warehouseId,\n                                    ingredient_id: recipeIngredient.ingredient_id,\n                                    movement_type: 'out',\n                                    quantity: consumedQuantity,\n                                    unit: recipeIngredient.unit,\n                                    reference_type: 'sale',\n                                    reference_id: transaction.id,\n                                    notes: \"Sale consumption - raw ingredients (no finished goods available)\",\n                                    performed_by: servedBy\n                                });\n                                // Record enhanced stock movement for raw ingredients\n                                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('enhanced_stock_movements').insert({\n                                    warehouse_id: warehouseId,\n                                    item_type: 'raw_ingredient',\n                                    item_id: recipeIngredient.ingredient_id,\n                                    movement_type: 'out',\n                                    quantity: consumedQuantity,\n                                    unit: recipeIngredient.unit,\n                                    reference_type: 'sale',\n                                    reference_id: transaction.id,\n                                    notes: \"Sale consumption - raw ingredients (no finished goods available)\",\n                                    performed_by: servedBy\n                                });\n                                break; // Found inventory, no need to check other warehouses\n                            }\n                        }\n                    }\n                }\n                // Log that we had to use raw ingredients (could trigger production alert)\n                console.log(\"Used raw ingredients for \".concat(remainingQuantity, \" units of menu item \").concat(item.menu_item_id, \" - consider increasing production\"));\n            }\n        }\n        // Insert transaction items\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').insert(transactionItems);\n        // Insert stock movements\n        if (stockMovements.length > 0) {\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n        }\n        return transaction;\n    },\n    // Generate automatic production recommendations based on sales trends\n    async generateProductionRecommendations (branchId) {\n        let forecastDays = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 7;\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - 30) // Look back 30 days\n        ;\n        // Get sales data for the branch\n        const { data: salesData, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').select(\"\\n        quantity,\\n        created_at,\\n        menu_item:menu_items(\\n          *,\\n          recipe:recipes(*)\\n        ),\\n        sales_transactions!inner(branch_id)\\n      \").eq('sales_transactions.branch_id', branchId).gte('created_at', startDate.toISOString());\n        if (error) throw error;\n        // Group by recipe and calculate trends\n        const recipeStats = {};\n        salesData === null || salesData === void 0 ? void 0 : salesData.forEach((item)=>{\n            var _item_menu_item;\n            if ((_item_menu_item = item.menu_item) === null || _item_menu_item === void 0 ? void 0 : _item_menu_item.recipe) {\n                const recipeId = item.menu_item.recipe.id;\n                if (!recipeStats[recipeId]) {\n                    recipeStats[recipeId] = {\n                        recipe: item.menu_item.recipe,\n                        menuItem: item.menu_item,\n                        totalSold: 0,\n                        dailySales: {},\n                        trend: 'stable'\n                    };\n                }\n                recipeStats[recipeId].totalSold += item.quantity;\n                const date = new Date(item.created_at).toISOString().split('T')[0];\n                recipeStats[recipeId].dailySales[date] = (recipeStats[recipeId].dailySales[date] || 0) + item.quantity;\n            }\n        });\n        // Calculate recommendations\n        const recommendations = [];\n        for (const [recipeId, stats] of Object.entries(recipeStats)){\n            const dailyValues = Object.values(stats.dailySales);\n            const averageDailySales = dailyValues.reduce((sum, val)=>sum + val, 0) / dailyValues.length;\n            const forecastedDemand = Math.ceil(averageDailySales * forecastDays);\n            // Get current stock level\n            const { data: warehouses } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select('id').eq('branch_id', branchId);\n            let currentStock = 0;\n            if (warehouses) {\n                for (const warehouse of warehouses){\n                    const { data: inventory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock').eq('warehouse_id', warehouse.id).eq('ingredient_id', recipeId).single();\n                    currentStock += (inventory === null || inventory === void 0 ? void 0 : inventory.current_stock) || 0;\n                }\n            }\n            const recommendedProduction = Math.max(0, forecastedDemand - currentStock);\n            if (recommendedProduction > 0) {\n                recommendations.push({\n                    recipeId,\n                    recipeName: stats.recipe.name,\n                    menuItemName: stats.menuItem.name,\n                    currentStock,\n                    averageDailySales: Math.round(averageDailySales * 100) / 100,\n                    forecastedDemand,\n                    recommendedProduction,\n                    priority: recommendedProduction > averageDailySales * 2 ? 'high' : 'medium',\n                    reason: currentStock < forecastedDemand ? 'low_stock' : 'demand_forecast'\n                });\n            }\n        }\n        // Sort by priority and recommended quantity\n        recommendations.sort((a, b)=>{\n            if (a.priority === 'high' && b.priority !== 'high') return -1;\n            if (b.priority === 'high' && a.priority !== 'high') return 1;\n            return b.recommendedProduction - a.recommendedProduction;\n        });\n        return {\n            recommendations,\n            generatedAt: new Date().toISOString(),\n            forecastPeriod: forecastDays,\n            totalRecommendations: recommendations.length\n        };\n    }\n};\n// Menu Management Functions\nconst menuService = {\n    // Get menu items\n    async getMenuItems () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select(\"\\n        *,\\n        recipe:recipes(*)\\n      \").eq('is_available', true).order('category', {\n            ascending: true\n        }).order('name', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get branch menu with pricing\n    // async getBranchMenu(branchId: string) {\n    //   const { data, error } = await supabase\n    //     .from('branch_menu_pricing')\n    //     .select(`\n    //       *,\n    //       menu_item:menu_items(\n    //         *,\n    //         recipe:recipes(*)\n    //       )\n    //     `)\n    //     .eq('branch_id', branchId)\n    //     .eq('is_available', true)\n    //     .order('menu_item.category', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    async getBranchMenu (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').select(\"\\n        *,\\n        menu_item:menu_items(\\n          *,\\n          recipe:recipes(*)\\n        )\\n      \").eq('branch_id', branchId).eq('is_available', true).order('menu_item(category)', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item availability\n    async updateMenuItemAvailability (branchId, menuItemId, isAvailable) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').update({\n            is_available: isAvailable\n        }).eq('branch_id', branchId).eq('menu_item_id', menuItemId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    }\n};\n// // Analytics and Reporting Functions\n// export const analyticsService = {\n//   // Get branch performance metrics\n//   async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     // Get sales data\n//     const { data: salesData, error: salesError } = await supabase\n//       .from('sales_transactions')\n//       .select('total_amount, created_at, payment_method')\n//       .eq('branch_id', branchId)\n//       .gte('created_at', start)\n//       .lte('created_at', end)\n//     if (salesError) throw salesError\n//     // Get inventory data\n//     const { data: inventoryData, error: inventoryError } = await supabase\n//       .from('inventory')\n//       .select(`\n//         current_stock,\n//         minimum_stock,\n//         ingredient:ingredients(cost_per_unit)\n//       `)\n//       .in('warehouse_id',\n//         supabase\n//           .from('warehouses')\n//           .select('id')\n//           .eq('branch_id', branchId)\n//       )\n//     if (inventoryError) throw inventoryError\n//     // Calculate metrics\n//     const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)\n//     const totalTransactions = salesData.length\n//     const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0\n//     const inventoryValue = inventoryData.reduce((sum, item) =>\n//       sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0\n//     )\n//     const lowStockItems = inventoryData.filter(item =>\n//       item.current_stock <= item.minimum_stock\n//     ).length\n//     return {\n//       totalRevenue,\n//       totalTransactions,\n//       averageTransaction,\n//       inventoryValue,\n//       lowStockItems,\n//       salesData,\n//       inventoryData\n//     }\n//   },\n//   // Get top selling items\n//   async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     const { data, error } = await supabase\n//       .from('sales_transaction_items')\n//       .select(`\n//         quantity,\n//         total_price,\n//         menu_item:menu_items(name, category),\n//         transaction:sales_transactions!inner(created_at, branch_id)\n//       `)\n//       .eq('transaction.branch_id', branchId)\n//       .gte('transaction.created_at', start)\n//       .lte('transaction.created_at', end)\n//     if (error) throw error\n//     // Aggregate by menu item\n//     const itemStats = data.reduce((acc: any, item) => {\n//       const itemName = item.menu_item.name\n//       if (!acc[itemName]) {\n//         acc[itemName] = {\n//           name: itemName,\n//           category: item.menu_item.category,\n//           totalQuantity: 0,\n//           totalRevenue: 0\n//         }\n//       }\n//       acc[itemName].totalQuantity += item.quantity\n//       acc[itemName].totalRevenue += item.total_price\n//       return acc\n//     }, {})\n//     return Object.values(itemStats)\n//       .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)\n//       .slice(0, limit)\n//   }\n// }\n// Analytics and Reporting Functions\nconst analyticsService = {\n    // Get branch performance metrics\n    async getBranchPerformance (branchId, startDate, endDate) {\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Get sales data\n        const { data: salesData, error: salesError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select('total_amount, created_at, payment_method').eq('branch_id', branchId).gte('created_at', start).lte('created_at', end);\n        if (salesError) throw salesError;\n        // First get warehouse IDs for the branch\n        const { data: warehouseIds, error: warehouseError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select('id').eq('branch_id', branchId);\n        if (warehouseError) throw warehouseError;\n        // Get inventory data using warehouse IDs\n        const warehouseIdList = warehouseIds.map((w)=>w.id);\n        const { data: inventoryData, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        current_stock,\\n        minimum_stock,\\n        ingredient:ingredients(cost_per_unit)\\n      \").in('warehouse_id', warehouseIdList);\n        if (inventoryError) throw inventoryError;\n        // Calculate metrics\n        const totalRevenue = salesData.reduce((sum, sale)=>sum + sale.total_amount, 0);\n        const totalTransactions = salesData.length;\n        const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;\n        const inventoryValue = inventoryData.reduce((sum, item)=>{\n            var _item_ingredient;\n            return sum + item.current_stock * (((_item_ingredient = item.ingredient) === null || _item_ingredient === void 0 ? void 0 : _item_ingredient.cost_per_unit) || 0);\n        }, 0);\n        const lowStockItems = inventoryData.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        return {\n            totalRevenue,\n            totalTransactions,\n            averageTransaction,\n            inventoryValue,\n            lowStockItems,\n            salesData,\n            inventoryData\n        };\n    },\n    // Get top selling items\n    async getTopSellingItems (branchId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, startDate = arguments.length > 2 ? arguments[2] : void 0, endDate = arguments.length > 3 ? arguments[3] : void 0;\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').select(\"\\n        quantity,\\n        total_price,\\n        menu_item:menu_items(name, category),\\n        sales_transactions!inner(created_at, branch_id)\\n      \").eq('sales_transactions.branch_id', branchId).gte('sales_transactions.created_at', start).lte('sales_transactions.created_at', end);\n        if (error) throw error;\n        // Aggregate by menu item\n        const itemStats = data.reduce((acc, item)=>{\n            const itemName = item.menu_item.name;\n            if (!acc[itemName]) {\n                acc[itemName] = {\n                    name: itemName,\n                    category: item.menu_item.category,\n                    totalQuantity: 0,\n                    totalRevenue: 0\n                };\n            }\n            acc[itemName].totalQuantity += item.quantity;\n            acc[itemName].totalRevenue += item.total_price;\n            return acc;\n        }, {});\n        return Object.values(itemStats).sort((a, b)=>b.totalQuantity - a.totalQuantity).slice(0, limit);\n    },\n    // Alternative approach for top selling items using RPC if the above doesn't work\n    async getTopSellingItemsRPC (branchId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, startDate = arguments.length > 2 ? arguments[2] : void 0, endDate = arguments.length > 3 ? arguments[3] : void 0;\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Call a stored procedure/function for complex aggregation\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('get_top_selling_items', {\n            branch_id: branchId,\n            start_date: start,\n            end_date: end,\n            item_limit: limit\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/database.ts\n"));

/***/ })

});