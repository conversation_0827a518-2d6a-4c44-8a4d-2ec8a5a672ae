import { g as globalApis } from './constants.DnKduX2e.js';
import { V as VitestIndex } from './index.CdQS2e2Q.js';
import './vi.bdSIJ99Y.js';
import '@vitest/expect';
import '@vitest/runner';
import '@vitest/runner/utils';
import 'chai';
import './utils.XdZDrNZV.js';
import '@vitest/utils';
import './_commonjsHelpers.BFTU3MAI.js';
import '@vitest/snapshot';
import '@vitest/utils/error';
import '@vitest/spy';
import '@vitest/utils/source-map';
import './date.Bq6ZW5rf.js';
import './benchmark.CYdenmiT.js';
import 'expect-type';

function registerApiGlobally() {
	globalApis.forEach((api) => {
		// @ts-expect-error I know what I am doing :P
		globalThis[api] = VitestIndex[api];
	});
}

export { registerApiGlobally };
