'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { productionService, kitchenService, salesService } from '@/lib/database'
import ProductionPlanningModal from '@/components/production/ProductionPlanningModal'
import QualityControlModal from '@/components/production/QualityControlModal'
import ProductionAnalytics from '@/components/production/ProductionAnalytics'

interface Recipe {
  id: string
  name: string
  code: string
  description: string
  category: string
  serving_size: number
  prep_time_minutes: number
  cook_time_minutes: number
  difficulty_level: number
  recipe_ingredients: {
    id: string
    quantity: number
    unit: string
    ingredient: {
      id: string
      name: string
      code: string
      unit: string
    }
  }[]
}

interface ProductionBatch {
  id: string
  batch_number: string
  planned_quantity: number
  actual_quantity: number
  status: string
  quality_score: number
  quality_notes: string
  planned_start_time: string
  actual_start_time: string
  actual_end_time: string
  recipe: {
    id: string
    name: string
    code: string
  }
  kitchen: {
    id: string
    name: string
    code: string
  }
  starter: {
    full_name: string
  }
}

interface Kitchen {
  id: string
  name: string
  code: string
  type: string
  capacity: number
}

export default function ProductionPage() {
  const { profile } = useAuth()
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [batches, setBatches] = useState<ProductionBatch[]>([])
  const [kitchens, setKitchens] = useState<Kitchen[]>([])
  const [selectedKitchen, setSelectedKitchen] = useState<string>('')
  const [selectedRecipe, setSelectedRecipe] = useState<string>('')
  const [plannedQuantity, setPlannedQuantity] = useState<number>(1)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showPlanningModal, setShowPlanningModal] = useState(false)
  const [showQualityModal, setShowQualityModal] = useState(false)
  const [selectedBatch, setSelectedBatch] = useState<ProductionBatch | null>(null)
  const [activeTab, setActiveTab] = useState<'batches' | 'analytics'>('batches')

  useEffect(() => {
    loadInitialData()
  }, [])

  useEffect(() => {
    if (selectedKitchen) {
      loadProductionBatches(selectedKitchen)
    }
  }, [selectedKitchen])

  const loadInitialData = async () => {
    try {
      setLoading(true)
      const [recipesData, kitchensData] = await Promise.all([
        productionService.getAllRecipes(),
        kitchenService.getAllKitchens()
      ])
      
      setRecipes(recipesData)
      setKitchens(kitchensData)
      
      if (kitchensData.length > 0) {
        setSelectedKitchen(kitchensData[0].id)
      }
    } catch (err) {
      setError('Failed to load data')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const loadProductionBatches = async (kitchenId: string) => {
    try {
      const data = await productionService.getProductionBatches(kitchenId)
      setBatches(data)
    } catch (err) {
      console.error('Failed to load production batches:', err)
    }
  }

  const createProductionBatch = async () => {
    if (!selectedRecipe || !selectedKitchen || !profile?.id) {
      setError('Please select recipe and kitchen')
      return
    }

    try {
      await productionService.createProductionBatch(
        selectedKitchen,
        selectedRecipe,
        plannedQuantity,
        profile.id
      )
      
      setShowCreateForm(false)
      setSelectedRecipe('')
      setPlannedQuantity(1)
      loadProductionBatches(selectedKitchen)
    } catch (err) {
      setError('Failed to create production batch')
      console.error(err)
    }
  }

  const updateBatchStatus = async (batchId: string, newStatus: string) => {
    try {
      await productionService.updateBatchStatus(batchId, newStatus, profile?.id)
      loadProductionBatches(selectedKitchen)
    } catch (err) {
      setError('Failed to update batch status')
      console.error(err)
    }
  }

  const startProduction = async (batchId: string) => {
    try {
      await productionService.startProductionBatch(batchId, profile?.id || '')
      loadProductionBatches(selectedKitchen)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start production')
      console.error(err)
    }
  }

  const openQualityControl = (batch: ProductionBatch) => {
    setSelectedBatch(batch)
    setShowQualityModal(true)
  }

  const handleQualityUpdated = () => {
    loadProductionBatches(selectedKitchen)
  }

  const handlePlanCreated = () => {
    // Refresh data when a new plan is created
    loadProductionBatches(selectedKitchen)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyStars = (level: number) => {
    return '★'.repeat(level) + '☆'.repeat(5 - level)
  }

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'manager', 'staff']}>
        <DashboardLayout>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'manager', 'staff']}>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Production Management</h1>
            <div className="flex space-x-4">
              <select
                value={selectedKitchen}
                onChange={(e) => setSelectedKitchen(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="">Select Kitchen</option>
                {kitchens.map((kitchen) => (
                  <option key={kitchen.id} value={kitchen.id}>
                    {kitchen.name} ({kitchen.code})
                  </option>
                ))}
              </select>
              <button
                onClick={() => setShowPlanningModal(true)}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                Create Plan
              </button>
              <button
                onClick={() => setShowCreateForm(true)}
                className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
              >
                Start Production
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('batches')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'batches'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Production Batches
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'analytics'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Analytics & Insights
              </button>
            </nav>
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          {/* Production Stats */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">📋</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total Batches
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {batches.length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">🔄</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        In Progress
                      </dt>
                      <dd className="text-lg font-medium text-yellow-600">
                        {batches.filter(b => b.status === 'in_progress').length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">✅</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Completed Today
                      </dt>
                      <dd className="text-lg font-medium text-green-600">
                        {batches.filter(b => 
                          b.status === 'completed' && 
                          new Date(b.actual_end_time).toDateString() === new Date().toDateString()
                        ).length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">⭐</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Avg Quality Score
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {batches.filter(b => b.quality_score).length > 0
                          ? (batches.reduce((sum, b) => sum + (b.quality_score || 0), 0) / 
                             batches.filter(b => b.quality_score).length).toFixed(1)
                          : 'N/A'
                        }
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Create Production Form */}
          {showCreateForm && (
            <div className="bg-white shadow overflow-hidden sm:rounded-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Start New Production Batch</h3>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Recipe</label>
                  <select
                    value={selectedRecipe}
                    onChange={(e) => setSelectedRecipe(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="">Select Recipe</option>
                    {recipes.map((recipe) => (
                      <option key={recipe.id} value={recipe.id}>
                        {recipe.name} - {getDifficultyStars(recipe.difficulty_level)}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Planned Quantity</label>
                  <input
                    type="number"
                    value={plannedQuantity}
                    onChange={(e) => setPlannedQuantity(parseInt(e.target.value))}
                    min="1"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div className="flex items-end space-x-2">
                  <button
                    onClick={createProductionBatch}
                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                  >
                    Create Batch
                  </button>
                  <button
                    onClick={() => setShowCreateForm(false)}
                    className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Tab Content */}
          {activeTab === 'batches' && (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Production Batches
                  {selectedKitchen && kitchens.find(k => k.id === selectedKitchen) && (
                    <span className="text-sm text-gray-500 ml-2">
                      - {kitchens.find(k => k.id === selectedKitchen)?.name}
                    </span>
                  )}
                </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Batch #
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Recipe
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quality
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Started By
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {batches.map((batch) => (
                    <tr key={batch.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {batch.batch_number}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {batch.recipe.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {batch.recipe.code}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {batch.actual_quantity || batch.planned_quantity} / {batch.planned_quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(batch.status)}`}>
                          {batch.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {batch.quality_score ? `${batch.quality_score}/10` : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {batch.starter?.full_name || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {batch.status === 'planned' && (
                            <button
                              onClick={() => startProduction(batch.id)}
                              className="text-green-600 hover:text-green-900"
                              title="Start Production"
                            >
                              ▶️ Start
                            </button>
                          )}
                          {batch.status === 'in_progress' && (
                            <>
                              <button
                                onClick={() => openQualityControl(batch)}
                                className="text-yellow-600 hover:text-yellow-900"
                                title="Quality Control"
                              >
                                🔍 QC
                              </button>
                              <button
                                onClick={() => updateBatchStatus(batch.id, 'completed')}
                                className="text-blue-600 hover:text-blue-900"
                                title="Complete Batch"
                              >
                                ✅ Complete
                              </button>
                            </>
                          )}
                          {batch.status === 'completed' && (
                            <button
                              onClick={() => openQualityControl(batch)}
                              className="text-indigo-600 hover:text-indigo-900"
                              title="View Quality Report"
                            >
                              📊 Report
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          )}

          {/* Analytics Tab */}
          {activeTab === 'analytics' && (
            <ProductionAnalytics
              kitchenId={selectedKitchen}
              branchId={profile?.branch_id}
            />
          )}

          {/* Modals */}
          <ProductionPlanningModal
            isOpen={showPlanningModal}
            onClose={() => setShowPlanningModal(false)}
            onPlanCreated={handlePlanCreated}
            selectedKitchen={selectedKitchen}
          />

          <QualityControlModal
            isOpen={showQualityModal}
            onClose={() => setShowQualityModal(false)}
            batch={selectedBatch}
            onQualityUpdated={handleQualityUpdated}
          />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}
