/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/pos/page";
exports.ids = ["app/pos/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpos%2Fpage&page=%2Fpos%2Fpage&appPaths=%2Fpos%2Fpage&pagePath=private-next-app-dir%2Fpos%2Fpage.tsx&appDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpos%2Fpage&page=%2Fpos%2Fpage&appPaths=%2Fpos%2Fpage&pagePath=private-next-app-dir%2Fpos%2Fpage.tsx&appDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/pos/page.tsx */ \"(rsc)/./src/app/pos/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'pos',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/pos/page\",\n        pathname: \"/pos\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpos%2Fpage&page=%2Fpos%2Fpage&appPaths=%2Fpos%2Fpage&pagePath=private-next-app-dir%2Fpos%2Fpage.tsx&appDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cpos%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cpos%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/pos/page.tsx */ \"(rsc)/./src/app/pos/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwYWRhbmdodWJfc3VwYWJhc2UlNUMlNUNwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwb3MlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0pBQThHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxwYWRhbmdodWJfc3VwYWJhc2VcXFxccGFkYW5nLXJlc3RhdXJhbnQtc3lzdGVtXFxcXHNyY1xcXFxhcHBcXFxccG9zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cpos%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxwYWRhbmdodWJfc3VwYWJhc2VcXHBhZGFuZy1yZXN0YXVyYW50LXN5c3RlbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccGFkYW5naHViX3N1cGFiYXNlXFxwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Padang Restaurant System\",\n    description: \"Comprehensive restaurant management system for Padang restaurants\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFLTUE7QUFLQUM7QUFSaUI7QUFDK0I7QUFZL0MsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFDQ0MsV0FBVyxHQUFHWCwyTEFBa0IsQ0FBQyxDQUFDLEVBQUVDLGdNQUFrQixDQUFDLFlBQVksQ0FBQztzQkFFcEUsNEVBQUNDLCtEQUFZQTswQkFDVks7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsiRDpcXHBhZGFuZ2h1Yl9zdXBhYmFzZVxccGFkYW5nLXJlc3RhdXJhbnQtc3lzdGVtXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCJAL2NvbnRleHRzL0F1dGhDb250ZXh0XCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIlBhZGFuZyBSZXN0YXVyYW50IFN5c3RlbVwiLFxuICBkZXNjcmlwdGlvbjogXCJDb21wcmVoZW5zaXZlIHJlc3RhdXJhbnQgbWFuYWdlbWVudCBzeXN0ZW0gZm9yIFBhZGFuZyByZXN0YXVyYW50c1wiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5XG4gICAgICAgIGNsYXNzTmFtZT17YCR7Z2Vpc3RTYW5zLnZhcmlhYmxlfSAke2dlaXN0TW9uby52YXJpYWJsZX0gYW50aWFsaWFzZWRgfVxuICAgICAgPlxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImdlaXN0U2FucyIsImdlaXN0TW9ubyIsIkF1dGhQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\padanghub_supabase\\padang-restaurant-system\\src\\app\\pos\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\padanghub_supabase\\padang-restaurant-system\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\padanghub_supabase\\padang-restaurant-system\\src\\contexts\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwYWRhbmdodWJfc3VwYWJhc2UlNUMlNUNwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkdlaXN0JTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3Qtc2FucyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0U2FucyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDcGFkYW5naHViX3N1cGFiYXNlJTVDJTVDcGFkYW5nLXJlc3RhdXJhbnQtc3lzdGVtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJHZWlzdF9Nb25vJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3QtbW9ubyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0TW9ubyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDcGFkYW5naHViX3N1cGFiYXNlJTVDJTVDcGFkYW5nLXJlc3RhdXJhbnQtc3lzdGVtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q3BhZGFuZ2h1Yl9zdXBhYmFzZSU1QyU1Q3BhZGFuZy1yZXN0YXVyYW50LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBdUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkQ6XFxcXHBhZGFuZ2h1Yl9zdXBhYmFzZVxcXFxwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW1cXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cpos%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cpos%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/pos/page.tsx */ \"(ssr)/./src/app/pos/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwYWRhbmdodWJfc3VwYWJhc2UlNUMlNUNwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwb3MlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0pBQThHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxwYWRhbmdodWJfc3VwYWJhc2VcXFxccGFkYW5nLXJlc3RhdXJhbnQtc3lzdGVtXFxcXHNyY1xcXFxhcHBcXFxccG9zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cpos%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ POSPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/database */ \"(ssr)/./src/lib/database.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction POSPage() {\n    const { profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [menuItems, setMenuItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [branches, setBranches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedBranch, setSelectedBranch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerPhone, setCustomerPhone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [paymentMethod, setPaymentMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('cash');\n    const [discountAmount, setDiscountAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [processingOrder, setProcessingOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"POSPage.useEffect\": ()=>{\n            loadInitialData();\n        }\n    }[\"POSPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"POSPage.useEffect\": ()=>{\n            if (selectedBranch) {\n                loadBranchMenu(selectedBranch);\n            }\n        }\n    }[\"POSPage.useEffect\"], [\n        selectedBranch\n    ]);\n    const loadInitialData = async ()=>{\n        try {\n            setLoading(true);\n            const branchesData = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.branchService.getAllBranches();\n            setBranches(branchesData);\n            // Set default branch based on user's branch or first available\n            const defaultBranch = profile?.branch_id || (branchesData.length > 0 ? branchesData[0].id : '');\n            setSelectedBranch(defaultBranch);\n        } catch (err) {\n            setError('Failed to load data');\n            console.error(err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadBranchMenu = async (branchId)=>{\n        try {\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.menuService.getBranchMenu(branchId);\n            setMenuItems(data);\n        } catch (err) {\n            setError('Failed to load menu');\n            console.error(err);\n        }\n    };\n    const addToCart = (menuItem)=>{\n        const existingItem = cart.find((item)=>item.menu_item_id === menuItem.menu_item.id);\n        if (existingItem) {\n            setCart(cart.map((item)=>item.menu_item_id === menuItem.menu_item.id ? {\n                    ...item,\n                    quantity: item.quantity + 1\n                } : item));\n        } else {\n            setCart([\n                ...cart,\n                {\n                    menu_item_id: menuItem.menu_item.id,\n                    name: menuItem.menu_item.name,\n                    price: menuItem.price,\n                    quantity: 1,\n                    special_instructions: ''\n                }\n            ]);\n        }\n    };\n    const updateCartItemQuantity = (menuItemId, quantity)=>{\n        if (quantity <= 0) {\n            setCart(cart.filter((item)=>item.menu_item_id !== menuItemId));\n        } else {\n            setCart(cart.map((item)=>item.menu_item_id === menuItemId ? {\n                    ...item,\n                    quantity\n                } : item));\n        }\n    };\n    const updateSpecialInstructions = (menuItemId, instructions)=>{\n        setCart(cart.map((item)=>item.menu_item_id === menuItemId ? {\n                ...item,\n                special_instructions: instructions\n            } : item));\n    };\n    const calculateSubtotal = ()=>{\n        return cart.reduce((total, item)=>total + item.price * item.quantity, 0);\n    };\n    const calculateTax = ()=>{\n        return calculateSubtotal() * 0.1 // 10% tax\n        ;\n    };\n    const calculateTotal = ()=>{\n        return calculateSubtotal() + calculateTax() - discountAmount;\n    };\n    const processOrder = async ()=>{\n        if (!selectedBranch || cart.length === 0 || !profile?.id) {\n            setError('Please select items and ensure all required fields are filled');\n            return;\n        }\n        try {\n            setProcessingOrder(true);\n            const customerInfo = {\n                name: customerName,\n                phone: customerPhone\n            };\n            const paymentInfo = {\n                method: paymentMethod,\n                tax_amount: calculateTax(),\n                discount_amount: discountAmount\n            };\n            await _lib_database__WEBPACK_IMPORTED_MODULE_5__.salesService.createSalesTransaction(selectedBranch, cart, customerInfo, paymentInfo, profile.id);\n            // Clear cart and customer info\n            setCart([]);\n            setCustomerName('');\n            setCustomerPhone('');\n            setDiscountAmount(0);\n            setError(null);\n            alert('Order processed successfully!');\n        } catch (err) {\n            setError('Failed to process order');\n            console.error(err);\n        } finally{\n            setProcessingOrder(false);\n        }\n    };\n    const categories = [\n        'all',\n        ...new Set(menuItems.map((item)=>item.menu_item.category))\n    ];\n    const filteredMenuItems = selectedCategory === 'all' ? menuItems : menuItems.filter((item)=>item.menu_item.category === selectedCategory);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('id-ID', {\n            style: 'currency',\n            currency: 'IDR',\n            minimumFractionDigits: 0\n        }).format(amount);\n    };\n    const getSpiceLevel = (level)=>{\n        return '🌶️'.repeat(level);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            allowedRoles: [\n                'admin',\n                'manager',\n                'cashier'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        allowedRoles: [\n            'admin',\n            'manager',\n            'cashier'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen bg-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Point of Sale\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedBranch,\n                                                onChange: (e)=>setSelectedBranch(e.target.value),\n                                                className: \"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select Branch\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    branches.map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: branch.id,\n                                                            children: [\n                                                                branch.name,\n                                                                \" (\",\n                                                                branch.code,\n                                                                \")\"\n                                                            ]\n                                                        }, branch.id, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 mb-4\",\n                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedCategory(category),\n                                                className: `px-4 py-2 rounded-md text-sm font-medium ${selectedCategory === category ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'}`,\n                                                children: category.charAt(0).toUpperCase() + category.slice(1)\n                                            }, category, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\",\n                                children: filteredMenuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `bg-white rounded-lg shadow-md p-4 cursor-pointer transition-transform hover:scale-105 ${!item.is_available ? 'opacity-50' : ''}`,\n                                        onClick: ()=>item.is_available && addToCart(item),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 line-clamp-2\",\n                                                        children: item.menu_item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.menu_item.is_signature_dish && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-500 text-xl\",\n                                                        children: \"⭐\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-2 line-clamp-2\",\n                                                children: item.menu_item.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-indigo-600\",\n                                                        children: formatCurrency(item.price)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.menu_item.spice_level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: getSpiceLevel(item.menu_item.spice_level)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Category: \",\n                                                            item.menu_item.category\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            item.current_sold,\n                                                            \"/\",\n                                                            item.daily_quota || '∞'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this),\n                                            !item.is_available && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-center text-red-600 font-medium\",\n                                                children: \"Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-96 bg-white shadow-lg p-6 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Order Summary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Customer Name (Optional)\",\n                                        value: customerName,\n                                        onChange: (e)=>setCustomerName(e.target.value),\n                                        className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"tel\",\n                                        placeholder: \"Phone Number (Optional)\",\n                                        value: customerPhone,\n                                        onChange: (e)=>setCustomerPhone(e.target.value),\n                                        className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 mb-4 max-h-64 overflow-y-auto\",\n                                children: cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-md p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 text-sm\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>updateCartItemQuantity(item.menu_item_id, 0),\n                                                        className: \"text-red-500 hover:text-red-700 text-sm\",\n                                                        children: \"✕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>updateCartItemQuantity(item.menu_item_id, item.quantity - 1),\n                                                                className: \"bg-gray-200 text-gray-700 w-6 h-6 rounded-full text-sm hover:bg-gray-300\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: item.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>updateCartItemQuantity(item.menu_item_id, item.quantity + 1),\n                                                                className: \"bg-gray-200 text-gray-700 w-6 h-6 rounded-full text-sm hover:bg-gray-300\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: formatCurrency(item.price * item.quantity)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Special instructions...\",\n                                                value: item.special_instructions,\n                                                onChange: (e)=>updateSpecialInstructions(item.menu_item_id, e.target.value),\n                                                className: \"w-full border border-gray-300 rounded-md px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-indigo-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.menu_item_id, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 pt-4 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Subtotal:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: formatCurrency(calculateSubtotal())\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Tax (10%):\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: formatCurrency(calculateTax())\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"Discount:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                value: discountAmount,\n                                                onChange: (e)=>setDiscountAmount(parseFloat(e.target.value) || 0),\n                                                className: \"w-20 border border-gray-300 rounded-md px-2 py-1 text-sm text-right focus:outline-none focus:ring-1 focus:ring-indigo-500\",\n                                                min: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-lg font-bold border-t border-gray-200 pt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Total:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: formatCurrency(calculateTotal())\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Payment Method\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: paymentMethod,\n                                        onChange: (e)=>setPaymentMethod(e.target.value),\n                                        className: \"w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"cash\",\n                                                children: \"Cash\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"card\",\n                                                children: \"Credit/Debit Card\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"e_wallet\",\n                                                children: \"E-Wallet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qris\",\n                                                children: \"QRIS\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"bank_transfer\",\n                                                children: \"Bank Transfer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: processOrder,\n                                disabled: cart.length === 0 || processingOrder,\n                                className: \"w-full mt-4 bg-indigo-600 text-white py-3 rounded-md font-medium hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: processingOrder ? 'Processing...' : `Process Order (${formatCurrency(calculateTotal())})`\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCart([]),\n                                disabled: cart.length === 0,\n                                className: \"w-full mt-2 bg-gray-600 text-white py-2 rounded-md font-medium hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Clear Cart\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 221,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 220,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\pos\\\\page.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/pos/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LoginForm() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { signIn } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        const { error } = await signIn(email, password);\n        if (error) {\n            setError(error.message);\n        }\n        setLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"Padang Restaurant System\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md shadow-sm -space-y-px\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"sr-only\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Email address\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"sr-only\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: \"password\",\n                                            autoComplete: \"current-password\",\n                                            required: true,\n                                            className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Password\",\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50\",\n                                children: loading ? 'Signing in...' : 'Sign in'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/LoginForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoginForm */ \"(ssr)/./src/components/auth/LoginForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ProtectedRoute({ children, allowedRoles, requireProfile = true }) {\n    const { user, profile, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoginForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 29,\n            columnNumber: 12\n        }, this);\n    }\n    if (requireProfile && !profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Profile Setup Required\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Your account needs to be configured by an administrator before you can access the system.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700\",\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    if (allowedRoles && profile && !allowedRoles.includes(profile.role)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Access Denied\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"You don't have permission to access this page.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Your role: \",\n                            profile?.role\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { profile, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const navigation = [\n        {\n            name: 'Dashboard',\n            href: '/dashboard',\n            icon: '📊'\n        },\n        {\n            name: 'Inventory',\n            href: '/inventory',\n            icon: '📦'\n        },\n        {\n            name: 'Production',\n            href: '/production',\n            icon: '👨‍🍳'\n        },\n        {\n            name: 'Branches',\n            href: '/branches',\n            icon: '🏪'\n        },\n        {\n            name: 'POS',\n            href: '/pos',\n            icon: '💰'\n        },\n        {\n            name: 'Reports',\n            href: '/reports',\n            icon: '📈'\n        }\n    ];\n    // Filter navigation based on user role\n    const filteredNavigation = navigation.filter((item)=>{\n        if (!profile) return false;\n        switch(profile.role){\n            case 'admin':\n                return true // Admin can access everything\n                ;\n            case 'manager':\n                return ![\n                    'branches'\n                ].includes(item.href.replace('/', ''));\n            case 'staff':\n                return [\n                    'dashboard',\n                    'inventory',\n                    'production'\n                ].includes(item.href.replace('/', ''));\n            case 'cashier':\n                return [\n                    'dashboard',\n                    'pos'\n                ].includes(item.href.replace('/', ''));\n            default:\n                return false;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 -mr-12 pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Close sidebar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"✕\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                                navigation: filteredNavigation,\n                                pathname: pathname\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col min-h-0 bg-white border-r border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                        navigation: filteredNavigation,\n                        pathname: pathname\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:pl-64 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\",\n                            onClick: ()=>setSidebarOpen(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Open sidebar\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                \"☰\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white shadow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Padang Restaurant System\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: [\n                                                profile?.full_name,\n                                                \" (\",\n                                                profile?.role,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: signOut,\n                                            className: \"bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700\",\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\nfunction SidebarContent({ navigation, pathname }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-16 flex-shrink-0 px-4 bg-indigo-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-white text-lg font-semibold\",\n                    children: \"PRS\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 px-2 py-4 space-y-1\",\n                    children: navigation.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: item.href,\n                            className: `${isActive ? 'bg-indigo-100 text-indigo-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-3 text-lg\",\n                                    children: item.icon\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this),\n                                item.name\n                            ]\n                        }, item.name, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session } })=>{\n                    setSession(session);\n                    setUser(session?.user ?? null);\n                    if (session?.user) {\n                        fetchProfile(session.user.id);\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Listen for auth changes\n            const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    setSession(session);\n                    setUser(session?.user ?? null);\n                    if (session?.user) {\n                        await fetchProfile(session.user.id);\n                    } else {\n                        setProfile(null);\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const fetchProfile = async (userId)=>{\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('profiles').select('*').eq('id', userId).single();\n            if (error) {\n                console.error('Error fetching profile:', error);\n            } else {\n                setProfile(data);\n            }\n        } catch (error) {\n            console.error('Error fetching profile:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signIn = async (email, password)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signUp = async (email, password, fullName)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName\n                }\n            }\n        });\n        return {\n            error\n        };\n    };\n    const signOut = async ()=>{\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) return {\n            error: 'No user logged in'\n        };\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('profiles').update(updates).eq('id', user.id);\n        if (!error) {\n            setProfile((prev)=>prev ? {\n                    ...prev,\n                    ...updates\n                } : null);\n        }\n        return {\n            error\n        };\n    };\n    const value = {\n        user,\n        profile,\n        session,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 127,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXNFO0FBRXBCO0FBYWxELE1BQU1LLDRCQUFjTCxvREFBYUEsQ0FBOEJNO0FBRXhELFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUFpQztJQUN0RSxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR1AsK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDUSxTQUFTQyxXQUFXLEdBQUdULCtDQUFRQSxDQUFpQjtJQUN2RCxNQUFNLENBQUNVLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQWlCO0lBQ3ZELE1BQU0sQ0FBQ1ksU0FBU0MsV0FBVyxHQUFHYiwrQ0FBUUEsQ0FBQztJQUV2Q0QsZ0RBQVNBO2tDQUFDO1lBQ1Isc0JBQXNCO1lBQ3RCRSxtREFBUUEsQ0FBQ2EsSUFBSSxDQUFDQyxVQUFVLEdBQUdDLElBQUk7MENBQUMsQ0FBQyxFQUFFQyxNQUFNLEVBQUVQLE9BQU8sRUFBRSxFQUFFO29CQUNwREMsV0FBV0Q7b0JBQ1hILFFBQVFHLFNBQVNKLFFBQVE7b0JBQ3pCLElBQUlJLFNBQVNKLE1BQU07d0JBQ2pCWSxhQUFhUixRQUFRSixJQUFJLENBQUNhLEVBQUU7b0JBQzlCLE9BQU87d0JBQ0xOLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUEsMEJBQTBCO1lBQzFCLE1BQU0sRUFDSkksTUFBTSxFQUFFRyxZQUFZLEVBQUUsRUFDdkIsR0FBR25CLG1EQUFRQSxDQUFDYSxJQUFJLENBQUNPLGlCQUFpQjswQ0FBQyxPQUFPQyxPQUFPWjtvQkFDaERDLFdBQVdEO29CQUNYSCxRQUFRRyxTQUFTSixRQUFRO29CQUV6QixJQUFJSSxTQUFTSixNQUFNO3dCQUNqQixNQUFNWSxhQUFhUixRQUFRSixJQUFJLENBQUNhLEVBQUU7b0JBQ3BDLE9BQU87d0JBQ0xWLFdBQVc7d0JBQ1hJLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUE7MENBQU8sSUFBTU8sYUFBYUcsV0FBVzs7UUFDdkM7aUNBQUcsRUFBRTtJQUVMLE1BQU1MLGVBQWUsT0FBT007UUFDMUIsSUFBSTtZQUNGLE1BQU0sRUFBRVAsSUFBSSxFQUFFUSxLQUFLLEVBQUUsR0FBRyxNQUFNeEIsbURBQVFBLENBQ25DeUIsSUFBSSxDQUFDLFlBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsTUFBTUosUUFDVEssTUFBTTtZQUVULElBQUlKLE9BQU87Z0JBQ1RLLFFBQVFMLEtBQUssQ0FBQywyQkFBMkJBO1lBQzNDLE9BQU87Z0JBQ0xoQixXQUFXUTtZQUNiO1FBQ0YsRUFBRSxPQUFPUSxPQUFPO1lBQ2RLLFFBQVFMLEtBQUssQ0FBQywyQkFBMkJBO1FBQzNDLFNBQVU7WUFDUlosV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNa0IsU0FBUyxPQUFPQyxPQUFlQztRQUNuQyxNQUFNLEVBQUVSLEtBQUssRUFBRSxHQUFHLE1BQU14QixtREFBUUEsQ0FBQ2EsSUFBSSxDQUFDb0Isa0JBQWtCLENBQUM7WUFDdkRGO1lBQ0FDO1FBQ0Y7UUFDQSxPQUFPO1lBQUVSO1FBQU07SUFDakI7SUFFQSxNQUFNVSxTQUFTLE9BQU9ILE9BQWVDLFVBQWtCRztRQUNyRCxNQUFNLEVBQUVYLEtBQUssRUFBRSxHQUFHLE1BQU14QixtREFBUUEsQ0FBQ2EsSUFBSSxDQUFDcUIsTUFBTSxDQUFDO1lBQzNDSDtZQUNBQztZQUNBSSxTQUFTO2dCQUNQcEIsTUFBTTtvQkFDSnFCLFdBQVdGO2dCQUNiO1lBQ0Y7UUFDRjtRQUNBLE9BQU87WUFBRVg7UUFBTTtJQUNqQjtJQUVBLE1BQU1jLFVBQVU7UUFDZCxNQUFNdEMsbURBQVFBLENBQUNhLElBQUksQ0FBQ3lCLE9BQU87SUFDN0I7SUFFQSxNQUFNQyxnQkFBZ0IsT0FBT0M7UUFDM0IsSUFBSSxDQUFDbkMsTUFBTSxPQUFPO1lBQUVtQixPQUFPO1FBQW9CO1FBRS9DLE1BQU0sRUFBRUEsS0FBSyxFQUFFLEdBQUcsTUFBTXhCLG1EQUFRQSxDQUM3QnlCLElBQUksQ0FBQyxZQUNMZ0IsTUFBTSxDQUFDRCxTQUNQYixFQUFFLENBQUMsTUFBTXRCLEtBQUthLEVBQUU7UUFFbkIsSUFBSSxDQUFDTSxPQUFPO1lBQ1ZoQixXQUFXa0MsQ0FBQUEsT0FBUUEsT0FBTztvQkFBRSxHQUFHQSxJQUFJO29CQUFFLEdBQUdGLE9BQU87Z0JBQUMsSUFBSTtRQUN0RDtRQUVBLE9BQU87WUFBRWhCO1FBQU07SUFDakI7SUFFQSxNQUFNbUIsUUFBUTtRQUNadEM7UUFDQUU7UUFDQUU7UUFDQUU7UUFDQW1CO1FBQ0FJO1FBQ0FJO1FBQ0FDO0lBQ0Y7SUFFQSxxQkFBTyw4REFBQ3RDLFlBQVkyQyxRQUFRO1FBQUNELE9BQU9BO2tCQUFRdkM7Ozs7OztBQUM5QztBQUVPLFNBQVN5QztJQUNkLE1BQU1DLFVBQVVqRCxpREFBVUEsQ0FBQ0k7SUFDM0IsSUFBSTZDLFlBQVk1QyxXQUFXO1FBQ3pCLE1BQU0sSUFBSTZDLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJEOlxccGFkYW5naHViX3N1cGFiYXNlXFxwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW1cXHNyY1xcY29udGV4dHNcXEF1dGhDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgVXNlciwgU2Vzc2lvbiB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcydcbmltcG9ydCB7IHN1cGFiYXNlLCBQcm9maWxlIH0gZnJvbSAnQC9saWIvc3VwYWJhc2UnXG5cbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xuICB1c2VyOiBVc2VyIHwgbnVsbFxuICBwcm9maWxlOiBQcm9maWxlIHwgbnVsbFxuICBzZXNzaW9uOiBTZXNzaW9uIHwgbnVsbFxuICBsb2FkaW5nOiBib29sZWFuXG4gIHNpZ25JbjogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IFByb21pc2U8eyBlcnJvcjogYW55IH0+XG4gIHNpZ25VcDogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIGZ1bGxOYW1lOiBzdHJpbmcpID0+IFByb21pc2U8eyBlcnJvcjogYW55IH0+XG4gIHNpZ25PdXQ6ICgpID0+IFByb21pc2U8dm9pZD5cbiAgdXBkYXRlUHJvZmlsZTogKHVwZGF0ZXM6IFBhcnRpYWw8UHJvZmlsZT4pID0+IFByb21pc2U8eyBlcnJvcjogYW55IH0+XG59XG5cbmNvbnN0IEF1dGhDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBdXRoQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZClcblxuZXhwb3J0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKVxuICBjb25zdCBbcHJvZmlsZSwgc2V0UHJvZmlsZV0gPSB1c2VTdGF0ZTxQcm9maWxlIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3Nlc3Npb24sIHNldFNlc3Npb25dID0gdXNlU3RhdGU8U2Vzc2lvbiB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBHZXQgaW5pdGlhbCBzZXNzaW9uXG4gICAgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKCkudGhlbigoeyBkYXRhOiB7IHNlc3Npb24gfSB9KSA9PiB7XG4gICAgICBzZXRTZXNzaW9uKHNlc3Npb24pXG4gICAgICBzZXRVc2VyKHNlc3Npb24/LnVzZXIgPz8gbnVsbClcbiAgICAgIGlmIChzZXNzaW9uPy51c2VyKSB7XG4gICAgICAgIGZldGNoUHJvZmlsZShzZXNzaW9uLnVzZXIuaWQpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgICAgfVxuICAgIH0pXG5cbiAgICAvLyBMaXN0ZW4gZm9yIGF1dGggY2hhbmdlc1xuICAgIGNvbnN0IHtcbiAgICAgIGRhdGE6IHsgc3Vic2NyaXB0aW9uIH0sXG4gICAgfSA9IHN1cGFiYXNlLmF1dGgub25BdXRoU3RhdGVDaGFuZ2UoYXN5bmMgKGV2ZW50LCBzZXNzaW9uKSA9PiB7XG4gICAgICBzZXRTZXNzaW9uKHNlc3Npb24pXG4gICAgICBzZXRVc2VyKHNlc3Npb24/LnVzZXIgPz8gbnVsbClcbiAgICAgIFxuICAgICAgaWYgKHNlc3Npb24/LnVzZXIpIHtcbiAgICAgICAgYXdhaXQgZmV0Y2hQcm9maWxlKHNlc3Npb24udXNlci5pZClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldFByb2ZpbGUobnVsbClcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgcmV0dXJuICgpID0+IHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IGZldGNoUHJvZmlsZSA9IGFzeW5jICh1c2VySWQ6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgncHJvZmlsZXMnKVxuICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgLmVxKCdpZCcsIHVzZXJJZClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwcm9maWxlOicsIGVycm9yKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0UHJvZmlsZShkYXRhKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwcm9maWxlOicsIGVycm9yKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHNpZ25JbiA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoUGFzc3dvcmQoe1xuICAgICAgZW1haWwsXG4gICAgICBwYXNzd29yZCxcbiAgICB9KVxuICAgIHJldHVybiB7IGVycm9yIH1cbiAgfVxuXG4gIGNvbnN0IHNpZ25VcCA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nLCBmdWxsTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduVXAoe1xuICAgICAgZW1haWwsXG4gICAgICBwYXNzd29yZCxcbiAgICAgIG9wdGlvbnM6IHtcbiAgICAgICAgZGF0YToge1xuICAgICAgICAgIGZ1bGxfbmFtZTogZnVsbE5hbWUsXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0pXG4gICAgcmV0dXJuIHsgZXJyb3IgfVxuICB9XG5cbiAgY29uc3Qgc2lnbk91dCA9IGFzeW5jICgpID0+IHtcbiAgICBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25PdXQoKVxuICB9XG5cbiAgY29uc3QgdXBkYXRlUHJvZmlsZSA9IGFzeW5jICh1cGRhdGVzOiBQYXJ0aWFsPFByb2ZpbGU+KSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm4geyBlcnJvcjogJ05vIHVzZXIgbG9nZ2VkIGluJyB9XG5cbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3Byb2ZpbGVzJylcbiAgICAgIC51cGRhdGUodXBkYXRlcylcbiAgICAgIC5lcSgnaWQnLCB1c2VyLmlkKVxuXG4gICAgaWYgKCFlcnJvcikge1xuICAgICAgc2V0UHJvZmlsZShwcmV2ID0+IHByZXYgPyB7IC4uLnByZXYsIC4uLnVwZGF0ZXMgfSA6IG51bGwpXG4gICAgfVxuXG4gICAgcmV0dXJuIHsgZXJyb3IgfVxuICB9XG5cbiAgY29uc3QgdmFsdWUgPSB7XG4gICAgdXNlcixcbiAgICBwcm9maWxlLFxuICAgIHNlc3Npb24sXG4gICAgbG9hZGluZyxcbiAgICBzaWduSW4sXG4gICAgc2lnblVwLFxuICAgIHNpZ25PdXQsXG4gICAgdXBkYXRlUHJvZmlsZSxcbiAgfVxuXG4gIHJldHVybiA8QXV0aENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT57Y2hpbGRyZW59PC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUF1dGgoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KVxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYW4gQXV0aFByb3ZpZGVyJylcbiAgfVxuICByZXR1cm4gY29udGV4dFxufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJzdXBhYmFzZSIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ1c2VyIiwic2V0VXNlciIsInByb2ZpbGUiLCJzZXRQcm9maWxlIiwic2Vzc2lvbiIsInNldFNlc3Npb24iLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImF1dGgiLCJnZXRTZXNzaW9uIiwidGhlbiIsImRhdGEiLCJmZXRjaFByb2ZpbGUiLCJpZCIsInN1YnNjcmlwdGlvbiIsIm9uQXV0aFN0YXRlQ2hhbmdlIiwiZXZlbnQiLCJ1bnN1YnNjcmliZSIsInVzZXJJZCIsImVycm9yIiwiZnJvbSIsInNlbGVjdCIsImVxIiwic2luZ2xlIiwiY29uc29sZSIsInNpZ25JbiIsImVtYWlsIiwicGFzc3dvcmQiLCJzaWduSW5XaXRoUGFzc3dvcmQiLCJzaWduVXAiLCJmdWxsTmFtZSIsIm9wdGlvbnMiLCJmdWxsX25hbWUiLCJzaWduT3V0IiwidXBkYXRlUHJvZmlsZSIsInVwZGF0ZXMiLCJ1cGRhdGUiLCJwcmV2IiwidmFsdWUiLCJQcm92aWRlciIsInVzZUF1dGgiLCJjb250ZXh0IiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auditLog.ts":
/*!*****************************!*\
  !*** ./src/lib/auditLog.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupOldAuditLogs: () => (/* binding */ cleanupOldAuditLogs),\n/* harmony export */   createAuditLog: () => (/* binding */ createAuditLog),\n/* harmony export */   getAuditLogs: () => (/* binding */ getAuditLogs),\n/* harmony export */   getAuditSummary: () => (/* binding */ getAuditSummary),\n/* harmony export */   logBulkOperation: () => (/* binding */ logBulkOperation),\n/* harmony export */   logDataExport: () => (/* binding */ logDataExport),\n/* harmony export */   logStockChange: () => (/* binding */ logStockChange),\n/* harmony export */   logTransferOperation: () => (/* binding */ logTransferOperation),\n/* harmony export */   logUserAuth: () => (/* binding */ logUserAuth)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\n/**\n * Create an audit log entry\n */ async function createAuditLog(entry) {\n    try {\n        const auditEntry = {\n            ...entry,\n            timestamp: new Date().toISOString()\n        };\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('audit_logs').insert(auditEntry);\n        if (error) {\n            console.error('Failed to create audit log:', error);\n        // Don't throw error to avoid breaking the main operation\n        }\n    } catch (error) {\n        console.error('Audit logging error:', error);\n    // Don't throw error to avoid breaking the main operation\n    }\n}\n/**\n * Get audit logs with filtering\n */ async function getAuditLogs(filter = {}) {\n    try {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('audit_logs').select(`\n        *,\n        user:profiles!user_id(full_name, email),\n        branch:branches(name),\n        warehouse:warehouses(name)\n      `);\n        // Apply filters\n        if (filter.action) {\n            query = query.eq('action', filter.action);\n        }\n        if (filter.entity_type) {\n            query = query.eq('entity_type', filter.entity_type);\n        }\n        if (filter.entity_id) {\n            query = query.eq('entity_id', filter.entity_id);\n        }\n        if (filter.user_id) {\n            query = query.eq('user_id', filter.user_id);\n        }\n        if (filter.branch_id) {\n            query = query.eq('branch_id', filter.branch_id);\n        }\n        if (filter.warehouse_id) {\n            query = query.eq('warehouse_id', filter.warehouse_id);\n        }\n        if (filter.start_date) {\n            query = query.gte('timestamp', filter.start_date);\n        }\n        if (filter.end_date) {\n            query = query.lte('timestamp', filter.end_date);\n        }\n        // Order by timestamp descending and apply limit\n        query = query.order('timestamp', {\n            ascending: false\n        });\n        if (filter.limit) {\n            query = query.limit(filter.limit);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw new Error(`Failed to fetch audit logs: ${error.message}`);\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error fetching audit logs:', error);\n        throw error;\n    }\n}\n/**\n * Log inventory stock change\n */ async function logStockChange(action, inventoryId, userId, userRole, details, warehouseId, branchId) {\n    await createAuditLog({\n        action,\n        entity_type: 'inventory',\n        entity_id: inventoryId,\n        user_id: userId,\n        user_role: userRole,\n        details,\n        warehouse_id: warehouseId,\n        branch_id: branchId\n    });\n}\n/**\n * Log transfer operation\n */ async function logTransferOperation(action, transferId, userId, userRole, details, warehouseId, branchId) {\n    await createAuditLog({\n        action,\n        entity_type: 'transfer',\n        entity_id: transferId,\n        user_id: userId,\n        user_role: userRole,\n        details,\n        warehouse_id: warehouseId,\n        branch_id: branchId\n    });\n}\n/**\n * Log bulk operation\n */ async function logBulkOperation(userId, userRole, details, warehouseId, branchId) {\n    await createAuditLog({\n        action: 'bulk_update',\n        entity_type: 'inventory',\n        entity_id: 'bulk_operation',\n        user_id: userId,\n        user_role: userRole,\n        details,\n        warehouse_id: warehouseId,\n        branch_id: branchId\n    });\n}\n/**\n * Log user authentication\n */ async function logUserAuth(action, userId, userRole, details, ipAddress, userAgent) {\n    await createAuditLog({\n        action,\n        entity_type: 'user',\n        entity_id: userId,\n        user_id: userId,\n        user_role: userRole,\n        details,\n        ip_address: ipAddress,\n        user_agent: userAgent\n    });\n}\n/**\n * Log data export\n */ async function logDataExport(userId, userRole, details, branchId) {\n    await createAuditLog({\n        action: 'data_export',\n        entity_type: 'system',\n        entity_id: 'data_export',\n        user_id: userId,\n        user_role: userRole,\n        details,\n        branch_id: branchId\n    });\n}\n/**\n * Get audit summary for a specific time period\n */ async function getAuditSummary(startDate, endDate, branchId) {\n    try {\n        const filter = {\n            start_date: startDate,\n            end_date: endDate,\n            branch_id: branchId\n        };\n        const logs = await getAuditLogs(filter);\n        const actionBreakdown = {};\n        const userActivity = {};\n        logs.forEach((log)=>{\n            // Count actions\n            actionBreakdown[log.action] = (actionBreakdown[log.action] || 0) + 1;\n            // Count user activity\n            userActivity[log.user_id] = (userActivity[log.user_id] || 0) + 1;\n        });\n        // Get top actions\n        const topActions = Object.entries(actionBreakdown).map(([action, count])=>({\n                action: action,\n                count\n            })).sort((a, b)=>b.count - a.count).slice(0, 10);\n        return {\n            totalActions: logs.length,\n            actionBreakdown: actionBreakdown,\n            userActivity,\n            topActions\n        };\n    } catch (error) {\n        console.error('Error generating audit summary:', error);\n        throw error;\n    }\n}\n/**\n * Clean up old audit logs (for maintenance)\n */ async function cleanupOldAuditLogs(daysToKeep = 365) {\n    try {\n        const cutoffDate = new Date();\n        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('audit_logs').delete().lt('timestamp', cutoffDate.toISOString()).select('id');\n        if (error) {\n            throw new Error(`Failed to cleanup audit logs: ${error.message}`);\n        }\n        return data?.length || 0;\n    } catch (error) {\n        console.error('Error cleaning up audit logs:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auditLog.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsService: () => (/* binding */ analyticsService),\n/* harmony export */   branchService: () => (/* binding */ branchService),\n/* harmony export */   ingredientService: () => (/* binding */ ingredientService),\n/* harmony export */   inventoryService: () => (/* binding */ inventoryService),\n/* harmony export */   kitchenService: () => (/* binding */ kitchenService),\n/* harmony export */   menuService: () => (/* binding */ menuService),\n/* harmony export */   productionService: () => (/* binding */ productionService),\n/* harmony export */   salesService: () => (/* binding */ salesService),\n/* harmony export */   warehouseService: () => (/* binding */ warehouseService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _auditLog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auditLog */ \"(ssr)/./src/lib/auditLog.ts\");\n\n\n// Inventory Management Functions\nconst inventoryService = {\n    // Get all inventory items for a warehouse\n    async getWarehouseInventory (warehouseId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n        *,\n        ingredient:ingredients(*),\n        warehouse:warehouses(*)\n      `).eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get low stock items across all warehouses (optimized with database filtering)\n    async getLowStockItems (branchId) {\n        // Use a more efficient query with database-side filtering\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('get_low_stock_items_optimized', {\n            branch_id_param: branchId || null\n        });\n        if (error) {\n            // Fallback to client-side filtering if RPC function doesn't exist\n            console.warn('RPC function not available, using fallback query');\n            return this.getLowStockItemsFallback(branchId);\n        }\n        return data || [];\n    },\n    // Fallback method for low stock items\n    async getLowStockItemsFallback (branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n        *,\n        ingredient:ingredients(*),\n        warehouse:warehouses(*)\n      `).filter('current_stock', 'lt', 'minimum_stock') // Database-side filtering\n        ;\n        if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query.order('current_stock', {\n            ascending: true\n        }).limit(100) // Limit results for performance\n        ;\n        if (error) throw error;\n        // Filter low stock items in JavaScript\n        // const lowStockItems = data?.filter(item =>\n        //   item.current_stock < item.minimum_stock\n        // ).sort((a, b) => a.current_stock - b.current_stock)\n        // return lowStockItems\n        return data || [];\n    },\n    // async getLowStockItems(branchId?: string) {\n    //   let query = supabase\n    //     .from('inventory')\n    //     .select(`\n    //       *,\n    //       ingredient:ingredients(*),\n    //       warehouse:warehouses(*)\n    //     `)\n    //     .filter('current_stock', 'lt', 10)\n    //   if (branchId) {\n    //     query = query.eq('warehouses.branch_id', branchId)\n    //   }\n    //   const { data, error } = await query.order('current_stock', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    // Update stock levels with enhanced validation and transaction safety\n    async updateStock (inventoryId, newStock, movementType, notes, performedBy) {\n        // Input validation\n        if (!inventoryId || newStock < 0) {\n            throw new Error('Invalid input: inventory ID is required and stock cannot be negative');\n        }\n        if (![\n            'in',\n            'out',\n            'adjustment',\n            'waste'\n        ].includes(movementType)) {\n            throw new Error('Invalid movement type');\n        }\n        // Fetch current inventory with detailed information\n        const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n        *,\n        ingredient:ingredients(unit, name),\n        warehouse:warehouses(name, code)\n      `).eq('id', inventoryId).single();\n        if (fetchError) {\n            throw new Error(`Failed to fetch inventory: ${fetchError.message}`);\n        }\n        const quantity = newStock - inventory.current_stock;\n        const isIncrease = quantity > 0;\n        // Business rule validation\n        if (newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n            throw new Error(`Stock cannot exceed maximum limit of ${inventory.maximum_stock} ${inventory.ingredient?.unit}`);\n        }\n        // Check if this would create negative stock for outbound movements\n        if (movementType === 'out' && newStock < 0) {\n            throw new Error('Cannot reduce stock below zero');\n        }\n        try {\n            // Use a transaction-like approach with error handling\n            const timestamp = new Date().toISOString();\n            // Update inventory record\n            const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                current_stock: newStock,\n                updated_at: timestamp,\n                last_restocked_at: isIncrease ? timestamp : inventory.last_restocked_at\n            }).eq('id', inventoryId);\n            if (updateError) {\n                throw new Error(`Failed to update inventory: ${updateError.message}`);\n            }\n            // Record stock movement with enhanced details\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n                warehouse_id: inventory.warehouse_id,\n                ingredient_id: inventory.ingredient_id,\n                movement_type: movementType,\n                quantity: Math.abs(quantity),\n                unit: inventory.ingredient?.unit || 'kg',\n                reference_type: 'manual_adjustment',\n                notes: notes || `Stock ${movementType} - ${inventory.ingredient?.name} at ${inventory.warehouse?.name}`,\n                performed_by: performedBy,\n                created_at: timestamp\n            });\n            if (movementError) {\n                // Attempt to rollback the inventory update\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: inventory.current_stock,\n                    updated_at: inventory.updated_at\n                }).eq('id', inventoryId);\n                throw new Error(`Failed to record stock movement: ${movementError.message}`);\n            }\n            // Log the stock change for audit trail\n            if (performedBy) {\n                try {\n                    await (0,_auditLog__WEBPACK_IMPORTED_MODULE_1__.logStockChange)(movementType, inventoryId, performedBy, 'staff', {\n                        ingredient_name: inventory.ingredient?.name || 'Unknown',\n                        warehouse_name: inventory.warehouse?.name || 'Unknown',\n                        previous_stock: inventory.current_stock,\n                        new_stock: newStock,\n                        quantity_changed: Math.abs(quantity),\n                        unit: inventory.ingredient?.unit || 'kg',\n                        notes: notes\n                    }, inventory.warehouse_id);\n                } catch (auditError) {\n                    console.error('Failed to log stock change:', auditError);\n                // Don't fail the operation if audit logging fails\n                }\n            }\n            return {\n                success: true,\n                previousStock: inventory.current_stock,\n                newStock: newStock,\n                quantity: Math.abs(quantity),\n                movementType,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(`Stock update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    },\n    // Create new inventory record\n    async createInventoryRecord (warehouseId, ingredientId, initialStock, notes) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            current_stock: initialStock,\n            minimum_stock: 0,\n            maximum_stock: initialStock * 10,\n            reorder_point: initialStock * 0.2\n        }).select().single();\n        if (error) throw error;\n        // Record initial stock movement\n        const { data: ingredient } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select('unit').eq('id', ingredientId).single();\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            movement_type: 'in',\n            quantity: initialStock,\n            unit: ingredient?.unit || 'kg',\n            reference_type: 'initial_stock',\n            notes: notes || 'Initial stock entry'\n        });\n        return data;\n    },\n    // Get stock movements for a warehouse\n    async getStockMovements (warehouseId, limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').select(`\n        *,\n        ingredient:ingredients(*),\n        warehouse:warehouses(*),\n        performer:profiles(full_name)\n      `).eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    },\n    // Create warehouse transfer\n    async createTransfer (fromWarehouseId, toWarehouseId, items, requestedBy, notes) {\n        // Create transfer record\n        const { data: transfer, error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').insert({\n            from_warehouse_id: fromWarehouseId,\n            to_warehouse_id: toWarehouseId,\n            requested_by: requestedBy,\n            total_items: items.length,\n            notes,\n            status: 'pending'\n        }).select().single();\n        if (transferError) throw transferError;\n        // Create transfer items\n        const transferItems = items.map((item)=>({\n                transfer_id: transfer.id,\n                ingredient_id: item.ingredient_id,\n                requested_quantity: item.quantity,\n                unit: item.unit,\n                notes: item.notes\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').insert(transferItems);\n        if (itemsError) throw itemsError;\n        return transfer;\n    },\n    // Get pending transfers\n    async getPendingTransfers (warehouseId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(`\n        *,\n        from_warehouse:warehouses!from_warehouse_id(*),\n        to_warehouse:warehouses!to_warehouse_id(*),\n        requester:profiles!requested_by(*),\n        transfer_items(*, ingredient:ingredients(*))\n      `).eq('status', 'pending');\n        if (warehouseId) {\n            query = query.or(`from_warehouse_id.eq.${warehouseId},to_warehouse_id.eq.${warehouseId}`);\n        }\n        const { data, error } = await query.order('requested_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Approve transfer with enhanced validation\n    async approveTransfer (transferId, approvedBy, approvedItems, autoComplete = false) {\n        try {\n            // Validate transfer exists and is in pending status\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(`\n          *,\n          from_warehouse:warehouses!from_warehouse_id(*),\n          to_warehouse:warehouses!to_warehouse_id(*),\n          transfer_items(*, ingredient:ingredients(*))\n        `).eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(`Transfer not found: ${fetchError.message}`);\n            }\n            if (transfer.status !== 'pending') {\n                throw new Error(`Cannot approve transfer with status: ${transfer.status}`);\n            }\n            // Validate stock availability for approved quantities\n            for (const approvedItem of approvedItems){\n                const transferItem = transfer.transfer_items.find((item)=>item.id === approvedItem.id);\n                if (!transferItem) {\n                    throw new Error(`Transfer item not found: ${approvedItem.id}`);\n                }\n                // Check current stock in source warehouse\n                const { data: inventory, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock').eq('warehouse_id', transfer.from_warehouse_id).eq('ingredient_id', transferItem.ingredient_id).single();\n                if (inventoryError) {\n                    throw new Error(`Cannot verify stock for ingredient: ${transferItem.ingredient.name}`);\n                }\n                if (inventory.current_stock < approvedItem.approved_quantity) {\n                    throw new Error(`Insufficient stock for ${transferItem.ingredient.name}. Available: ${inventory.current_stock}, Requested: ${approvedItem.approved_quantity}`);\n                }\n            }\n            const timestamp = new Date().toISOString();\n            // Update transfer status\n            const { error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'approved',\n                approved_by: approvedBy,\n                approved_at: timestamp\n            }).eq('id', transferId);\n            if (transferError) {\n                throw new Error(`Failed to approve transfer: ${transferError.message}`);\n            }\n            // Update approved quantities for items\n            for (const item of approvedItems){\n                const { error: itemError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').update({\n                    approved_quantity: item.approved_quantity\n                }).eq('id', item.id);\n                if (itemError) {\n                    throw new Error(`Failed to update approved quantity: ${itemError.message}`);\n                }\n            }\n            // Auto-complete the transfer if requested\n            if (autoComplete) {\n                try {\n                    const completionResult = await this.completeTransfer(transferId, approvedBy);\n                    return {\n                        success: true,\n                        transferId,\n                        approvedAt: timestamp,\n                        approvedItems: approvedItems.length,\n                        autoCompleted: true,\n                        completionResult\n                    };\n                } catch (completionError) {\n                    // If auto-completion fails, log the error but don't fail the approval\n                    console.error('Auto-completion failed after approval:', completionError);\n                    return {\n                        success: true,\n                        transferId,\n                        approvedAt: timestamp,\n                        approvedItems: approvedItems.length,\n                        autoCompleted: false,\n                        autoCompletionError: completionError instanceof Error ? completionError.message : 'Unknown error'\n                    };\n                }\n            }\n            return {\n                success: true,\n                transferId,\n                approvedAt: timestamp,\n                approvedItems: approvedItems.length,\n                autoCompleted: false\n            };\n        } catch (error) {\n            throw new Error(`Transfer approval failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    },\n    // Complete transfer and update inventory levels\n    async completeTransfer (transferId, completedBy) {\n        try {\n            // Fetch transfer with all details\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(`\n          *,\n          from_warehouse:warehouses!from_warehouse_id(*),\n          to_warehouse:warehouses!to_warehouse_id(*),\n          transfer_items(*, ingredient:ingredients(*))\n        `).eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(`Transfer not found: ${fetchError.message}`);\n            }\n            if (transfer.status !== 'approved' && transfer.status !== 'in_transit') {\n                throw new Error(`Cannot complete transfer with status: ${transfer.status}`);\n            }\n            const timestamp = new Date().toISOString();\n            const stockMovements = [];\n            // Process each transfer item\n            for (const item of transfer.transfer_items){\n                const approvedQty = item.approved_quantity || item.requested_quantity;\n                // Reduce stock from source warehouse\n                const { data: sourceInventory, error: sourceError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', transfer.from_warehouse_id).eq('ingredient_id', item.ingredient_id).single();\n                if (sourceError) {\n                    throw new Error(`Source inventory not found for ${item.ingredient.name}`);\n                }\n                if (sourceInventory.current_stock < approvedQty) {\n                    throw new Error(`Insufficient stock for ${item.ingredient.name}. Available: ${sourceInventory.current_stock}, Required: ${approvedQty}`);\n                }\n                // Update source inventory\n                const { error: sourceUpdateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: sourceInventory.current_stock - approvedQty,\n                    updated_at: timestamp\n                }).eq('id', sourceInventory.id);\n                if (sourceUpdateError) {\n                    throw new Error(`Failed to update source inventory: ${sourceUpdateError.message}`);\n                }\n                // Add stock movement for source (outbound)\n                stockMovements.push({\n                    warehouse_id: transfer.from_warehouse_id,\n                    ingredient_id: item.ingredient_id,\n                    movement_type: 'transfer',\n                    quantity: approvedQty,\n                    unit: item.unit,\n                    reference_type: 'transfer_out',\n                    reference_id: transferId,\n                    notes: `Transfer out to ${transfer.to_warehouse.name} - ${item.ingredient.name}`,\n                    performed_by: completedBy,\n                    created_at: timestamp\n                });\n                // Check if destination inventory exists\n                const { data: destInventory, error: destFetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', transfer.to_warehouse_id).eq('ingredient_id', item.ingredient_id).maybeSingle();\n                if (destFetchError) {\n                    throw new Error(`Error checking destination inventory: ${destFetchError.message}`);\n                }\n                if (destInventory) {\n                    // Update existing destination inventory\n                    const { error: destUpdateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: destInventory.current_stock + approvedQty,\n                        updated_at: timestamp,\n                        last_restocked_at: timestamp\n                    }).eq('id', destInventory.id);\n                    if (destUpdateError) {\n                        throw new Error(`Failed to update destination inventory: ${destUpdateError.message}`);\n                    }\n                } else {\n                    // Create new destination inventory record\n                    const { error: destCreateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n                        warehouse_id: transfer.to_warehouse_id,\n                        ingredient_id: item.ingredient_id,\n                        current_stock: approvedQty,\n                        minimum_stock: 0,\n                        maximum_stock: approvedQty * 10,\n                        reorder_point: approvedQty * 0.2,\n                        last_restocked_at: timestamp\n                    });\n                    if (destCreateError) {\n                        throw new Error(`Failed to create destination inventory: ${destCreateError.message}`);\n                    }\n                }\n                // Add stock movement for destination (inbound)\n                stockMovements.push({\n                    warehouse_id: transfer.to_warehouse_id,\n                    ingredient_id: item.ingredient_id,\n                    movement_type: 'transfer',\n                    quantity: approvedQty,\n                    unit: item.unit,\n                    reference_type: 'transfer_in',\n                    reference_id: transferId,\n                    notes: `Transfer in from ${transfer.from_warehouse.name} - ${item.ingredient.name}`,\n                    performed_by: completedBy,\n                    created_at: timestamp\n                });\n            }\n            // Insert all stock movements\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n            if (movementError) {\n                throw new Error(`Failed to record stock movements: ${movementError.message}`);\n            }\n            // Update transfer status to completed\n            const { error: completeError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'completed',\n                completed_at: timestamp\n            }).eq('id', transferId);\n            if (completeError) {\n                throw new Error(`Failed to complete transfer: ${completeError.message}`);\n            }\n            return {\n                success: true,\n                transferId,\n                completedAt: timestamp,\n                itemsTransferred: transfer.transfer_items.length,\n                stockMovements: stockMovements.length\n            };\n        } catch (error) {\n            throw new Error(`Transfer completion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    },\n    // Cancel transfer\n    async cancelTransfer (transferId, cancelledBy, reason) {\n        try {\n            // Validate transfer exists and can be cancelled\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select('*').eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(`Transfer not found: ${fetchError.message}`);\n            }\n            if (![\n                'pending',\n                'approved',\n                'in_transit'\n            ].includes(transfer.status)) {\n                throw new Error(`Cannot cancel transfer with status: ${transfer.status}`);\n            }\n            const timestamp = new Date().toISOString();\n            // Update transfer status\n            const { error: cancelError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'cancelled',\n                notes: transfer.notes ? `${transfer.notes}\\n\\nCancelled: ${reason || 'No reason provided'}` : `Cancelled: ${reason || 'No reason provided'}`,\n                completed_at: timestamp\n            }).eq('id', transferId);\n            if (cancelError) {\n                throw new Error(`Failed to cancel transfer: ${cancelError.message}`);\n            }\n            return {\n                success: true,\n                transferId,\n                cancelledAt: timestamp,\n                reason: reason || 'No reason provided'\n            };\n        } catch (error) {\n            throw new Error(`Transfer cancellation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    },\n    // Bulk update stock levels for multiple items\n    async bulkUpdateStock (updates, performedBy) {\n        if (!updates || updates.length === 0) {\n            throw new Error('No updates provided');\n        }\n        const results = [];\n        const timestamp = new Date().toISOString();\n        const stockMovements = [];\n        try {\n            // Process each update\n            for (const update of updates){\n                try {\n                    // Validate input\n                    if (!update.inventoryId || update.newStock < 0) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: 'Invalid input: inventory ID is required and stock cannot be negative'\n                        });\n                        continue;\n                    }\n                    // Fetch current inventory\n                    const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n              *,\n              ingredient:ingredients(unit, name),\n              warehouse:warehouses(name, code)\n            `).eq('id', update.inventoryId).single();\n                    if (fetchError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: `Failed to fetch inventory: ${fetchError.message}`\n                        });\n                        continue;\n                    }\n                    const quantity = update.newStock - inventory.current_stock;\n                    // Business rule validation\n                    if (update.newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: `Stock cannot exceed maximum limit of ${inventory.maximum_stock} ${inventory.ingredient?.unit}`\n                        });\n                        continue;\n                    }\n                    // Update inventory\n                    const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: update.newStock,\n                        updated_at: timestamp,\n                        last_restocked_at: quantity > 0 ? timestamp : inventory.last_restocked_at\n                    }).eq('id', update.inventoryId);\n                    if (updateError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: `Failed to update inventory: ${updateError.message}`\n                        });\n                        continue;\n                    }\n                    // Prepare stock movement record\n                    stockMovements.push({\n                        warehouse_id: inventory.warehouse_id,\n                        ingredient_id: inventory.ingredient_id,\n                        movement_type: update.movementType,\n                        quantity: Math.abs(quantity),\n                        unit: inventory.ingredient?.unit || 'kg',\n                        reference_type: 'bulk_adjustment',\n                        notes: update.notes || `Bulk stock ${update.movementType} - ${inventory.ingredient?.name}`,\n                        performed_by: performedBy,\n                        created_at: timestamp\n                    });\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: true,\n                        previousStock: inventory.current_stock,\n                        newStock: update.newStock\n                    });\n                } catch (error) {\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: false,\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    });\n                }\n            }\n            // Insert all stock movements in batch\n            if (stockMovements.length > 0) {\n                const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n                if (movementError) {\n                    throw new Error(`Failed to record stock movements: ${movementError.message}`);\n                }\n            }\n            const successCount = results.filter((r)=>r.success).length;\n            const failureCount = results.filter((r)=>!r.success).length;\n            return {\n                success: failureCount === 0,\n                totalUpdates: updates.length,\n                successCount,\n                failureCount,\n                results,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(`Bulk update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    },\n    // Get comprehensive low stock alerts with reorder suggestions\n    async getLowStockAlertsWithReorderSuggestions (branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n        *,\n        ingredient:ingredients(*),\n        warehouse:warehouses(*)\n      `);\n        if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query;\n        if (error) throw error;\n        // Process and categorize alerts\n        const alerts = data?.map((item)=>{\n            const stockLevel = item.current_stock;\n            const minStock = item.minimum_stock;\n            const reorderPoint = item.reorder_point;\n            const maxStock = item.maximum_stock;\n            let alertLevel = 'good';\n            let suggestedReorderQuantity = 0;\n            let daysUntilStockout = null;\n            // Determine alert level\n            if (stockLevel <= 0) {\n                alertLevel = 'critical';\n            } else if (stockLevel <= minStock) {\n                alertLevel = 'critical';\n            } else if (stockLevel <= reorderPoint) {\n                alertLevel = 'reorder';\n            } else if (stockLevel <= minStock * 1.5) {\n                alertLevel = 'low';\n            }\n            // Calculate suggested reorder quantity\n            if (alertLevel !== 'good') {\n                const targetStock = maxStock || minStock * 3;\n                suggestedReorderQuantity = Math.max(0, targetStock - stockLevel);\n            }\n            // Estimate days until stockout (simplified calculation)\n            // In a real system, you'd use historical consumption data\n            if (stockLevel > 0 && minStock > 0) {\n                const avgDailyConsumption = minStock / 30 // Rough estimate\n                ;\n                if (avgDailyConsumption > 0) {\n                    daysUntilStockout = Math.floor(stockLevel / avgDailyConsumption);\n                }\n            }\n            return {\n                ...item,\n                alertLevel,\n                suggestedReorderQuantity,\n                daysUntilStockout,\n                stockPercentage: maxStock > 0 ? stockLevel / maxStock * 100 : 0\n            };\n        }).filter((item)=>item.alertLevel !== 'good').sort((a, b)=>{\n            // Sort by alert level priority, then by days until stockout\n            const alertPriority = {\n                critical: 0,\n                low: 1,\n                reorder: 2,\n                good: 3\n            };\n            const aPriority = alertPriority[a.alertLevel];\n            const bPriority = alertPriority[b.alertLevel];\n            if (aPriority !== bPriority) {\n                return aPriority - bPriority;\n            }\n            // If same alert level, sort by days until stockout (ascending)\n            if (a.daysUntilStockout !== null && b.daysUntilStockout !== null) {\n                return a.daysUntilStockout - b.daysUntilStockout;\n            }\n            return 0;\n        });\n        return alerts || [];\n    },\n    // Get inventory performance metrics\n    async getInventoryMetrics (warehouseId, branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n        *,\n        ingredient:ingredients(cost_per_unit),\n        warehouse:warehouses(branch_id)\n      `);\n        if (warehouseId) {\n            query = query.eq('warehouse_id', warehouseId);\n        } else if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query;\n        if (error) throw error;\n        if (!data || data.length === 0) {\n            return {\n                totalItems: 0,\n                totalValue: 0,\n                lowStockCount: 0,\n                criticalStockCount: 0,\n                reorderNeededCount: 0,\n                averageStockLevel: 0,\n                turnoverRate: 0\n            };\n        }\n        const totalItems = data.length;\n        const totalValue = data.reduce((sum, item)=>{\n            const cost = item.ingredient?.cost_per_unit || 0;\n            return sum + item.current_stock * cost;\n        }, 0);\n        const lowStockCount = data.filter((item)=>item.current_stock <= item.minimum_stock && item.current_stock > 0).length;\n        const criticalStockCount = data.filter((item)=>item.current_stock <= 0).length;\n        const reorderNeededCount = data.filter((item)=>item.current_stock <= item.reorder_point).length;\n        const averageStockLevel = data.reduce((sum, item)=>{\n            const maxStock = item.maximum_stock || item.minimum_stock * 3;\n            const stockPercentage = maxStock > 0 ? item.current_stock / maxStock * 100 : 0;\n            return sum + stockPercentage;\n        }, 0) / totalItems;\n        return {\n            totalItems,\n            totalValue,\n            lowStockCount,\n            criticalStockCount,\n            reorderNeededCount,\n            averageStockLevel: Math.round(averageStockLevel),\n            turnoverRate: 0 // Would need historical data to calculate\n        };\n    },\n    // Generate automatic reorder suggestions\n    async generateReorderSuggestions (warehouseId, branchId) {\n        const alerts = await this.getLowStockAlertsWithReorderSuggestions(branchId);\n        const suggestions = alerts.filter((item)=>item.suggestedReorderQuantity > 0).map((item)=>({\n                inventoryId: item.id,\n                ingredientId: item.ingredient.id,\n                ingredientName: item.ingredient.name,\n                ingredientCode: item.ingredient.code,\n                warehouseId: item.warehouse_id,\n                warehouseName: item.warehouse.name,\n                currentStock: item.current_stock,\n                minimumStock: item.minimum_stock,\n                reorderPoint: item.reorder_point,\n                maximumStock: item.maximum_stock,\n                suggestedQuantity: item.suggestedReorderQuantity,\n                estimatedCost: (item.ingredient.cost_per_unit || 0) * item.suggestedReorderQuantity,\n                priority: item.alertLevel,\n                daysUntilStockout: item.daysUntilStockout,\n                unit: item.ingredient.unit\n            })).sort((a, b)=>{\n            const priorityOrder = {\n                critical: 0,\n                low: 1,\n                reorder: 2\n            };\n            return priorityOrder[a.priority] - priorityOrder[b.priority];\n        });\n        const totalEstimatedCost = suggestions.reduce((sum, item)=>sum + item.estimatedCost, 0);\n        const criticalCount = suggestions.filter((item)=>item.priority === 'critical').length;\n        const lowCount = suggestions.filter((item)=>item.priority === 'low').length;\n        const reorderCount = suggestions.filter((item)=>item.priority === 'reorder').length;\n        return {\n            suggestions,\n            summary: {\n                totalItems: suggestions.length,\n                totalEstimatedCost,\n                criticalCount,\n                lowCount,\n                reorderCount\n            }\n        };\n    }\n};\n// Branch Management Functions\n// export const branchService = {\n//   // Get all branches\n//   async getAllBranches() {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*)\n//       `)\n//       .eq('is_active', true)\n//       .order('name')\n//     if (error) throw error\n//     return data\n//   },\n//   // Get branch with details\n//   async getBranchDetails(branchId: string) {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*),\n//         warehouses(*),\n//         kitchens(*)\n//       `)\n//       .eq('id', branchId)\n//       .single()\n//     if (error) throw error\n//     return data\n//   }\n// }\nconst branchService = {\n    // Get all branches\n    async getAllBranches () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(`\n        *,\n        manager:profiles!fk_branches_manager(*)\n      `).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get branch with details\n    async getBranchDetails (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(`\n        *,\n        manager:profiles!fk_branches_manager(*),\n        warehouses(*),\n        kitchens(*)\n      `).eq('id', branchId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Warehouse Management Functions\nconst warehouseService = {\n    // Get all warehouses\n    async getAllWarehouses () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(`\n        *,\n        branch:branches(*),\n        manager:profiles(*)\n      `).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get warehouses by branch\n    async getWarehousesByBranch (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(`\n        *,\n        manager:profiles(*)\n      `).eq('branch_id', branchId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Ingredient Management Functions\nconst ingredientService = {\n    // Get all ingredients\n    async getAllIngredients () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(`\n        *,\n        category:ingredient_categories(*)\n      `).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get ingredients by category\n    async getIngredientsByCategory (categoryId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(`\n        *,\n        category:ingredient_categories(*)\n      `).eq('category_id', categoryId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Production Management Functions\nconst productionService = {\n    // Get all recipes\n    async getAllRecipes () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(`\n        *,\n        recipe_ingredients(\n          *,\n          ingredient:ingredients(*)\n        )\n      `).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get recipe details\n    async getRecipeDetails (recipeId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(`\n        *,\n        recipe_ingredients(\n          *,\n          ingredient:ingredients(*)\n        )\n      `).eq('id', recipeId).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create production batch\n    async createProductionBatch (kitchenId, recipeId, plannedQuantity, startedBy) {\n        const batchNumber = `BATCH-${Date.now()}`;\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').insert({\n            kitchen_id: kitchenId,\n            recipe_id: recipeId,\n            batch_number: batchNumber,\n            planned_quantity: plannedQuantity,\n            status: 'planned',\n            started_by: startedBy,\n            planned_start_time: new Date().toISOString()\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Get production batches\n    async getProductionBatches (kitchenId, status) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(`\n        *,\n        recipe:recipes(*),\n        kitchen:kitchens(*),\n        starter:profiles!started_by(*),\n        completer:profiles!completed_by(*)\n      `);\n        if (kitchenId) {\n            query = query.eq('kitchen_id', kitchenId);\n        }\n        if (status) {\n            query = query.eq('status', status);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update batch status\n    async updateBatchStatus (batchId, status, userId, actualQuantity, qualityScore, qualityNotes) {\n        const updates = {\n            status,\n            updated_at: new Date().toISOString()\n        };\n        if (status === 'in_progress') {\n            updates.actual_start_time = new Date().toISOString();\n        } else if (status === 'completed') {\n            updates.actual_end_time = new Date().toISOString();\n            updates.completed_by = userId;\n            if (actualQuantity) updates.actual_quantity = actualQuantity;\n            if (qualityScore) updates.quality_score = qualityScore;\n            if (qualityNotes) updates.quality_notes = qualityNotes;\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update(updates).eq('id', batchId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Record ingredient usage for batch\n    async recordIngredientUsage (batchId, ingredientUsage) {\n        const usageRecords = ingredientUsage.map((usage)=>({\n                batch_id: batchId,\n                ingredient_id: usage.ingredient_id,\n                planned_quantity: usage.planned_quantity,\n                actual_quantity: usage.actual_quantity,\n                unit: usage.unit,\n                cost_per_unit: usage.cost_per_unit,\n                total_cost: usage.actual_quantity * usage.cost_per_unit\n            }));\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('batch_ingredients_used').insert(usageRecords);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Get kitchen production summary\n    async getKitchenProductionSummary (kitchenId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(`\n        *,\n        recipe:recipes(name)\n      `).eq('kitchen_id', kitchenId);\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n// Kitchen Management Functions\nconst kitchenService = {\n    // Get all kitchens\n    async getAllKitchens () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(`\n        *,\n        branch:branches(*),\n        warehouse:warehouses(*),\n        head_chef:profiles(*)\n      `).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get kitchen details\n    async getKitchenDetails (kitchenId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(`\n        *,\n        branch:branches(*),\n        warehouse:warehouses(*),\n        head_chef:profiles(*)\n      `).eq('id', kitchenId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Sales Management Functions\nconst salesService = {\n    // Get sales transactions\n    async getSalesTransactions (branchId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select(`\n        *,\n        branch:branches(*),\n        server:profiles!served_by(*),\n        sales_transaction_items(\n          *,\n          menu_item:menu_items(*)\n        )\n      `);\n        if (branchId) {\n            query = query.eq('branch_id', branchId);\n        }\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get daily sales summary\n    async getDailySalesSummary (branchId, date) {\n        const targetDate = date || new Date().toISOString().split('T')[0];\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('daily_sales_summaries').select('*').eq('branch_id', branchId).eq('date', targetDate).single();\n        if (error && error.code !== 'PGRST116') throw error;\n        return data;\n    },\n    // Create sales transaction\n    async createSalesTransaction (branchId, items, customerInfo, paymentInfo, servedBy) {\n        const transactionNumber = `TXN-${Date.now()}`;\n        const totalAmount = items.reduce((sum, item)=>sum + item.quantity * item.unit_price, 0);\n        // Create transaction\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').insert({\n            branch_id: branchId,\n            transaction_number: transactionNumber,\n            customer_name: customerInfo.name,\n            customer_phone: customerInfo.phone,\n            total_amount: totalAmount,\n            tax_amount: paymentInfo.tax_amount || 0,\n            discount_amount: paymentInfo.discount_amount || 0,\n            payment_method: paymentInfo.method,\n            payment_status: 'completed',\n            served_by: servedBy\n        }).select().single();\n        if (transactionError) throw transactionError;\n        // Create transaction items\n        const transactionItems = items.map((item)=>({\n                transaction_id: transaction.id,\n                menu_item_id: item.menu_item_id,\n                quantity: item.quantity,\n                unit_price: item.unit_price,\n                total_price: item.quantity * item.unit_price,\n                special_instructions: item.special_instructions\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').insert(transactionItems);\n        if (itemsError) throw itemsError;\n        return transaction;\n    }\n};\n// Menu Management Functions\nconst menuService = {\n    // Get menu items\n    async getMenuItems () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select(`\n        *,\n        recipe:recipes(*)\n      `).eq('is_available', true).order('category', {\n            ascending: true\n        }).order('name', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get branch menu with pricing\n    // async getBranchMenu(branchId: string) {\n    //   const { data, error } = await supabase\n    //     .from('branch_menu_pricing')\n    //     .select(`\n    //       *,\n    //       menu_item:menu_items(\n    //         *,\n    //         recipe:recipes(*)\n    //       )\n    //     `)\n    //     .eq('branch_id', branchId)\n    //     .eq('is_available', true)\n    //     .order('menu_item.category', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    async getBranchMenu (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').select(`\n        *,\n        menu_item:menu_items(\n          *,\n          recipe:recipes(*)\n        )\n      `).eq('branch_id', branchId).eq('is_available', true).order('menu_item(category)', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item availability\n    async updateMenuItemAvailability (branchId, menuItemId, isAvailable) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').update({\n            is_available: isAvailable\n        }).eq('branch_id', branchId).eq('menu_item_id', menuItemId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    }\n};\n// // Analytics and Reporting Functions\n// export const analyticsService = {\n//   // Get branch performance metrics\n//   async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     // Get sales data\n//     const { data: salesData, error: salesError } = await supabase\n//       .from('sales_transactions')\n//       .select('total_amount, created_at, payment_method')\n//       .eq('branch_id', branchId)\n//       .gte('created_at', start)\n//       .lte('created_at', end)\n//     if (salesError) throw salesError\n//     // Get inventory data\n//     const { data: inventoryData, error: inventoryError } = await supabase\n//       .from('inventory')\n//       .select(`\n//         current_stock,\n//         minimum_stock,\n//         ingredient:ingredients(cost_per_unit)\n//       `)\n//       .in('warehouse_id',\n//         supabase\n//           .from('warehouses')\n//           .select('id')\n//           .eq('branch_id', branchId)\n//       )\n//     if (inventoryError) throw inventoryError\n//     // Calculate metrics\n//     const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)\n//     const totalTransactions = salesData.length\n//     const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0\n//     const inventoryValue = inventoryData.reduce((sum, item) =>\n//       sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0\n//     )\n//     const lowStockItems = inventoryData.filter(item =>\n//       item.current_stock <= item.minimum_stock\n//     ).length\n//     return {\n//       totalRevenue,\n//       totalTransactions,\n//       averageTransaction,\n//       inventoryValue,\n//       lowStockItems,\n//       salesData,\n//       inventoryData\n//     }\n//   },\n//   // Get top selling items\n//   async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     const { data, error } = await supabase\n//       .from('sales_transaction_items')\n//       .select(`\n//         quantity,\n//         total_price,\n//         menu_item:menu_items(name, category),\n//         transaction:sales_transactions!inner(created_at, branch_id)\n//       `)\n//       .eq('transaction.branch_id', branchId)\n//       .gte('transaction.created_at', start)\n//       .lte('transaction.created_at', end)\n//     if (error) throw error\n//     // Aggregate by menu item\n//     const itemStats = data.reduce((acc: any, item) => {\n//       const itemName = item.menu_item.name\n//       if (!acc[itemName]) {\n//         acc[itemName] = {\n//           name: itemName,\n//           category: item.menu_item.category,\n//           totalQuantity: 0,\n//           totalRevenue: 0\n//         }\n//       }\n//       acc[itemName].totalQuantity += item.quantity\n//       acc[itemName].totalRevenue += item.total_price\n//       return acc\n//     }, {})\n//     return Object.values(itemStats)\n//       .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)\n//       .slice(0, limit)\n//   }\n// }\n// Analytics and Reporting Functions\nconst analyticsService = {\n    // Get branch performance metrics\n    async getBranchPerformance (branchId, startDate, endDate) {\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Get sales data\n        const { data: salesData, error: salesError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select('total_amount, created_at, payment_method').eq('branch_id', branchId).gte('created_at', start).lte('created_at', end);\n        if (salesError) throw salesError;\n        // First get warehouse IDs for the branch\n        const { data: warehouseIds, error: warehouseError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select('id').eq('branch_id', branchId);\n        if (warehouseError) throw warehouseError;\n        // Get inventory data using warehouse IDs\n        const warehouseIdList = warehouseIds.map((w)=>w.id);\n        const { data: inventoryData, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(`\n        current_stock,\n        minimum_stock,\n        ingredient:ingredients(cost_per_unit)\n      `).in('warehouse_id', warehouseIdList);\n        if (inventoryError) throw inventoryError;\n        // Calculate metrics\n        const totalRevenue = salesData.reduce((sum, sale)=>sum + sale.total_amount, 0);\n        const totalTransactions = salesData.length;\n        const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;\n        const inventoryValue = inventoryData.reduce((sum, item)=>sum + item.current_stock * (item.ingredient?.cost_per_unit || 0), 0);\n        const lowStockItems = inventoryData.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        return {\n            totalRevenue,\n            totalTransactions,\n            averageTransaction,\n            inventoryValue,\n            lowStockItems,\n            salesData,\n            inventoryData\n        };\n    },\n    // Get top selling items\n    async getTopSellingItems (branchId, limit = 10, startDate, endDate) {\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').select(`\n        quantity,\n        total_price,\n        menu_item:menu_items(name, category),\n        sales_transactions!inner(created_at, branch_id)\n      `).eq('sales_transactions.branch_id', branchId).gte('sales_transactions.created_at', start).lte('sales_transactions.created_at', end);\n        if (error) throw error;\n        // Aggregate by menu item\n        const itemStats = data.reduce((acc, item)=>{\n            const itemName = item.menu_item.name;\n            if (!acc[itemName]) {\n                acc[itemName] = {\n                    name: itemName,\n                    category: item.menu_item.category,\n                    totalQuantity: 0,\n                    totalRevenue: 0\n                };\n            }\n            acc[itemName].totalQuantity += item.quantity;\n            acc[itemName].totalRevenue += item.total_price;\n            return acc;\n        }, {});\n        return Object.values(itemStats).sort((a, b)=>b.totalQuantity - a.totalQuantity).slice(0, limit);\n    },\n    // Alternative approach for top selling items using RPC if the above doesn't work\n    async getTopSellingItemsRPC (branchId, limit = 10, startDate, endDate) {\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Call a stored procedure/function for complex aggregation\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('get_top_selling_items', {\n            branch_id: branchId,\n            start_date: start,\n            end_date: end,\n            item_limit: limit\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://klgxgxjhjnojngrtcloe.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtsZ3hneGpoam5vam5ncnRjbG9lIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE1MzQ0NDAsImV4cCI6MjA2NzExMDQ0MH0.5F7hUxdV4XPRyfTrCYbCDXLQ6jzDiroSClLj1IcQj1s\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBRXBELE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUUxRCxNQUFNSyxXQUFXUCxtRUFBWUEsQ0FBQ0MsYUFBYUksaUJBQWdCIiwic291cmNlcyI6WyJEOlxccGFkYW5naHViX3N1cGFiYXNlXFxwYWRhbmctcmVzdGF1cmFudC1zeXN0ZW1cXHNyY1xcbGliXFxzdXBhYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnXG5cbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIVxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhXG5cbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VBbm9uS2V5KVxuXG4vLyBUeXBlcyBmb3Igb3VyIGRhdGFiYXNlXG5leHBvcnQgdHlwZSBVc2VyUm9sZSA9ICdhZG1pbicgfCAnbWFuYWdlcicgfCAnc3RhZmYnIHwgJ2Nhc2hpZXInXG5cbmV4cG9ydCBpbnRlcmZhY2UgUHJvZmlsZSB7XG4gIGlkOiBzdHJpbmdcbiAgZW1haWw6IHN0cmluZ1xuICBmdWxsX25hbWU6IHN0cmluZ1xuICByb2xlOiBVc2VyUm9sZVxuICBicmFuY2hfaWQ/OiBzdHJpbmdcbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEJyYW5jaCB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIGFkZHJlc3M6IHN0cmluZ1xuICBwaG9uZTogc3RyaW5nXG4gIG1hbmFnZXJfaWQ/OiBzdHJpbmdcbiAgaXNfYWN0aXZlOiBib29sZWFuXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBXYXJlaG91c2Uge1xuICBpZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBhZGRyZXNzOiBzdHJpbmdcbiAgdHlwZTogJ2NlbnRyYWwnIHwgJ2JyYW5jaCdcbiAgYnJhbmNoX2lkPzogc3RyaW5nXG4gIGlzX2FjdGl2ZTogYm9vbGVhblxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgS2l0Y2hlbiB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIHR5cGU6ICdjZW50cmFsJyB8ICdicmFuY2gnXG4gIGJyYW5jaF9pZD86IHN0cmluZ1xuICB3YXJlaG91c2VfaWQ6IHN0cmluZ1xuICBpc19hY3RpdmU6IGJvb2xlYW5cbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZ1xufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwic3VwYWJhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/@swc","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpos%2Fpage&page=%2Fpos%2Fpage&appPaths=%2Fpos%2Fpage&pagePath=private-next-app-dir%2Fpos%2Fpage.tsx&appDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpadanghub_supabase%5Cpadang-restaurant-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();