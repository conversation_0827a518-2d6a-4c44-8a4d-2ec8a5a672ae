-- Enhanced inventory schema for proper finished goods tracking

-- Finished goods inventory (separate from raw ingredients)
CREATE TABLE finished_goods_inventory (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    warehouse_id UUID REFERENCES warehouses(id) NOT NULL,
    menu_item_id UUID REFERENCES menu_items(id) NOT NULL,
    current_stock DECIMAL(10,3) DEFAULT 0,
    minimum_stock DECIMAL(10,3) DEFAULT 0,
    maximum_stock DECIMAL(10,3),
    reorder_point DECIMAL(10,3),
    unit TEXT NOT NULL DEFAULT 'portions', -- portions, servings, pieces
    cost_per_unit DECIMAL(10,2),
    last_produced_at TIMESTAMP WITH TIME ZONE,
    expiry_date TIMESTAMP WITH TIME ZONE,
    batch_number TEXT,
    production_batch_id UUID REFERENCES production_batches(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(warehouse_id, menu_item_id, batch_number)
);

-- Enhanced stock movements to handle both raw ingredients and finished goods
CREATE TABLE enhanced_stock_movements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    warehouse_id UUID REFERENCES warehouses(id) NOT NULL,
    item_type TEXT NOT NULL CHECK (item_type IN ('raw_ingredient', 'finished_goods')),
    item_id UUID NOT NULL, -- ingredient_id or menu_item_id depending on item_type
    movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'transfer', 'adjustment', 'waste', 'production_input', 'production_output')),
    quantity DECIMAL(10,3) NOT NULL,
    unit TEXT NOT NULL,
    reference_type TEXT, -- 'purchase', 'production', 'transfer', 'sale', 'waste', 'adjustment'
    reference_id UUID, -- ID of related record
    batch_number TEXT,
    cost_per_unit DECIMAL(10,2),
    total_cost DECIMAL(10,2),
    notes TEXT,
    performed_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Menu item availability cache for performance
CREATE TABLE menu_item_availability_cache (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    branch_id UUID REFERENCES branches(id) NOT NULL,
    menu_item_id UUID REFERENCES menu_items(id) NOT NULL,
    is_available BOOLEAN DEFAULT true,
    availability_reason TEXT CHECK (availability_reason IN ('in_stock', 'out_of_stock', 'low_stock', 'quota_reached', 'manually_disabled', 'missing_ingredients')),
    current_finished_stock DECIMAL(10,3) DEFAULT 0,
    can_produce BOOLEAN DEFAULT false,
    missing_ingredients TEXT[],
    estimated_production_time INTEGER, -- minutes
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(branch_id, menu_item_id)
);

-- Production to finished goods mapping
CREATE TABLE production_finished_goods_output (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    production_batch_id UUID REFERENCES production_batches(id) ON DELETE CASCADE,
    menu_item_id UUID REFERENCES menu_items(id) NOT NULL,
    warehouse_id UUID REFERENCES warehouses(id) NOT NULL,
    quantity_produced DECIMAL(10,3) NOT NULL,
    unit TEXT NOT NULL DEFAULT 'portions',
    cost_per_unit DECIMAL(10,2),
    total_cost DECIMAL(10,2),
    expiry_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_finished_goods_warehouse_menu ON finished_goods_inventory(warehouse_id, menu_item_id);
CREATE INDEX idx_finished_goods_stock_level ON finished_goods_inventory(current_stock);
CREATE INDEX idx_enhanced_stock_movements_warehouse ON enhanced_stock_movements(warehouse_id);
CREATE INDEX idx_enhanced_stock_movements_item ON enhanced_stock_movements(item_type, item_id);
CREATE INDEX idx_enhanced_stock_movements_reference ON enhanced_stock_movements(reference_type, reference_id);
CREATE INDEX idx_menu_availability_cache_branch ON menu_item_availability_cache(branch_id);
CREATE INDEX idx_menu_availability_cache_available ON menu_item_availability_cache(is_available);

-- Functions for automatic availability updates

-- Function to update menu availability when finished goods inventory changes
CREATE OR REPLACE FUNCTION update_menu_availability_on_stock_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Update availability cache when finished goods inventory changes
    INSERT INTO menu_item_availability_cache (
        branch_id, 
        menu_item_id, 
        is_available, 
        availability_reason, 
        current_finished_stock,
        last_updated
    )
    SELECT 
        w.branch_id,
        NEW.menu_item_id,
        CASE 
            WHEN NEW.current_stock > 0 THEN true
            ELSE false
        END,
        CASE 
            WHEN NEW.current_stock > NEW.minimum_stock THEN 'in_stock'
            WHEN NEW.current_stock > 0 THEN 'low_stock'
            ELSE 'out_of_stock'
        END,
        NEW.current_stock,
        NOW()
    FROM warehouses w
    WHERE w.id = NEW.warehouse_id
    ON CONFLICT (branch_id, menu_item_id) 
    DO UPDATE SET
        is_available = EXCLUDED.is_available,
        availability_reason = EXCLUDED.availability_reason,
        current_finished_stock = EXCLUDED.current_finished_stock,
        last_updated = EXCLUDED.last_updated;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update availability when finished goods inventory changes
CREATE TRIGGER trigger_update_menu_availability
    AFTER INSERT OR UPDATE ON finished_goods_inventory
    FOR EACH ROW
    EXECUTE FUNCTION update_menu_availability_on_stock_change();

-- Function to check and update daily quota availability
CREATE OR REPLACE FUNCTION update_daily_quota_availability()
RETURNS TRIGGER AS $$
BEGIN
    -- Update availability cache when daily sales change
    UPDATE menu_item_availability_cache 
    SET 
        is_available = CASE 
            WHEN NEW.daily_quota IS NOT NULL AND NEW.current_sold >= NEW.daily_quota THEN false
            WHEN availability_reason = 'quota_reached' AND NEW.current_sold < NEW.daily_quota THEN true
            ELSE is_available
        END,
        availability_reason = CASE 
            WHEN NEW.daily_quota IS NOT NULL AND NEW.current_sold >= NEW.daily_quota THEN 'quota_reached'
            WHEN availability_reason = 'quota_reached' AND NEW.current_sold < NEW.daily_quota THEN 'in_stock'
            ELSE availability_reason
        END,
        last_updated = NOW()
    WHERE branch_id = NEW.branch_id AND menu_item_id = NEW.menu_item_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update availability when daily sales change
CREATE TRIGGER trigger_update_quota_availability
    AFTER UPDATE ON branch_menu_pricing
    FOR EACH ROW
    WHEN (OLD.current_sold IS DISTINCT FROM NEW.current_sold)
    EXECUTE FUNCTION update_daily_quota_availability();

-- View for comprehensive menu item availability
CREATE VIEW menu_item_availability_view AS
SELECT 
    bmp.branch_id,
    bmp.menu_item_id,
    mi.name as menu_item_name,
    mi.category,
    bmp.price,
    bmp.is_available as manually_available,
    bmp.daily_quota,
    bmp.current_sold,
    COALESCE(mac.is_available, false) as system_available,
    COALESCE(mac.availability_reason, 'unknown') as availability_reason,
    COALESCE(mac.current_finished_stock, 0) as finished_stock,
    COALESCE(mac.can_produce, false) as can_produce,
    COALESCE(mac.missing_ingredients, ARRAY[]::TEXT[]) as missing_ingredients,
    COALESCE(mac.estimated_production_time, 0) as estimated_production_time,
    (bmp.is_available AND COALESCE(mac.is_available, false)) as final_availability
FROM branch_menu_pricing bmp
JOIN menu_items mi ON mi.id = bmp.menu_item_id
LEFT JOIN menu_item_availability_cache mac ON mac.branch_id = bmp.branch_id AND mac.menu_item_id = bmp.menu_item_id
WHERE mi.is_available = true;

-- Migration script to populate finished goods inventory from existing data
-- This would be run once to migrate existing production data

/*
INSERT INTO finished_goods_inventory (
    warehouse_id,
    menu_item_id,
    current_stock,
    minimum_stock,
    maximum_stock,
    reorder_point,
    cost_per_unit,
    last_produced_at,
    production_batch_id
)
SELECT DISTINCT
    k.warehouse_id,
    mi.id as menu_item_id,
    COALESCE(SUM(pb.actual_quantity), 0) as current_stock,
    10 as minimum_stock, -- Default minimum
    100 as maximum_stock, -- Default maximum
    20 as reorder_point, -- Default reorder point
    COALESCE(AVG(pc.cost_per_unit), 0) as cost_per_unit,
    MAX(pb.actual_end_time) as last_produced_at,
    MAX(pb.id) as production_batch_id
FROM menu_items mi
JOIN recipes r ON r.id = mi.recipe_id
JOIN production_batches pb ON pb.recipe_id = r.id AND pb.status = 'completed'
JOIN kitchens k ON k.id = pb.kitchen_id
LEFT JOIN production_costs pc ON pc.batch_id = pb.id
GROUP BY k.warehouse_id, mi.id;
*/
