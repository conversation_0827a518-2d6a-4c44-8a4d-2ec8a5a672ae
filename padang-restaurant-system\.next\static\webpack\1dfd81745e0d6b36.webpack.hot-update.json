{"c": ["app/layout", "app/inventory/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpadanghub_supabase%5C%5Cpadang-restaurant-system%5C%5Csrc%5C%5Capp%5C%5Cinventory%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/inventory/page.tsx", "(app-pages-browser)/./src/components/inventory/AddStockModal.tsx", "(app-pages-browser)/./src/components/inventory/BulkUpdateModal.tsx", "(app-pages-browser)/./src/components/inventory/TransferStockModal.tsx", "(app-pages-browser)/./src/components/inventory/UpdateStockModal.tsx"]}