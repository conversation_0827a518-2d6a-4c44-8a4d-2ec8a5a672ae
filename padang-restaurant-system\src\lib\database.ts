import { supabase } from './supabase'
import { logStockChange, logTransferOperation, logBulkOperation } from './auditLog'

// Inventory Management Functions
export const inventoryService = {
  // Get all inventory items for a warehouse
  async getWarehouseInventory(warehouseId: string) {
    const { data, error } = await supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*)
      `)
      .eq('warehouse_id', warehouseId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Get low stock items across all warehouses (optimized with database filtering)
  async getLowStockItems(branchId?: string) {
    // Use a more efficient query with database-side filtering
    const { data, error } = await supabase
      .rpc('get_low_stock_items_optimized', {
        branch_id_param: branchId || null
      })

    if (error) {
      // Fallback to client-side filtering if RPC function doesn't exist
      console.warn('RPC function not available, using fallback query')
      return this.getLowStockItemsFallback(branchId)
    }

    return data || []
  },

  // Fallback method for low stock items
  async getLowStockItemsFallback(branchId?: string) {
    let query = supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*)
      `)
      .filter('current_stock', 'lt', 'minimum_stock') // Database-side filtering

    if (branchId) {
      query = query.eq('warehouses.branch_id', branchId)
    }

    const { data, error } = await query
      .order('current_stock', { ascending: true })
      .limit(100) // Limit results for performance

    if (error) throw error

    // Filter low stock items in JavaScript
    // const lowStockItems = data?.filter(item =>
    //   item.current_stock < item.minimum_stock
    // ).sort((a, b) => a.current_stock - b.current_stock)
    // return lowStockItems

    return data || []
  },

  // async getLowStockItems(branchId?: string) {
  //   let query = supabase
  //     .from('inventory')
  //     .select(`
  //       *,
  //       ingredient:ingredients(*),
  //       warehouse:warehouses(*)
  //     `)
  //     .filter('current_stock', 'lt', 10)
  
  //   if (branchId) {
  //     query = query.eq('warehouses.branch_id', branchId)
  //   }
  
  //   const { data, error } = await query.order('current_stock', { ascending: true })
  
  //   if (error) throw error
  //   return data
  // },

  // Update stock levels with enhanced validation and transaction safety
  async updateStock(inventoryId: string, newStock: number, movementType: string, notes?: string, performedBy?: string) {
    // Input validation
    if (!inventoryId || newStock < 0) {
      throw new Error('Invalid input: inventory ID is required and stock cannot be negative')
    }

    if (!['in', 'out', 'adjustment', 'waste'].includes(movementType)) {
      throw new Error('Invalid movement type')
    }

    // Fetch current inventory with detailed information
    const { data: inventory, error: fetchError } = await supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(unit, name),
        warehouse:warehouses(name, code)
      `)
      .eq('id', inventoryId)
      .single()

    if (fetchError) {
      throw new Error(`Failed to fetch inventory: ${fetchError.message}`)
    }

    const quantity = newStock - inventory.current_stock
    const isIncrease = quantity > 0

    // Business rule validation
    if (newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {
      throw new Error(`Stock cannot exceed maximum limit of ${inventory.maximum_stock} ${inventory.ingredient?.unit}`)
    }

    // Check if this would create negative stock for outbound movements
    if (movementType === 'out' && newStock < 0) {
      throw new Error('Cannot reduce stock below zero')
    }

    try {
      // Use a transaction-like approach with error handling
      const timestamp = new Date().toISOString()

      // Update inventory record
      const { error: updateError } = await supabase
        .from('inventory')
        .update({
          current_stock: newStock,
          updated_at: timestamp,
          last_restocked_at: isIncrease ? timestamp : inventory.last_restocked_at
        })
        .eq('id', inventoryId)

      if (updateError) {
        throw new Error(`Failed to update inventory: ${updateError.message}`)
      }

      // Record stock movement with enhanced details
      const { error: movementError } = await supabase
        .from('stock_movements')
        .insert({
          warehouse_id: inventory.warehouse_id,
          ingredient_id: inventory.ingredient_id,
          movement_type: movementType,
          quantity: Math.abs(quantity),
          unit: inventory.ingredient?.unit || 'kg',
          reference_type: 'manual_adjustment',
          notes: notes || `Stock ${movementType} - ${inventory.ingredient?.name} at ${inventory.warehouse?.name}`,
          performed_by: performedBy,
          created_at: timestamp
        })

      if (movementError) {
        // Attempt to rollback the inventory update
        await supabase
          .from('inventory')
          .update({
            current_stock: inventory.current_stock,
            updated_at: inventory.updated_at
          })
          .eq('id', inventoryId)

        throw new Error(`Failed to record stock movement: ${movementError.message}`)
      }

      // Log the stock change for audit trail
      if (performedBy) {
        try {
          await logStockChange(
            movementType as 'stock_add' | 'stock_reduce' | 'stock_adjust' | 'stock_waste',
            inventoryId,
            performedBy,
            'staff', // Default role, should be passed from context
            {
              ingredient_name: inventory.ingredient?.name || 'Unknown',
              warehouse_name: inventory.warehouse?.name || 'Unknown',
              previous_stock: inventory.current_stock,
              new_stock: newStock,
              quantity_changed: Math.abs(quantity),
              unit: inventory.ingredient?.unit || 'kg',
              notes: notes
            },
            inventory.warehouse_id
          )
        } catch (auditError) {
          console.error('Failed to log stock change:', auditError)
          // Don't fail the operation if audit logging fails
        }
      }

      return {
        success: true,
        previousStock: inventory.current_stock,
        newStock: newStock,
        quantity: Math.abs(quantity),
        movementType,
        timestamp
      }
    } catch (error) {
      throw new Error(`Stock update failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Create new inventory record
  async createInventoryRecord(warehouseId: string, ingredientId: string, initialStock: number, notes?: string) {
    const { data, error } = await supabase
      .from('inventory')
      .insert({
        warehouse_id: warehouseId,
        ingredient_id: ingredientId,
        current_stock: initialStock,
        minimum_stock: 0,
        maximum_stock: initialStock * 10, // Default to 10x initial stock
        reorder_point: initialStock * 0.2, // Default to 20% of initial stock
      })
      .select()
      .single()

    if (error) throw error

    // Record initial stock movement
    const { data: ingredient } = await supabase
      .from('ingredients')
      .select('unit')
      .eq('id', ingredientId)
      .single()

    await supabase
      .from('stock_movements')
      .insert({
        warehouse_id: warehouseId,
        ingredient_id: ingredientId,
        movement_type: 'in',
        quantity: initialStock,
        unit: ingredient?.unit || 'kg',
        reference_type: 'initial_stock',
        notes: notes || 'Initial stock entry'
      })

    return data
  },

  // Get stock movements for a warehouse
  async getStockMovements(warehouseId: string, limit = 50) {
    const { data, error } = await supabase
      .from('stock_movements')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*),
        performer:profiles(full_name)
      `)
      .eq('warehouse_id', warehouseId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  },

  // Create warehouse transfer
  async createTransfer(fromWarehouseId: string, toWarehouseId: string, items: any[], requestedBy: string, notes?: string) {
    // Create transfer record
    const { data: transfer, error: transferError } = await supabase
      .from('warehouse_transfers')
      .insert({
        from_warehouse_id: fromWarehouseId,
        to_warehouse_id: toWarehouseId,
        requested_by: requestedBy,
        total_items: items.length,
        notes,
        status: 'pending'
      })
      .select()
      .single()

    if (transferError) throw transferError

    // Create transfer items
    const transferItems = items.map(item => ({
      transfer_id: transfer.id,
      ingredient_id: item.ingredient_id,
      requested_quantity: item.quantity,
      unit: item.unit,
      notes: item.notes
    }))

    const { error: itemsError } = await supabase
      .from('transfer_items')
      .insert(transferItems)

    if (itemsError) throw itemsError

    return transfer
  },

  // Get pending transfers
  async getPendingTransfers(warehouseId?: string) {
    let query = supabase
      .from('warehouse_transfers')
      .select(`
        *,
        from_warehouse:warehouses!from_warehouse_id(*),
        to_warehouse:warehouses!to_warehouse_id(*),
        requester:profiles!requested_by(*),
        transfer_items(*, ingredient:ingredients(*))
      `)
      .eq('status', 'pending')

    if (warehouseId) {
      query = query.or(`from_warehouse_id.eq.${warehouseId},to_warehouse_id.eq.${warehouseId}`)
    }

    const { data, error } = await query.order('requested_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Approve transfer with enhanced validation
  async approveTransfer(transferId: string, approvedBy: string, approvedItems: any[], autoComplete: boolean = false) {
    try {
      // Validate transfer exists and is in pending status
      const { data: transfer, error: fetchError } = await supabase
        .from('warehouse_transfers')
        .select(`
          *,
          from_warehouse:warehouses!from_warehouse_id(*),
          to_warehouse:warehouses!to_warehouse_id(*),
          transfer_items(*, ingredient:ingredients(*))
        `)
        .eq('id', transferId)
        .single()

      if (fetchError) {
        throw new Error(`Transfer not found: ${fetchError.message}`)
      }

      if (transfer.status !== 'pending') {
        throw new Error(`Cannot approve transfer with status: ${transfer.status}`)
      }

      // Validate stock availability for approved quantities
      for (const approvedItem of approvedItems) {
        const transferItem = transfer.transfer_items.find((item: any) => item.id === approvedItem.id)
        if (!transferItem) {
          throw new Error(`Transfer item not found: ${approvedItem.id}`)
        }

        // Check current stock in source warehouse
        const { data: inventory, error: inventoryError } = await supabase
          .from('inventory')
          .select('current_stock')
          .eq('warehouse_id', transfer.from_warehouse_id)
          .eq('ingredient_id', transferItem.ingredient_id)
          .single()

        if (inventoryError) {
          throw new Error(`Cannot verify stock for ingredient: ${transferItem.ingredient.name}`)
        }

        if (inventory.current_stock < approvedItem.approved_quantity) {
          throw new Error(`Insufficient stock for ${transferItem.ingredient.name}. Available: ${inventory.current_stock}, Requested: ${approvedItem.approved_quantity}`)
        }
      }

      const timestamp = new Date().toISOString()

      // Update transfer status
      const { error: transferError } = await supabase
        .from('warehouse_transfers')
        .update({
          status: 'approved',
          approved_by: approvedBy,
          approved_at: timestamp
        })
        .eq('id', transferId)

      if (transferError) {
        throw new Error(`Failed to approve transfer: ${transferError.message}`)
      }

      // Update approved quantities for items
      for (const item of approvedItems) {
        const { error: itemError } = await supabase
          .from('transfer_items')
          .update({
            approved_quantity: item.approved_quantity
          })
          .eq('id', item.id)

        if (itemError) {
          throw new Error(`Failed to update approved quantity: ${itemError.message}`)
        }
      }

      // Auto-complete the transfer if requested
      if (autoComplete) {
        try {
          const completionResult = await this.completeTransfer(transferId, approvedBy)
          return {
            success: true,
            transferId,
            approvedAt: timestamp,
            approvedItems: approvedItems.length,
            autoCompleted: true,
            completionResult
          }
        } catch (completionError) {
          // If auto-completion fails, log the error but don't fail the approval
          console.error('Auto-completion failed after approval:', completionError)
          return {
            success: true,
            transferId,
            approvedAt: timestamp,
            approvedItems: approvedItems.length,
            autoCompleted: false,
            autoCompletionError: completionError instanceof Error ? completionError.message : 'Unknown error'
          }
        }
      }

      return {
        success: true,
        transferId,
        approvedAt: timestamp,
        approvedItems: approvedItems.length,
        autoCompleted: false
      }

    } catch (error) {
      throw new Error(`Transfer approval failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Complete transfer and update inventory levels
  async completeTransfer(transferId: string, completedBy: string) {
    try {
      // Fetch transfer with all details
      const { data: transfer, error: fetchError } = await supabase
        .from('warehouse_transfers')
        .select(`
          *,
          from_warehouse:warehouses!from_warehouse_id(*),
          to_warehouse:warehouses!to_warehouse_id(*),
          transfer_items(*, ingredient:ingredients(*))
        `)
        .eq('id', transferId)
        .single()

      if (fetchError) {
        throw new Error(`Transfer not found: ${fetchError.message}`)
      }

      if (transfer.status !== 'approved' && transfer.status !== 'in_transit') {
        throw new Error(`Cannot complete transfer with status: ${transfer.status}`)
      }

      const timestamp = new Date().toISOString()
      const stockMovements: any[] = []

      // Process each transfer item
      for (const item of transfer.transfer_items) {
        const approvedQty = item.approved_quantity || item.requested_quantity

        // Reduce stock from source warehouse
        const { data: sourceInventory, error: sourceError } = await supabase
          .from('inventory')
          .select('*')
          .eq('warehouse_id', transfer.from_warehouse_id)
          .eq('ingredient_id', item.ingredient_id)
          .single()

        if (sourceError) {
          throw new Error(`Source inventory not found for ${item.ingredient.name}`)
        }

        if (sourceInventory.current_stock < approvedQty) {
          throw new Error(`Insufficient stock for ${item.ingredient.name}. Available: ${sourceInventory.current_stock}, Required: ${approvedQty}`)
        }

        // Update source inventory
        const { error: sourceUpdateError } = await supabase
          .from('inventory')
          .update({
            current_stock: sourceInventory.current_stock - approvedQty,
            updated_at: timestamp
          })
          .eq('id', sourceInventory.id)

        if (sourceUpdateError) {
          throw new Error(`Failed to update source inventory: ${sourceUpdateError.message}`)
        }

        // Add stock movement for source (outbound)
        stockMovements.push({
          warehouse_id: transfer.from_warehouse_id,
          ingredient_id: item.ingredient_id,
          movement_type: 'transfer',
          quantity: approvedQty,
          unit: item.unit,
          reference_type: 'transfer_out',
          reference_id: transferId,
          notes: `Transfer out to ${transfer.to_warehouse.name} - ${item.ingredient.name}`,
          performed_by: completedBy,
          created_at: timestamp
        })

        // Check if destination inventory exists
        const { data: destInventory, error: destFetchError } = await supabase
          .from('inventory')
          .select('*')
          .eq('warehouse_id', transfer.to_warehouse_id)
          .eq('ingredient_id', item.ingredient_id)
          .maybeSingle()

        if (destFetchError) {
          throw new Error(`Error checking destination inventory: ${destFetchError.message}`)
        }

        if (destInventory) {
          // Update existing destination inventory
          const { error: destUpdateError } = await supabase
            .from('inventory')
            .update({
              current_stock: destInventory.current_stock + approvedQty,
              updated_at: timestamp,
              last_restocked_at: timestamp
            })
            .eq('id', destInventory.id)

          if (destUpdateError) {
            throw new Error(`Failed to update destination inventory: ${destUpdateError.message}`)
          }
        } else {
          // Create new destination inventory record
          const { error: destCreateError } = await supabase
            .from('inventory')
            .insert({
              warehouse_id: transfer.to_warehouse_id,
              ingredient_id: item.ingredient_id,
              current_stock: approvedQty,
              minimum_stock: 0,
              maximum_stock: approvedQty * 10,
              reorder_point: approvedQty * 0.2,
              last_restocked_at: timestamp
            })

          if (destCreateError) {
            throw new Error(`Failed to create destination inventory: ${destCreateError.message}`)
          }
        }

        // Add stock movement for destination (inbound)
        stockMovements.push({
          warehouse_id: transfer.to_warehouse_id,
          ingredient_id: item.ingredient_id,
          movement_type: 'transfer',
          quantity: approvedQty,
          unit: item.unit,
          reference_type: 'transfer_in',
          reference_id: transferId,
          notes: `Transfer in from ${transfer.from_warehouse.name} - ${item.ingredient.name}`,
          performed_by: completedBy,
          created_at: timestamp
        })
      }

      // Insert all stock movements
      const { error: movementError } = await supabase
        .from('stock_movements')
        .insert(stockMovements)

      if (movementError) {
        throw new Error(`Failed to record stock movements: ${movementError.message}`)
      }

      // Update transfer status to completed
      const { error: completeError } = await supabase
        .from('warehouse_transfers')
        .update({
          status: 'completed',
          completed_at: timestamp
        })
        .eq('id', transferId)

      if (completeError) {
        throw new Error(`Failed to complete transfer: ${completeError.message}`)
      }

      return {
        success: true,
        transferId,
        completedAt: timestamp,
        itemsTransferred: transfer.transfer_items.length,
        stockMovements: stockMovements.length
      }

    } catch (error) {
      throw new Error(`Transfer completion failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Cancel transfer
  async cancelTransfer(transferId: string, cancelledBy: string, reason?: string) {
    try {
      // Validate transfer exists and can be cancelled
      const { data: transfer, error: fetchError } = await supabase
        .from('warehouse_transfers')
        .select('*')
        .eq('id', transferId)
        .single()

      if (fetchError) {
        throw new Error(`Transfer not found: ${fetchError.message}`)
      }

      if (!['pending', 'approved', 'in_transit'].includes(transfer.status)) {
        throw new Error(`Cannot cancel transfer with status: ${transfer.status}`)
      }

      const timestamp = new Date().toISOString()

      // Update transfer status
      const { error: cancelError } = await supabase
        .from('warehouse_transfers')
        .update({
          status: 'cancelled',
          notes: transfer.notes ? `${transfer.notes}\n\nCancelled: ${reason || 'No reason provided'}` : `Cancelled: ${reason || 'No reason provided'}`,
          completed_at: timestamp
        })
        .eq('id', transferId)

      if (cancelError) {
        throw new Error(`Failed to cancel transfer: ${cancelError.message}`)
      }

      return {
        success: true,
        transferId,
        cancelledAt: timestamp,
        reason: reason || 'No reason provided'
      }

    } catch (error) {
      throw new Error(`Transfer cancellation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Bulk update stock levels for multiple items
  async bulkUpdateStock(updates: Array<{
    inventoryId: string;
    newStock: number;
    movementType: string;
    notes?: string;
  }>, performedBy?: string) {
    if (!updates || updates.length === 0) {
      throw new Error('No updates provided')
    }

    const results: Array<{
      inventoryId: string;
      success: boolean;
      error?: string;
      previousStock?: number;
      newStock?: number;
    }> = []

    const timestamp = new Date().toISOString()
    const stockMovements: any[] = []

    try {
      // Process each update
      for (const update of updates) {
        try {
          // Validate input
          if (!update.inventoryId || update.newStock < 0) {
            results.push({
              inventoryId: update.inventoryId,
              success: false,
              error: 'Invalid input: inventory ID is required and stock cannot be negative'
            })
            continue
          }

          // Fetch current inventory
          const { data: inventory, error: fetchError } = await supabase
            .from('inventory')
            .select(`
              *,
              ingredient:ingredients(unit, name),
              warehouse:warehouses(name, code)
            `)
            .eq('id', update.inventoryId)
            .single()

          if (fetchError) {
            results.push({
              inventoryId: update.inventoryId,
              success: false,
              error: `Failed to fetch inventory: ${fetchError.message}`
            })
            continue
          }

          const quantity = update.newStock - inventory.current_stock

          // Business rule validation
          if (update.newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {
            results.push({
              inventoryId: update.inventoryId,
              success: false,
              error: `Stock cannot exceed maximum limit of ${inventory.maximum_stock} ${inventory.ingredient?.unit}`
            })
            continue
          }

          // Update inventory
          const { error: updateError } = await supabase
            .from('inventory')
            .update({
              current_stock: update.newStock,
              updated_at: timestamp,
              last_restocked_at: quantity > 0 ? timestamp : inventory.last_restocked_at
            })
            .eq('id', update.inventoryId)

          if (updateError) {
            results.push({
              inventoryId: update.inventoryId,
              success: false,
              error: `Failed to update inventory: ${updateError.message}`
            })
            continue
          }

          // Prepare stock movement record
          stockMovements.push({
            warehouse_id: inventory.warehouse_id,
            ingredient_id: inventory.ingredient_id,
            movement_type: update.movementType,
            quantity: Math.abs(quantity),
            unit: inventory.ingredient?.unit || 'kg',
            reference_type: 'bulk_adjustment',
            notes: update.notes || `Bulk stock ${update.movementType} - ${inventory.ingredient?.name}`,
            performed_by: performedBy,
            created_at: timestamp
          })

          results.push({
            inventoryId: update.inventoryId,
            success: true,
            previousStock: inventory.current_stock,
            newStock: update.newStock
          })

        } catch (error) {
          results.push({
            inventoryId: update.inventoryId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      // Insert all stock movements in batch
      if (stockMovements.length > 0) {
        const { error: movementError } = await supabase
          .from('stock_movements')
          .insert(stockMovements)

        if (movementError) {
          throw new Error(`Failed to record stock movements: ${movementError.message}`)
        }
      }

      const successCount = results.filter(r => r.success).length
      const failureCount = results.filter(r => !r.success).length

      return {
        success: failureCount === 0,
        totalUpdates: updates.length,
        successCount,
        failureCount,
        results,
        timestamp
      }

    } catch (error) {
      throw new Error(`Bulk update failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  },

  // Get comprehensive low stock alerts with reorder suggestions
  async getLowStockAlertsWithReorderSuggestions(branchId?: string) {
    let query = supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(*),
        warehouse:warehouses(*)
      `)

    if (branchId) {
      query = query.eq('warehouses.branch_id', branchId)
    }

    const { data, error } = await query

    if (error) throw error

    // Process and categorize alerts
    const alerts = data?.map(item => {
      const stockLevel = item.current_stock
      const minStock = item.minimum_stock
      const reorderPoint = item.reorder_point
      const maxStock = item.maximum_stock

      let alertLevel: 'critical' | 'low' | 'reorder' | 'good' = 'good'
      let suggestedReorderQuantity = 0
      let daysUntilStockout = null

      // Determine alert level
      if (stockLevel <= 0) {
        alertLevel = 'critical'
      } else if (stockLevel <= minStock) {
        alertLevel = 'critical'
      } else if (stockLevel <= reorderPoint) {
        alertLevel = 'reorder'
      } else if (stockLevel <= minStock * 1.5) {
        alertLevel = 'low'
      }

      // Calculate suggested reorder quantity
      if (alertLevel !== 'good') {
        const targetStock = maxStock || (minStock * 3)
        suggestedReorderQuantity = Math.max(0, targetStock - stockLevel)
      }

      // Estimate days until stockout (simplified calculation)
      // In a real system, you'd use historical consumption data
      if (stockLevel > 0 && minStock > 0) {
        const avgDailyConsumption = minStock / 30 // Rough estimate
        if (avgDailyConsumption > 0) {
          daysUntilStockout = Math.floor(stockLevel / avgDailyConsumption)
        }
      }

      return {
        ...item,
        alertLevel,
        suggestedReorderQuantity,
        daysUntilStockout,
        stockPercentage: maxStock > 0 ? (stockLevel / maxStock) * 100 : 0
      }
    }).filter(item => item.alertLevel !== 'good')
      .sort((a, b) => {
        // Sort by alert level priority, then by days until stockout
        const alertPriority = { critical: 0, low: 1, reorder: 2, good: 3 }
        const aPriority = alertPriority[a.alertLevel]
        const bPriority = alertPriority[b.alertLevel]

        if (aPriority !== bPriority) {
          return aPriority - bPriority
        }

        // If same alert level, sort by days until stockout (ascending)
        if (a.daysUntilStockout !== null && b.daysUntilStockout !== null) {
          return a.daysUntilStockout - b.daysUntilStockout
        }

        return 0
      })

    return alerts || []
  },

  // Get inventory performance metrics
  async getInventoryMetrics(warehouseId?: string, branchId?: string) {
    let query = supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(cost_per_unit),
        warehouse:warehouses(branch_id)
      `)

    if (warehouseId) {
      query = query.eq('warehouse_id', warehouseId)
    } else if (branchId) {
      query = query.eq('warehouses.branch_id', branchId)
    }

    const { data, error } = await query

    if (error) throw error

    if (!data || data.length === 0) {
      return {
        totalItems: 0,
        totalValue: 0,
        lowStockCount: 0,
        criticalStockCount: 0,
        reorderNeededCount: 0,
        averageStockLevel: 0,
        turnoverRate: 0
      }
    }

    const totalItems = data.length
    const totalValue = data.reduce((sum, item) => {
      const cost = item.ingredient?.cost_per_unit || 0
      return sum + (item.current_stock * cost)
    }, 0)

    const lowStockCount = data.filter(item =>
      item.current_stock <= item.minimum_stock && item.current_stock > 0
    ).length

    const criticalStockCount = data.filter(item =>
      item.current_stock <= 0
    ).length

    const reorderNeededCount = data.filter(item =>
      item.current_stock <= item.reorder_point
    ).length

    const averageStockLevel = data.reduce((sum, item) => {
      const maxStock = item.maximum_stock || item.minimum_stock * 3
      const stockPercentage = maxStock > 0 ? (item.current_stock / maxStock) * 100 : 0
      return sum + stockPercentage
    }, 0) / totalItems

    return {
      totalItems,
      totalValue,
      lowStockCount,
      criticalStockCount,
      reorderNeededCount,
      averageStockLevel: Math.round(averageStockLevel),
      turnoverRate: 0 // Would need historical data to calculate
    }
  },

  // Generate automatic reorder suggestions
  async generateReorderSuggestions(warehouseId?: string, branchId?: string) {
    const alerts = await this.getLowStockAlertsWithReorderSuggestions(branchId)

    const suggestions = alerts
      .filter(item => item.suggestedReorderQuantity > 0)
      .map(item => ({
        inventoryId: item.id,
        ingredientId: item.ingredient.id,
        ingredientName: item.ingredient.name,
        ingredientCode: item.ingredient.code,
        warehouseId: item.warehouse_id,
        warehouseName: item.warehouse.name,
        currentStock: item.current_stock,
        minimumStock: item.minimum_stock,
        reorderPoint: item.reorder_point,
        maximumStock: item.maximum_stock,
        suggestedQuantity: item.suggestedReorderQuantity,
        estimatedCost: (item.ingredient.cost_per_unit || 0) * item.suggestedReorderQuantity,
        priority: item.alertLevel,
        daysUntilStockout: item.daysUntilStockout,
        unit: item.ingredient.unit
      }))
      .sort((a, b) => {
        const priorityOrder = { critical: 0, low: 1, reorder: 2 }
        return priorityOrder[a.priority as keyof typeof priorityOrder] -
               priorityOrder[b.priority as keyof typeof priorityOrder]
      })

    const totalEstimatedCost = suggestions.reduce((sum, item) => sum + item.estimatedCost, 0)
    const criticalCount = suggestions.filter(item => item.priority === 'critical').length
    const lowCount = suggestions.filter(item => item.priority === 'low').length
    const reorderCount = suggestions.filter(item => item.priority === 'reorder').length

    return {
      suggestions,
      summary: {
        totalItems: suggestions.length,
        totalEstimatedCost,
        criticalCount,
        lowCount,
        reorderCount
      }
    }
  }
}

// Branch Management Functions
// export const branchService = {
//   // Get all branches
//   async getAllBranches() {
//     const { data, error } = await supabase
//       .from('branches')
//       .select(`
//         *,
//         manager:profiles(*)
//       `)
//       .eq('is_active', true)
//       .order('name')

//     if (error) throw error
//     return data
//   },

//   // Get branch with details
//   async getBranchDetails(branchId: string) {
//     const { data, error } = await supabase
//       .from('branches')
//       .select(`
//         *,
//         manager:profiles(*),
//         warehouses(*),
//         kitchens(*)
//       `)
//       .eq('id', branchId)
//       .single()

//     if (error) throw error
//     return data
//   }
// }
export const branchService = {
  // Get all branches
  async getAllBranches() {
    const { data, error } = await supabase
      .from('branches')
      .select(`
        *,
        manager:profiles!fk_branches_manager(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get branch with details
  async getBranchDetails(branchId: string) {
    const { data, error } = await supabase
      .from('branches')
      .select(`
        *,
        manager:profiles!fk_branches_manager(*),
        warehouses(*),
        kitchens(*)
      `)
      .eq('id', branchId)
      .single()

    if (error) throw error
    return data
  }
}

// Warehouse Management Functions
export const warehouseService = {
  // Get all warehouses
  async getAllWarehouses() {
    const { data, error } = await supabase
      .from('warehouses')
      .select(`
        *,
        branch:branches(*),
        manager:profiles(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get warehouses by branch
  async getWarehousesByBranch(branchId: string) {
    const { data, error } = await supabase
      .from('warehouses')
      .select(`
        *,
        manager:profiles(*)
      `)
      .eq('branch_id', branchId)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }
}

// Ingredient Management Functions
export const ingredientService = {
  // Get all ingredients
  async getAllIngredients() {
    const { data, error } = await supabase
      .from('ingredients')
      .select(`
        *,
        category:ingredient_categories(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get ingredients by category
  async getIngredientsByCategory(categoryId: string) {
    const { data, error } = await supabase
      .from('ingredients')
      .select(`
        *,
        category:ingredient_categories(*)
      `)
      .eq('category_id', categoryId)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }
}

// Production Management Functions
export const productionService = {
  // Get all recipes
  async getAllRecipes() {
    const { data, error } = await supabase
      .from('recipes')
      .select(`
        *,
        recipe_ingredients(
          *,
          ingredient:ingredients(*)
        )
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get recipe details
  async getRecipeDetails(recipeId: string) {
    const { data, error } = await supabase
      .from('recipes')
      .select(`
        *,
        recipe_ingredients(
          *,
          ingredient:ingredients(*)
        )
      `)
      .eq('id', recipeId)
      .single()

    if (error) throw error
    return data
  },

  // Create production batch with inventory validation
  async createProductionBatch(kitchenId: string, recipeId: string, plannedQuantity: number, startedBy: string, productionPlanId?: string): Promise<any> {
    const batchNumber = `BATCH-${Date.now()}`

    // Get recipe details with ingredients
    const recipe = await this.getRecipeDetails(recipeId)
    if (!recipe) throw new Error('Recipe not found')

    // Get kitchen details to find associated warehouse
    const { data: kitchen, error: kitchenError } = await supabase
      .from('kitchens')
      .select('*, warehouse:warehouses(*)')
      .eq('id', kitchenId)
      .single()

    if (kitchenError || !kitchen) throw new Error('Kitchen not found')

    // Check ingredient availability
    const ingredientChecks = await Promise.all(
      recipe.recipe_ingredients.map(async (recipeIngredient: any) => {
        const requiredQuantity = recipeIngredient.quantity * plannedQuantity

        const { data: inventory } = await supabase
          .from('inventory')
          .select('current_stock, ingredient:ingredients(name)')
          .eq('warehouse_id', kitchen.warehouse_id)
          .eq('ingredient_id', recipeIngredient.ingredient_id)
          .single()

        return {
          ingredient_id: recipeIngredient.ingredient_id,
          ingredient_name: inventory?.ingredient?.name || 'Unknown',
          required: requiredQuantity,
          available: inventory?.current_stock || 0,
          sufficient: (inventory?.current_stock || 0) >= requiredQuantity
        }
      })
    )

    // Check if all ingredients are available
    const insufficientIngredients = ingredientChecks.filter(check => !check.sufficient)
    if (insufficientIngredients.length > 0) {
      const errorMessage = `Insufficient ingredients: ${insufficientIngredients
        .map(ing => `${ing.ingredient_name} (need ${ing.required}, have ${ing.available})`)
        .join(', ')}`
      throw new Error(errorMessage)
    }

    // Calculate estimated cost
    const estimatedCost = recipe.recipe_ingredients.reduce((total: number, recipeIngredient: any) => {
      const ingredientCost = (recipeIngredient.ingredient.cost_per_unit || 0) * (recipeIngredient.quantity * plannedQuantity)
      return total + ingredientCost
    }, 0)

    const { data, error } = await supabase
      .from('production_batches')
      .insert({
        kitchen_id: kitchenId,
        recipe_id: recipeId,
        batch_number: batchNumber,
        planned_quantity: plannedQuantity,
        status: 'planned',
        started_by: startedBy,
        planned_start_time: new Date().toISOString(),
        production_plan_id: productionPlanId,
        estimated_cost: estimatedCost
      })
      .select()
      .single()

    if (error) throw error

    // Create planned ingredient usage records
    const plannedUsage = recipe.recipe_ingredients.map((recipeIngredient: any) => ({
      batch_id: data.id,
      ingredient_id: recipeIngredient.ingredient_id,
      planned_quantity: recipeIngredient.quantity * plannedQuantity,
      unit: recipeIngredient.unit,
      cost_per_unit: recipeIngredient.ingredient.cost_per_unit || 0
    }))

    await supabase
      .from('batch_ingredients_used')
      .insert(plannedUsage)

    return data
  },

  // Get production batches
  async getProductionBatches(kitchenId?: string, status?: string) {
    let query = supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(*),
        kitchen:kitchens(*),
        starter:profiles!started_by(*),
        completer:profiles!completed_by(*)
      `)

    if (kitchenId) {
      query = query.eq('kitchen_id', kitchenId)
    }

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Update batch status
  async updateBatchStatus(batchId: string, status: string, userId?: string, actualQuantity?: number, qualityScore?: number, qualityNotes?: string) {
    const updates: any = {
      status,
      updated_at: new Date().toISOString()
    }

    if (status === 'in_progress') {
      updates.actual_start_time = new Date().toISOString()
    } else if (status === 'completed') {
      updates.actual_end_time = new Date().toISOString()
      updates.completed_by = userId
      if (actualQuantity) updates.actual_quantity = actualQuantity
      if (qualityScore) updates.quality_score = qualityScore
      if (qualityNotes) updates.quality_notes = qualityNotes
    }

    const { error } = await supabase
      .from('production_batches')
      .update(updates)
      .eq('id', batchId)

    if (error) throw error
    return { success: true }
  },

  // Record ingredient usage for batch
  async recordIngredientUsage(batchId: string, ingredientUsage: any[]) {
    const usageRecords = ingredientUsage.map(usage => ({
      batch_id: batchId,
      ingredient_id: usage.ingredient_id,
      planned_quantity: usage.planned_quantity,
      actual_quantity: usage.actual_quantity,
      unit: usage.unit,
      cost_per_unit: usage.cost_per_unit,
      total_cost: usage.actual_quantity * usage.cost_per_unit
    }))

    const { error } = await supabase
      .from('batch_ingredients_used')
      .insert(usageRecords)

    if (error) throw error
    return { success: true }
  },

  // Get kitchen production summary
  async getKitchenProductionSummary(kitchenId: string, startDate?: string, endDate?: string) {
    let query = supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(name)
      `)
      .eq('kitchen_id', kitchenId)

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Start production batch with inventory consumption
  async startProductionBatch(batchId: string, userId: string): Promise<any> {
    // Get batch details with ingredients
    const { data: batch, error: batchError } = await supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(*),
        kitchen:kitchens(warehouse_id),
        batch_ingredients_used(*)
      `)
      .eq('id', batchId)
      .single()

    if (batchError || !batch) throw new Error('Production batch not found')

    if (batch.status !== 'planned') {
      throw new Error('Batch must be in planned status to start production')
    }

    // Update batch status to in_progress
    const { error: updateError } = await supabase
      .from('production_batches')
      .update({
        status: 'in_progress',
        actual_start_time: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', batchId)

    if (updateError) throw updateError

    // Consume ingredients from inventory
    const stockMovements = []
    for (const ingredientUsed of batch.batch_ingredients_used) {
      // Get current inventory
      const { data: inventory, error: invError } = await supabase
        .from('inventory')
        .select('*')
        .eq('warehouse_id', batch.kitchen.warehouse_id)
        .eq('ingredient_id', ingredientUsed.ingredient_id)
        .single()

      if (invError || !inventory) {
        throw new Error(`Inventory not found for ingredient ${ingredientUsed.ingredient_id}`)
      }

      // Check if sufficient stock
      if (inventory.current_stock < ingredientUsed.planned_quantity) {
        throw new Error(`Insufficient stock for ingredient ${ingredientUsed.ingredient_id}`)
      }

      // Update inventory
      const newStock = inventory.current_stock - ingredientUsed.planned_quantity
      await supabase
        .from('inventory')
        .update({
          current_stock: newStock,
          updated_at: new Date().toISOString()
        })
        .eq('id', inventory.id)

      // Record stock movement (traditional)
      stockMovements.push({
        warehouse_id: batch.kitchen.warehouse_id,
        ingredient_id: ingredientUsed.ingredient_id,
        movement_type: 'out',
        quantity: ingredientUsed.planned_quantity,
        unit: ingredientUsed.unit,
        reference_type: 'production',
        reference_id: batchId,
        notes: `Production consumption for batch ${batch.batch_number}`,
        performed_by: userId
      })

      // Record enhanced stock movement for raw ingredients
      await supabase
        .from('enhanced_stock_movements')
        .insert({
          warehouse_id: batch.kitchen.warehouse_id,
          item_type: 'raw_ingredient',
          item_id: ingredientUsed.ingredient_id,
          movement_type: 'production_input',
          quantity: ingredientUsed.planned_quantity,
          unit: ingredientUsed.unit,
          reference_type: 'production',
          reference_id: batchId,
          notes: `Production consumption for batch ${batch.batch_number}`,
          performed_by: userId
        })
    }

    // Insert stock movements
    if (stockMovements.length > 0) {
      await supabase
        .from('stock_movements')
        .insert(stockMovements)
    }

    return { success: true, message: 'Production started successfully' }
  },

  // Complete production batch with finished goods
  async completeProductionBatch(
    batchId: string,
    userId: string,
    actualQuantity: number,
    qualityScore: number,
    qualityNotes?: string,
    wasteData?: Array<{ingredient_id: string, waste_quantity: number, waste_type: string, waste_reason?: string}>
  ): Promise<any> {
    const { data: batch, error: batchError } = await supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(*),
        kitchen:kitchens(warehouse_id)
      `)
      .eq('id', batchId)
      .single()

    if (batchError || !batch) throw new Error('Production batch not found')

    if (batch.status !== 'in_progress') {
      throw new Error('Batch must be in progress to complete')
    }

    // Calculate yield percentage
    const yieldPercentage = (actualQuantity / batch.planned_quantity) * 100

    // Update batch status
    const { error: updateError } = await supabase
      .from('production_batches')
      .update({
        status: 'completed',
        actual_quantity: actualQuantity,
        actual_end_time: new Date().toISOString(),
        completed_by: userId,
        quality_score: qualityScore,
        quality_notes: qualityNotes,
        yield_percentage: yieldPercentage,
        updated_at: new Date().toISOString()
      })
      .eq('id', batchId)

    if (updateError) throw updateError

    // Record waste if provided
    if (wasteData && wasteData.length > 0) {
      const wasteRecords = wasteData.map(waste => ({
        batch_id: batchId,
        ingredient_id: waste.ingredient_id,
        waste_quantity: waste.waste_quantity,
        unit: 'kg', // Default unit, should be passed from frontend
        waste_type: waste.waste_type,
        waste_reason: waste.waste_reason,
        reported_by: userId
      }))

      await supabase
        .from('production_waste')
        .insert(wasteRecords)
    }

    // Add finished goods to inventory if recipe has a corresponding menu item
    const { data: menuItem } = await supabase
      .from('menu_items')
      .select('id, name')
      .eq('recipe_id', batch.recipe_id)
      .single()

    if (menuItem) {
      // Calculate cost per unit
      const { data: productionCost } = await supabase
        .from('production_costs')
        .select('total_cost')
        .eq('batch_id', batchId)
        .single()

      const costPerUnit = productionCost ? productionCost.total_cost / actualQuantity : 0

      // Check if finished goods inventory exists
      const { data: existingFinishedGoods } = await supabase
        .from('finished_goods_inventory')
        .select('*')
        .eq('warehouse_id', batch.kitchen.warehouse_id)
        .eq('menu_item_id', menuItem.id)
        .single()

      if (existingFinishedGoods) {
        // Update existing finished goods inventory
        await supabase
          .from('finished_goods_inventory')
          .update({
            current_stock: existingFinishedGoods.current_stock + actualQuantity,
            cost_per_unit: costPerUnit,
            last_produced_at: new Date().toISOString(),
            production_batch_id: batchId,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingFinishedGoods.id)
      } else {
        // Create new finished goods inventory record
        await supabase
          .from('finished_goods_inventory')
          .insert({
            warehouse_id: batch.kitchen.warehouse_id,
            menu_item_id: menuItem.id,
            current_stock: actualQuantity,
            minimum_stock: 10, // Default minimum
            maximum_stock: 100, // Default maximum
            reorder_point: 20, // Default reorder point
            cost_per_unit: costPerUnit,
            last_produced_at: new Date().toISOString(),
            production_batch_id: batchId
          })
      }

      // Record production output
      await supabase
        .from('production_finished_goods_output')
        .insert({
          production_batch_id: batchId,
          menu_item_id: menuItem.id,
          warehouse_id: batch.kitchen.warehouse_id,
          quantity_produced: actualQuantity,
          cost_per_unit: costPerUnit,
          total_cost: productionCost?.total_cost || 0,
          expiry_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours default
        })

      // Record enhanced stock movement for finished goods
      await supabase
        .from('enhanced_stock_movements')
        .insert({
          warehouse_id: batch.kitchen.warehouse_id,
          item_type: 'finished_goods',
          item_id: menuItem.id,
          movement_type: 'production_output',
          quantity: actualQuantity,
          unit: 'portions',
          reference_type: 'production',
          reference_id: batchId,
          cost_per_unit: costPerUnit,
          total_cost: productionCost?.total_cost || 0,
          notes: `Production output for batch ${batch.batch_number} - ${menuItem.name}`,
          performed_by: userId
        })
    }

    return { success: true, message: 'Production completed successfully' }
  },

  // Create production plan based on demand forecast
  async createProductionPlan(
    kitchenId: string,
    recipeId: string,
    plannedDate: string,
    plannedQuantity: number,
    createdBy: string,
    demandForecast?: number
  ): Promise<any> {
    // Get current stock level for the recipe/menu item
    const { data: kitchen } = await supabase
      .from('kitchens')
      .select('warehouse_id')
      .eq('id', kitchenId)
      .single()

    let currentStockLevel = 0
    if (kitchen) {
      const { data: inventory } = await supabase
        .from('inventory')
        .select('current_stock')
        .eq('warehouse_id', kitchen.warehouse_id)
        .eq('ingredient_id', recipeId)
        .single()

      currentStockLevel = inventory?.current_stock || 0
    }

    const { data, error } = await supabase
      .from('production_plans')
      .insert({
        kitchen_id: kitchenId,
        recipe_id: recipeId,
        planned_date: plannedDate,
        planned_quantity: plannedQuantity,
        demand_forecast_quantity: demandForecast,
        current_stock_level: currentStockLevel,
        created_by: createdBy,
        status: 'pending'
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Generate demand forecast based on historical sales
  async generateDemandForecast(recipeId: string, forecastDays: number = 7): Promise<any> {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - (forecastDays * 4)) // Look back 4 weeks

    const { data: salesData, error } = await supabase
      .from('sales_transaction_items')
      .select(`
        quantity,
        created_at,
        menu_item:menu_items!inner(recipe_id)
      `)
      .eq('menu_item.recipe_id', recipeId)
      .gte('created_at', startDate.toISOString())

    if (error) throw error

    if (!salesData || salesData.length === 0) {
      return {
        averageDailyDemand: 0,
        forecastedDemand: 0,
        confidence: 'low',
        historicalData: []
      }
    }

    // Group by day and calculate daily totals
    const dailyTotals: { [key: string]: number } = {}
    salesData.forEach(item => {
      const date = new Date(item.created_at).toISOString().split('T')[0]
      dailyTotals[date] = (dailyTotals[date] || 0) + item.quantity
    })

    const dailyValues = Object.values(dailyTotals)
    const averageDailyDemand = dailyValues.reduce((sum, val) => sum + val, 0) / dailyValues.length
    const forecastedDemand = Math.ceil(averageDailyDemand * forecastDays)

    // Calculate confidence based on data consistency
    const variance = dailyValues.reduce((sum, val) => sum + Math.pow(val - averageDailyDemand, 2), 0) / dailyValues.length
    const standardDeviation = Math.sqrt(variance)
    const coefficientOfVariation = standardDeviation / averageDailyDemand

    let confidence = 'high'
    if (coefficientOfVariation > 0.5) confidence = 'low'
    else if (coefficientOfVariation > 0.3) confidence = 'medium'

    return {
      averageDailyDemand: Math.round(averageDailyDemand * 100) / 100,
      forecastedDemand,
      confidence,
      historicalData: dailyTotals,
      dataPoints: dailyValues.length
    }
  },

  // Get production plans
  async getProductionPlans(kitchenId?: string, status?: string, startDate?: string, endDate?: string) {
    let query = supabase
      .from('production_plans')
      .select(`
        *,
        recipe:recipes(*),
        kitchen:kitchens(*),
        creator:profiles!created_by(*),
        approver:profiles!approved_by(*)
      `)

    if (kitchenId) query = query.eq('kitchen_id', kitchenId)
    if (status) query = query.eq('status', status)
    if (startDate) query = query.gte('planned_date', startDate)
    if (endDate) query = query.lte('planned_date', endDate)

    const { data, error } = await query.order('planned_date', { ascending: true })

    if (error) throw error
    return data
  },

  // Approve production plan
  async approveProductionPlan(planId: string, approvedBy: string): Promise<any> {
    const { error } = await supabase
      .from('production_plans')
      .update({
        status: 'approved',
        approved_by: approvedBy,
        updated_at: new Date().toISOString()
      })
      .eq('id', planId)

    if (error) throw error
    return { success: true }
  },

  // Get production analytics
  async getProductionAnalytics(kitchenId?: string, startDate?: string, endDate?: string) {
    let query = supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(name, category),
        production_costs(*),
        production_waste(*)
      `)

    if (kitchenId) query = query.eq('kitchen_id', kitchenId)
    if (startDate) query = query.gte('created_at', startDate)
    if (endDate) query = query.lte('created_at', endDate)

    const { data: batches, error } = await query

    if (error) throw error

    // Calculate analytics
    const totalBatches = batches.length
    const completedBatches = batches.filter(b => b.status === 'completed')
    const averageYield = completedBatches.reduce((sum, b) => sum + (b.yield_percentage || 0), 0) / completedBatches.length
    const averageQuality = completedBatches.reduce((sum, b) => sum + (b.quality_score || 0), 0) / completedBatches.length
    const totalWaste = batches.reduce((sum, b) => {
      return sum + (b.production_waste?.reduce((wasteSum: number, w: any) => wasteSum + w.waste_quantity, 0) || 0)
    }, 0)

    return {
      totalBatches,
      completedBatches: completedBatches.length,
      completionRate: (completedBatches.length / totalBatches) * 100,
      averageYield: Math.round(averageYield * 100) / 100,
      averageQuality: Math.round(averageQuality * 100) / 100,
      totalWaste: Math.round(totalWaste * 100) / 100,
      batchesByStatus: {
        planned: batches.filter(b => b.status === 'planned').length,
        in_progress: batches.filter(b => b.status === 'in_progress').length,
        completed: batches.filter(b => b.status === 'completed').length,
        cancelled: batches.filter(b => b.status === 'cancelled').length
      }
    }
  },

  // Quality Control Methods
  async createQualityCheckpoint(
    batchId: string,
    checkpointName: string,
    checkpointType: 'preparation' | 'cooking' | 'plating' | 'final',
    status: 'pending' | 'passed' | 'failed' | 'requires_attention',
    score: number,
    notes: string,
    checkedBy: string
  ): Promise<any> {
    const { data, error } = await supabase
      .from('quality_checkpoints')
      .insert({
        batch_id: batchId,
        checkpoint_name: checkpointName,
        checkpoint_type: checkpointType,
        status,
        score,
        notes,
        checked_by: checkedBy
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  async getQualityCheckpoints(batchId: string) {
    const { data, error } = await supabase
      .from('quality_checkpoints')
      .select(`
        *,
        checker:profiles!checked_by(full_name)
      `)
      .eq('batch_id', batchId)
      .order('checked_at', { ascending: true })

    if (error) throw error
    return data
  },

  // Production Cost Tracking
  async recordProductionCosts(
    batchId: string,
    ingredientCost: number,
    laborCost: number = 0,
    overheadCost: number = 0
  ): Promise<any> {
    // Get batch details to calculate cost per unit
    const { data: batch } = await supabase
      .from('production_batches')
      .select('actual_quantity, planned_quantity')
      .eq('id', batchId)
      .single()

    const quantity = batch?.actual_quantity || batch?.planned_quantity || 1
    const totalCost = ingredientCost + laborCost + overheadCost
    const costPerUnit = totalCost / quantity

    const { data, error } = await supabase
      .from('production_costs')
      .insert({
        batch_id: batchId,
        ingredient_cost: ingredientCost,
        labor_cost: laborCost,
        overhead_cost: overheadCost,
        cost_per_unit: costPerUnit
      })
      .select()
      .single()

    if (error) throw error

    // Update batch with actual cost
    await supabase
      .from('production_batches')
      .update({ actual_cost: totalCost })
      .eq('id', batchId)

    return data
  },

  async getProductionCosts(batchId: string) {
    const { data, error } = await supabase
      .from('production_costs')
      .select('*')
      .eq('batch_id', batchId)
      .single()

    if (error) throw error
    return data
  },

  // Waste Tracking
  async recordProductionWaste(
    batchId: string,
    ingredientId: string,
    wasteQuantity: number,
    unit: string,
    wasteType: 'spoilage' | 'overcooking' | 'preparation_error' | 'equipment_failure' | 'other',
    wasteReason: string,
    costImpact: number,
    reportedBy: string
  ): Promise<any> {
    const { data, error } = await supabase
      .from('production_waste')
      .insert({
        batch_id: batchId,
        ingredient_id: ingredientId,
        waste_quantity: wasteQuantity,
        unit,
        waste_type: wasteType,
        waste_reason: wasteReason,
        cost_impact: costImpact,
        reported_by: reportedBy
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  async getProductionWaste(batchId?: string, startDate?: string, endDate?: string) {
    let query = supabase
      .from('production_waste')
      .select(`
        *,
        ingredient:ingredients(name, unit),
        batch:production_batches(batch_number, recipe:recipes(name)),
        reporter:profiles!reported_by(full_name)
      `)

    if (batchId) query = query.eq('batch_id', batchId)
    if (startDate) query = query.gte('created_at', startDate)
    if (endDate) query = query.lte('created_at', endDate)

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Batch Production (Multiple recipes in one batch)
  async createMultiRecipeBatch(
    kitchenId: string,
    recipes: Array<{recipe_id: string, planned_quantity: number}>,
    startedBy: string,
    batchName?: string
  ): Promise<any> {
    const batchNumber = batchName || `MULTI-BATCH-${Date.now()}`
    const createdBatches = []

    for (const recipe of recipes) {
      const batch = await this.createProductionBatch(
        kitchenId,
        recipe.recipe_id,
        recipe.planned_quantity,
        startedBy
      )

      // Update batch number to group them
      await supabase
        .from('production_batches')
        .update({
          batch_number: `${batchNumber}-${recipe.recipe_id.slice(-4)}`,
          special_instructions: `Part of multi-recipe batch: ${batchNumber}`
        })
        .eq('id', batch.id)

      createdBatches.push(batch)
    }

    return {
      success: true,
      batchNumber,
      batches: createdBatches,
      message: `Created ${createdBatches.length} production batches`
    }
  },

  // Production Efficiency Metrics
  async getProductionEfficiencyMetrics(kitchenId?: string, startDate?: string, endDate?: string) {
    let query = supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(prep_time_minutes, cook_time_minutes),
        production_costs(*)
      `)
      .eq('status', 'completed')

    if (kitchenId) query = query.eq('kitchen_id', kitchenId)
    if (startDate) query = query.gte('created_at', startDate)
    if (endDate) query = query.lte('created_at', endDate)

    const { data: batches, error } = await query

    if (error) throw error

    if (!batches || batches.length === 0) {
      return {
        totalBatches: 0,
        averageEfficiency: 0,
        onTimeCompletion: 0,
        costEfficiency: 0
      }
    }

    // Calculate efficiency metrics
    let totalEfficiency = 0
    let onTimeCount = 0
    let totalCostVariance = 0

    batches.forEach(batch => {
      // Time efficiency
      const plannedDuration = (batch.recipe.prep_time_minutes || 0) + (batch.recipe.cook_time_minutes || 0)
      if (batch.actual_start_time && batch.actual_end_time && plannedDuration > 0) {
        const actualDuration = (new Date(batch.actual_end_time).getTime() - new Date(batch.actual_start_time).getTime()) / (1000 * 60)
        const efficiency = (plannedDuration / actualDuration) * 100
        totalEfficiency += Math.min(efficiency, 200) // Cap at 200% efficiency

        if (efficiency >= 90) onTimeCount++
      }

      // Cost efficiency
      if (batch.estimated_cost && batch.actual_cost) {
        const costVariance = ((batch.actual_cost - batch.estimated_cost) / batch.estimated_cost) * 100
        totalCostVariance += Math.abs(costVariance)
      }
    })

    return {
      totalBatches: batches.length,
      averageEfficiency: Math.round((totalEfficiency / batches.length) * 100) / 100,
      onTimeCompletion: Math.round((onTimeCount / batches.length) * 100 * 100) / 100,
      costEfficiency: Math.round((100 - (totalCostVariance / batches.length)) * 100) / 100
    }
  }
}

// Kitchen Management Functions
export const kitchenService = {
  // Get all kitchens
  async getAllKitchens() {
    const { data, error } = await supabase
      .from('kitchens')
      .select(`
        *,
        branch:branches(*),
        warehouse:warehouses(*),
        head_chef:profiles(*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Get kitchen details
  async getKitchenDetails(kitchenId: string) {
    const { data, error } = await supabase
      .from('kitchens')
      .select(`
        *,
        branch:branches(*),
        warehouse:warehouses(*),
        head_chef:profiles(*)
      `)
      .eq('id', kitchenId)
      .single()

    if (error) throw error
    return data
  }
}

// Sales Management Functions
export const salesService = {
  // Get sales transactions
  async getSalesTransactions(branchId?: string, startDate?: string, endDate?: string) {
    let query = supabase
      .from('sales_transactions')
      .select(`
        *,
        branch:branches(*),
        server:profiles!served_by(*),
        sales_transaction_items(
          *,
          menu_item:menu_items(*)
        )
      `)

    if (branchId) {
      query = query.eq('branch_id', branchId)
    }

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Get daily sales summary
  async getDailySalesSummary(branchId: string, date?: string) {
    const targetDate = date || new Date().toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('daily_sales_summaries')
      .select('*')
      .eq('branch_id', branchId)
      .eq('date', targetDate)
      .single()

    if (error && error.code !== 'PGRST116') throw error
    return data
  },

  // Create sales transaction
  async createSalesTransaction(branchId: string, items: any[], customerInfo: any, paymentInfo: any, servedBy: string) {
    const transactionNumber = `TXN-${Date.now()}`
    const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0)

    // Create transaction
    const { data: transaction, error: transactionError } = await supabase
      .from('sales_transactions')
      .insert({
        branch_id: branchId,
        transaction_number: transactionNumber,
        customer_name: customerInfo.name,
        customer_phone: customerInfo.phone,
        total_amount: totalAmount,
        tax_amount: paymentInfo.tax_amount || 0,
        discount_amount: paymentInfo.discount_amount || 0,
        payment_method: paymentInfo.method,
        payment_status: 'completed',
        served_by: servedBy
      })
      .select()
      .single()

    if (transactionError) throw transactionError

    // Create transaction items
    const transactionItems = items.map(item => ({
      transaction_id: transaction.id,
      menu_item_id: item.menu_item_id,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.quantity * item.unit_price,
      special_instructions: item.special_instructions
    }))

    const { error: itemsError } = await supabase
      .from('sales_transaction_items')
      .insert(transactionItems)

    if (itemsError) throw itemsError

    return transaction
  },

  // Enhanced sales transaction with finished goods consumption
  async createSalesTransactionWithFinishedGoods(branchId: string, items: any[], customerInfo: any, paymentInfo: any, servedBy: string) {
    const transactionNumber = `TXN-${Date.now()}`
    const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.price), 0)

    // Create transaction
    const { data: transaction, error: transactionError } = await supabase
      .from('sales_transactions')
      .insert({
        branch_id: branchId,
        transaction_number: transactionNumber,
        customer_name: customerInfo.name,
        customer_phone: customerInfo.phone,
        total_amount: totalAmount,
        tax_amount: paymentInfo.tax_amount || 0,
        discount_amount: paymentInfo.discount_amount || 0,
        payment_method: paymentInfo.method,
        payment_status: 'completed',
        served_by: servedBy
      })
      .select()
      .single()

    if (transactionError) throw transactionError

    // Create transaction items and update inventory
    const transactionItems = []
    const stockMovements = []

    // Get branch warehouses
    const { data: warehouses } = await supabase
      .from('warehouses')
      .select('id')
      .eq('branch_id', branchId)

    const warehouseIds = warehouses?.map(w => w.id) || []

    for (const item of items) {
      // Create transaction item
      transactionItems.push({
        transaction_id: transaction.id,
        menu_item_id: item.menu_item_id,
        quantity: item.quantity,
        unit_price: item.price,
        total_price: item.quantity * item.price,
        special_instructions: item.special_instructions
      })

      // Try to consume from finished goods first
      let remainingQuantity = item.quantity

      for (const warehouseId of warehouseIds) {
        if (remainingQuantity <= 0) break

        const { data: finishedGoods } = await supabase
          .from('finished_goods_inventory')
          .select('*')
          .eq('warehouse_id', warehouseId)
          .eq('menu_item_id', item.menu_item_id)
          .single()

        if (finishedGoods && finishedGoods.current_stock > 0) {
          const consumeQuantity = Math.min(remainingQuantity, finishedGoods.current_stock)

          // Update finished goods inventory
          await supabase
            .from('finished_goods_inventory')
            .update({
              current_stock: finishedGoods.current_stock - consumeQuantity,
              updated_at: new Date().toISOString()
            })
            .eq('id', finishedGoods.id)

          // Record enhanced stock movement for finished goods
          await supabase
            .from('enhanced_stock_movements')
            .insert({
              warehouse_id: warehouseId,
              item_type: 'finished_goods',
              item_id: item.menu_item_id,
              movement_type: 'out',
              quantity: consumeQuantity,
              unit: 'portions',
              reference_type: 'sale',
              reference_id: transaction.id,
              notes: `Sale consumption - finished goods`,
              performed_by: servedBy
            })

          remainingQuantity -= consumeQuantity
        }
      }

      // If we still have remaining quantity, consume raw ingredients (fallback)
      if (remainingQuantity > 0) {
        // Get menu item recipe to update ingredient inventory
        const { data: menuItem } = await supabase
          .from('menu_items')
          .select(`
            *,
            recipe:recipes(
              recipe_ingredients(
                *,
                ingredient:ingredients(*)
              )
            )
          `)
          .eq('id', item.menu_item_id)
          .single()

        if (menuItem?.recipe?.recipe_ingredients) {
          // Update inventory for each ingredient used (for remaining quantity)
          for (const recipeIngredient of menuItem.recipe.recipe_ingredients) {
            const consumedQuantity = recipeIngredient.quantity * remainingQuantity

            // Find inventory in branch warehouses
            for (const warehouseId of warehouseIds) {
              const { data: inventory } = await supabase
                .from('inventory')
                .select('*')
                .eq('warehouse_id', warehouseId)
                .eq('ingredient_id', recipeIngredient.ingredient_id)
                .single()

              if (inventory && inventory.current_stock >= consumedQuantity) {
                // Update inventory
                await supabase
                  .from('inventory')
                  .update({
                    current_stock: inventory.current_stock - consumedQuantity,
                    updated_at: new Date().toISOString()
                  })
                  .eq('id', inventory.id)

                // Record traditional stock movement
                stockMovements.push({
                  warehouse_id: warehouseId,
                  ingredient_id: recipeIngredient.ingredient_id,
                  movement_type: 'out',
                  quantity: consumedQuantity,
                  unit: recipeIngredient.unit,
                  reference_type: 'sale',
                  reference_id: transaction.id,
                  notes: `Sale consumption - raw ingredients (no finished goods available)`,
                  performed_by: servedBy
                })

                // Record enhanced stock movement for raw ingredients
                await supabase
                  .from('enhanced_stock_movements')
                  .insert({
                    warehouse_id: warehouseId,
                    item_type: 'raw_ingredient',
                    item_id: recipeIngredient.ingredient_id,
                    movement_type: 'out',
                    quantity: consumedQuantity,
                    unit: recipeIngredient.unit,
                    reference_type: 'sale',
                    reference_id: transaction.id,
                    notes: `Sale consumption - raw ingredients (no finished goods available)`,
                    performed_by: servedBy
                  })

                break // Found inventory, no need to check other warehouses
              }
            }
          }
        }

        // Log that we had to use raw ingredients (could trigger production alert)
        console.log(`Used raw ingredients for ${remainingQuantity} units of menu item ${item.menu_item_id} - consider increasing production`)
      }
    }

    // Insert transaction items
    await supabase
      .from('sales_transaction_items')
      .insert(transactionItems)

    // Insert stock movements
    if (stockMovements.length > 0) {
      await supabase
        .from('stock_movements')
        .insert(stockMovements)
    }

    return transaction
  },

  // Check real-time menu item availability based on finished goods inventory
  async checkMenuItemAvailability(branchId: string, menuItemId: string) {
    // Get branch warehouses
    const { data: warehouses } = await supabase
      .from('warehouses')
      .select('id')
      .eq('branch_id', branchId)

    if (!warehouses || warehouses.length === 0) {
      return {
        menu_item_id: menuItemId,
        is_available: false,
        availability_reason: 'out_of_stock',
        current_stock: 0,
        can_produce: false,
        missing_ingredients: [],
        estimated_production_time: 0
      }
    }

    // Check finished goods inventory
    let totalFinishedStock = 0
    for (const warehouse of warehouses) {
      const { data: finishedGoods } = await supabase
        .from('finished_goods_inventory')
        .select('current_stock')
        .eq('warehouse_id', warehouse.id)
        .eq('menu_item_id', menuItemId)
        .single()

      totalFinishedStock += finishedGoods?.current_stock || 0
    }

    // Check daily quota
    const { data: branchMenu } = await supabase
      .from('branch_menu_pricing')
      .select('daily_quota, current_sold, is_available')
      .eq('branch_id', branchId)
      .eq('menu_item_id', menuItemId)
      .single()

    // Check if manually disabled
    if (!branchMenu?.is_available) {
      return {
        menu_item_id: menuItemId,
        is_available: false,
        availability_reason: 'manually_disabled',
        current_stock: totalFinishedStock,
        can_produce: false,
        missing_ingredients: [],
        estimated_production_time: 0
      }
    }

    // Check daily quota
    if (branchMenu?.daily_quota && branchMenu.current_sold >= branchMenu.daily_quota) {
      return {
        menu_item_id: menuItemId,
        is_available: false,
        availability_reason: 'quota_reached',
        current_stock: totalFinishedStock,
        can_produce: false,
        missing_ingredients: [],
        estimated_production_time: 0
      }
    }

    // If we have finished goods in stock
    if (totalFinishedStock > 0) {
      return {
        menu_item_id: menuItemId,
        is_available: true,
        availability_reason: totalFinishedStock <= 5 ? 'low_stock' : 'in_stock',
        current_stock: totalFinishedStock,
        can_produce: true,
        missing_ingredients: [],
        estimated_production_time: 0
      }
    }

    // Check if we can produce (raw ingredients available)
    const productionCheck = await this.checkProductionCapability(menuItemId, warehouses[0].id)

    return {
      menu_item_id: menuItemId,
      is_available: productionCheck.can_produce,
      availability_reason: productionCheck.can_produce ? 'out_of_stock' : 'out_of_stock',
      current_stock: 0,
      can_produce: productionCheck.can_produce,
      missing_ingredients: productionCheck.missing_ingredients,
      estimated_production_time: productionCheck.estimated_time
    }
  },

  // Check if we can produce a menu item based on raw ingredient availability
  async checkProductionCapability(menuItemId: string, warehouseId: string) {
    const { data: menuItem } = await supabase
      .from('menu_items')
      .select(`
        *,
        recipe:recipes(
          prep_time_minutes,
          cook_time_minutes,
          recipe_ingredients(
            quantity,
            unit,
            ingredient:ingredients(id, name, unit)
          )
        )
      `)
      .eq('id', menuItemId)
      .single()

    if (!menuItem?.recipe) {
      return { can_produce: false, missing_ingredients: ['Recipe not found'], estimated_time: 0 }
    }

    const missingIngredients: string[] = []

    for (const recipeIngredient of menuItem.recipe.recipe_ingredients) {
      const { data: inventory } = await supabase
        .from('inventory')
        .select('current_stock')
        .eq('warehouse_id', warehouseId)
        .eq('ingredient_id', recipeIngredient.ingredient.id)
        .single()

      if (!inventory || inventory.current_stock < recipeIngredient.quantity) {
        missingIngredients.push(recipeIngredient.ingredient.name)
      }
    }

    const estimatedTime = (menuItem.recipe.prep_time_minutes || 0) + (menuItem.recipe.cook_time_minutes || 0)

    return {
      can_produce: missingIngredients.length === 0,
      missing_ingredients: missingIngredients,
      estimated_time: estimatedTime
    }
  },

  // Get comprehensive availability for all menu items in a branch
  async getBranchMenuAvailability(branchId: string) {
    const { data, error } = await supabase
      .from('menu_item_availability_view')
      .select('*')
      .eq('branch_id', branchId)

    if (error) throw error
    return data
  },

  // Generate automatic production recommendations based on sales trends
  async generateProductionRecommendations(branchId: string, forecastDays: number = 7) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30) // Look back 30 days

    // Get sales data for the branch
    const { data: salesData, error } = await supabase
      .from('sales_transaction_items')
      .select(`
        quantity,
        created_at,
        menu_item:menu_items(
          *,
          recipe:recipes(*)
        ),
        sales_transactions!inner(branch_id)
      `)
      .eq('sales_transactions.branch_id', branchId)
      .gte('created_at', startDate.toISOString())

    if (error) throw error

    // Group by recipe and calculate trends
    const recipeStats: { [key: string]: any } = {}

    salesData?.forEach(item => {
      if (item.menu_item?.recipe) {
        const recipeId = item.menu_item.recipe.id
        if (!recipeStats[recipeId]) {
          recipeStats[recipeId] = {
            recipe: item.menu_item.recipe,
            menuItem: item.menu_item,
            totalSold: 0,
            dailySales: {},
            trend: 'stable'
          }
        }

        recipeStats[recipeId].totalSold += item.quantity

        const date = new Date(item.created_at).toISOString().split('T')[0]
        recipeStats[recipeId].dailySales[date] = (recipeStats[recipeId].dailySales[date] || 0) + item.quantity
      }
    })

    // Calculate recommendations
    const recommendations = []

    for (const [recipeId, stats] of Object.entries(recipeStats)) {
      const dailyValues = Object.values(stats.dailySales) as number[]
      const averageDailySales = dailyValues.reduce((sum: number, val: number) => sum + val, 0) / dailyValues.length
      const forecastedDemand = Math.ceil(averageDailySales * forecastDays)

      // Get current stock level
      const { data: warehouses } = await supabase
        .from('warehouses')
        .select('id')
        .eq('branch_id', branchId)

      let currentStock = 0
      if (warehouses) {
        for (const warehouse of warehouses) {
          const { data: inventory } = await supabase
            .from('inventory')
            .select('current_stock')
            .eq('warehouse_id', warehouse.id)
            .eq('ingredient_id', recipeId)
            .single()

          currentStock += inventory?.current_stock || 0
        }
      }

      const recommendedProduction = Math.max(0, forecastedDemand - currentStock)

      if (recommendedProduction > 0) {
        recommendations.push({
          recipeId,
          recipeName: stats.recipe.name,
          menuItemName: stats.menuItem.name,
          currentStock,
          averageDailySales: Math.round(averageDailySales * 100) / 100,
          forecastedDemand,
          recommendedProduction,
          priority: recommendedProduction > averageDailySales * 2 ? 'high' : 'medium',
          reason: currentStock < forecastedDemand ? 'low_stock' : 'demand_forecast'
        })
      }
    }

    // Sort by priority and recommended quantity
    recommendations.sort((a, b) => {
      if (a.priority === 'high' && b.priority !== 'high') return -1
      if (b.priority === 'high' && a.priority !== 'high') return 1
      return b.recommendedProduction - a.recommendedProduction
    })

    return {
      recommendations,
      generatedAt: new Date().toISOString(),
      forecastPeriod: forecastDays,
      totalRecommendations: recommendations.length
    }
  }
}

// Menu Management Functions
export const menuService = {
  // Get menu items
  async getMenuItems() {
    const { data, error } = await supabase
      .from('menu_items')
      .select(`
        *,
        recipe:recipes(*)
      `)
      .eq('is_available', true)
      .order('category', { ascending: true })
      .order('name', { ascending: true })

    if (error) throw error
    return data
  },

  // Get branch menu with pricing
  // async getBranchMenu(branchId: string) {
  //   const { data, error } = await supabase
  //     .from('branch_menu_pricing')
  //     .select(`
  //       *,
  //       menu_item:menu_items(
  //         *,
  //         recipe:recipes(*)
  //       )
  //     `)
  //     .eq('branch_id', branchId)
  //     .eq('is_available', true)
  //     .order('menu_item.category', { ascending: true })

  //   if (error) throw error
  //   return data
  // },
  async getBranchMenu(branchId: string) {
    const { data, error } = await supabase
      .from('branch_menu_pricing')
      .select(`
        *,
        menu_item:menu_items(
          *,
          recipe:recipes(*)
        )
      `)
      .eq('branch_id', branchId)
      .eq('is_available', true)
      .order('menu_item(category)', { ascending: true })
  
    if (error) throw error
    return data
  },

  // Update menu item availability
  async updateMenuItemAvailability(branchId: string, menuItemId: string, isAvailable: boolean) {
    const { error } = await supabase
      .from('branch_menu_pricing')
      .update({ is_available: isAvailable })
      .eq('branch_id', branchId)
      .eq('menu_item_id', menuItemId)

    if (error) throw error
    return { success: true }
  }
}

// // Analytics and Reporting Functions
// export const analyticsService = {
//   // Get branch performance metrics
//   async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {
//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
//     const end = endDate || new Date().toISOString()

//     // Get sales data
//     const { data: salesData, error: salesError } = await supabase
//       .from('sales_transactions')
//       .select('total_amount, created_at, payment_method')
//       .eq('branch_id', branchId)
//       .gte('created_at', start)
//       .lte('created_at', end)

//     if (salesError) throw salesError

//     // Get inventory data
//     const { data: inventoryData, error: inventoryError } = await supabase
//       .from('inventory')
//       .select(`
//         current_stock,
//         minimum_stock,
//         ingredient:ingredients(cost_per_unit)
//       `)
//       .in('warehouse_id',
//         supabase
//           .from('warehouses')
//           .select('id')
//           .eq('branch_id', branchId)
//       )

//     if (inventoryError) throw inventoryError

//     // Calculate metrics
//     const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)
//     const totalTransactions = salesData.length
//     const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0
//     const inventoryValue = inventoryData.reduce((sum, item) =>
//       sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0
//     )
//     const lowStockItems = inventoryData.filter(item =>
//       item.current_stock <= item.minimum_stock
//     ).length

//     return {
//       totalRevenue,
//       totalTransactions,
//       averageTransaction,
//       inventoryValue,
//       lowStockItems,
//       salesData,
//       inventoryData
//     }
//   },

//   // Get top selling items
//   async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {
//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
//     const end = endDate || new Date().toISOString()

//     const { data, error } = await supabase
//       .from('sales_transaction_items')
//       .select(`
//         quantity,
//         total_price,
//         menu_item:menu_items(name, category),
//         transaction:sales_transactions!inner(created_at, branch_id)
//       `)
//       .eq('transaction.branch_id', branchId)
//       .gte('transaction.created_at', start)
//       .lte('transaction.created_at', end)

//     if (error) throw error

//     // Aggregate by menu item
//     const itemStats = data.reduce((acc: any, item) => {
//       const itemName = item.menu_item.name
//       if (!acc[itemName]) {
//         acc[itemName] = {
//           name: itemName,
//           category: item.menu_item.category,
//           totalQuantity: 0,
//           totalRevenue: 0
//         }
//       }
//       acc[itemName].totalQuantity += item.quantity
//       acc[itemName].totalRevenue += item.total_price
//       return acc
//     }, {})

//     return Object.values(itemStats)
//       .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)
//       .slice(0, limit)
//   }
// }
// Analytics and Reporting Functions
export const analyticsService = {
  // Get branch performance metrics
  async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const end = endDate || new Date().toISOString()
    
    // Get sales data
    const { data: salesData, error: salesError } = await supabase
      .from('sales_transactions')
      .select('total_amount, created_at, payment_method')
      .eq('branch_id', branchId)
      .gte('created_at', start)
      .lte('created_at', end)
    
    if (salesError) throw salesError
    
    // First get warehouse IDs for the branch
    const { data: warehouseIds, error: warehouseError } = await supabase
      .from('warehouses')
      .select('id')
      .eq('branch_id', branchId)
    
    if (warehouseError) throw warehouseError
    
    // Get inventory data using warehouse IDs
    const warehouseIdList = warehouseIds.map(w => w.id)
    const { data: inventoryData, error: inventoryError } = await supabase
      .from('inventory')
      .select(`
        current_stock,
        minimum_stock,
        ingredient:ingredients(cost_per_unit)
      `)
      .in('warehouse_id', warehouseIdList)
    
    if (inventoryError) throw inventoryError
    
    // Calculate metrics
    const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)
    const totalTransactions = salesData.length
    const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0
    const inventoryValue = inventoryData.reduce((sum, item) =>
      sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0
    )
    const lowStockItems = inventoryData.filter(item =>
      item.current_stock <= item.minimum_stock
    ).length
    
    return {
      totalRevenue,
      totalTransactions,
      averageTransaction,
      inventoryValue,
      lowStockItems,
      salesData,
      inventoryData
    }
  },

  // Get top selling items
  async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const end = endDate || new Date().toISOString()
    
    const { data, error } = await supabase
      .from('sales_transaction_items')
      .select(`
        quantity,
        total_price,
        menu_item:menu_items(name, category),
        sales_transactions!inner(created_at, branch_id)
      `)
      .eq('sales_transactions.branch_id', branchId)
      .gte('sales_transactions.created_at', start)
      .lte('sales_transactions.created_at', end)
    
    if (error) throw error
    
    // Aggregate by menu item
    const itemStats = data.reduce((acc: any, item) => {
      const itemName = item.menu_item.name
      if (!acc[itemName]) {
        acc[itemName] = {
          name: itemName,
          category: item.menu_item.category,
          totalQuantity: 0,
          totalRevenue: 0
        }
      }
      acc[itemName].totalQuantity += item.quantity
      acc[itemName].totalRevenue += item.total_price
      return acc
    }, {})
    
    return Object.values(itemStats)
      .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)
      .slice(0, limit)
  },

  // Alternative approach for top selling items using RPC if the above doesn't work
  async getTopSellingItemsRPC(branchId: string, limit = 10, startDate?: string, endDate?: string) {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const end = endDate || new Date().toISOString()
    
    // Call a stored procedure/function for complex aggregation
    const { data, error } = await supabase
      .rpc('get_top_selling_items', {
        branch_id: branchId,
        start_date: start,
        end_date: end,
        item_limit: limit
      })
    
    if (error) throw error
    return data
  }
}
