'use client'

import { useState, useEffect } from 'react'
import { productionService } from '@/lib/database'
import { useAuth } from '@/contexts/AuthContext'

interface QualityCheckpoint {
  id: string
  checkpoint_name: string
  checkpoint_type: 'preparation' | 'cooking' | 'plating' | 'final'
  status: 'pending' | 'passed' | 'failed' | 'requires_attention'
  score: number
  notes: string
  checked_at: string
  checker: {
    full_name: string
  }
}

interface ProductionBatch {
  id: string
  batch_number: string
  planned_quantity: number
  actual_quantity: number
  status: string
  quality_score: number
  quality_notes: string
  recipe: {
    id: string
    name: string
    code: string
  }
}

interface QualityControlModalProps {
  isOpen: boolean
  onClose: () => void
  batch: ProductionBatch | null
  onQualityUpdated: () => void
}

export default function QualityControlModal({ 
  isOpen, 
  onClose, 
  batch, 
  onQualityUpdated 
}: QualityControlModalProps) {
  const { profile } = useAuth()
  const [checkpoints, setCheckpoints] = useState<QualityCheckpoint[]>([])
  const [newCheckpoint, setNewCheckpoint] = useState({
    name: '',
    type: 'preparation' as 'preparation' | 'cooking' | 'plating' | 'final',
    status: 'pending' as 'pending' | 'passed' | 'failed' | 'requires_attention',
    score: 8,
    notes: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (isOpen && batch) {
      loadQualityCheckpoints()
    }
  }, [isOpen, batch])

  const loadQualityCheckpoints = async () => {
    if (!batch) return

    try {
      setLoading(true)
      const data = await productionService.getQualityCheckpoints(batch.id)
      setCheckpoints(data)
    } catch (err) {
      setError('Failed to load quality checkpoints')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const handleAddCheckpoint = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!batch || !profile?.id) return

    try {
      setLoading(true)
      await productionService.createQualityCheckpoint(
        batch.id,
        newCheckpoint.name,
        newCheckpoint.type,
        newCheckpoint.status,
        newCheckpoint.score,
        newCheckpoint.notes,
        profile.id
      )

      // Reset form
      setNewCheckpoint({
        name: '',
        type: 'preparation',
        status: 'pending',
        score: 8,
        notes: ''
      })

      // Reload checkpoints
      await loadQualityCheckpoints()
      onQualityUpdated()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add quality checkpoint')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'bg-green-100 text-green-800'
      case 'failed': return 'bg-red-100 text-red-800'
      case 'requires_attention': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'preparation': return '🔪'
      case 'cooking': return '🍳'
      case 'plating': return '🍽️'
      case 'final': return '✅'
      default: return '📋'
    }
  }

  const handleClose = () => {
    setError(null)
    setNewCheckpoint({
      name: '',
      type: 'preparation',
      status: 'pending',
      score: 8,
      notes: ''
    })
    onClose()
  }

  if (!isOpen || !batch) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Quality Control</h2>
            <p className="text-sm text-gray-600">
              Batch: {batch.batch_number} - {batch.recipe.name}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <span className="sr-only">Close</span>
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Existing Checkpoints */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quality Checkpoints</h3>
            <div className="space-y-3">
              {checkpoints.map((checkpoint) => (
                <div key={checkpoint.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getTypeIcon(checkpoint.checkpoint_type)}</span>
                      <h4 className="font-medium text-gray-900">{checkpoint.checkpoint_name}</h4>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(checkpoint.status)}`}>
                      {checkpoint.status.replace('_', ' ')}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Score:</span>
                      <span className="ml-2 font-medium">{checkpoint.score}/10</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Type:</span>
                      <span className="ml-2 font-medium capitalize">{checkpoint.checkpoint_type}</span>
                    </div>
                  </div>
                  {checkpoint.notes && (
                    <p className="text-sm text-gray-600 mt-2">{checkpoint.notes}</p>
                  )}
                  <div className="text-xs text-gray-500 mt-2">
                    Checked by {checkpoint.checker.full_name} on {new Date(checkpoint.checked_at).toLocaleString()}
                  </div>
                </div>
              ))}
              {checkpoints.length === 0 && (
                <p className="text-gray-500 text-center py-8">No quality checkpoints recorded yet</p>
              )}
            </div>
          </div>

          {/* Add New Checkpoint */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Add Quality Checkpoint</h3>
            <form onSubmit={handleAddCheckpoint} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Checkpoint Name
                </label>
                <input
                  type="text"
                  value={newCheckpoint.name}
                  onChange={(e) => setNewCheckpoint({ ...newCheckpoint, name: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="e.g., Spice level check"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Checkpoint Type
                </label>
                <select
                  value={newCheckpoint.type}
                  onChange={(e) => setNewCheckpoint({ ...newCheckpoint, type: e.target.value as any })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="preparation">Preparation</option>
                  <option value="cooking">Cooking</option>
                  <option value="plating">Plating</option>
                  <option value="final">Final</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={newCheckpoint.status}
                  onChange={(e) => setNewCheckpoint({ ...newCheckpoint, status: e.target.value as any })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="pending">Pending</option>
                  <option value="passed">Passed</option>
                  <option value="failed">Failed</option>
                  <option value="requires_attention">Requires Attention</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quality Score (1-10)
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={newCheckpoint.score}
                  onChange={(e) => setNewCheckpoint({ ...newCheckpoint, score: parseInt(e.target.value) })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  value={newCheckpoint.notes}
                  onChange={(e) => setNewCheckpoint({ ...newCheckpoint, notes: e.target.value })}
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="Quality observations, issues, or recommendations..."
                />
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
              >
                {loading ? 'Adding...' : 'Add Checkpoint'}
              </button>
            </form>
          </div>
        </div>

        <div className="flex justify-end pt-6">
          <button
            onClick={handleClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}
