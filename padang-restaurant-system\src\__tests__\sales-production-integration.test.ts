import { describe, it, expect, vi, beforeEach } from 'vitest'
import { salesService } from '@/lib/database'

// Mock Supabase
const mockSupabase = {
  from: vi.fn(),
  select: vi.fn(),
  insert: vi.fn(),
  update: vi.fn(),
  eq: vi.fn(),
  single: vi.fn(),
  order: vi.fn(),
  gte: vi.fn(),
  lte: vi.fn(),
  in: vi.fn(),
}

vi.mock('@/lib/supabase', () => ({
  supabase: mockSupabase
}))

describe('Sales-Production Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('createSalesTransactionWithInventory', () => {
    it('should create sales transaction and update inventory', async () => {
      const mockTransaction = {
        id: 'transaction-1',
        transaction_number: 'TXN-123',
        total_amount: 50000
      }

      const mockMenuItem = {
        id: 'menu-1',
        name: 'Rendang',
        recipe: {
          recipe_ingredients: [
            {
              ingredient_id: 'ingredient-1',
              quantity: 0.2,
              unit: 'kg'
            }
          ]
        }
      }

      const mockWarehouses = [{ id: 'warehouse-1' }]
      const mockInventory = {
        id: 'inventory-1',
        current_stock: 10
      }

      const items = [
        {
          menu_item_id: 'menu-1',
          quantity: 2,
          price: 25000,
          special_instructions: ''
        }
      ]

      const customerInfo = { name: 'John Doe', phone: '*********' }
      const paymentInfo = { method: 'cash', tax_amount: 0, discount_amount: 0 }

      // Mock the chain of calls
      const mockSelect = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      const mockSingle = vi.fn()
      const mockInsert = vi.fn().mockReturnThis()
      const mockUpdate = vi.fn().mockReturnThis()

      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'sales_transactions') {
          mockSingle.mockResolvedValueOnce({ data: mockTransaction, error: null })
          return { insert: mockInsert }
        }
        if (table === 'menu_items') {
          mockSingle.mockResolvedValueOnce({ data: mockMenuItem, error: null })
          return { select: mockSelect }
        }
        if (table === 'warehouses') {
          return { select: mockSelect }
        }
        if (table === 'inventory') {
          if (mockSelect.mock.calls.length === 2) {
            mockSingle.mockResolvedValueOnce({ data: mockInventory, error: null })
            return { select: mockSelect }
          } else {
            return { update: mockUpdate }
          }
        }
        if (table === 'sales_transaction_items') {
          return { insert: mockInsert }
        }
        if (table === 'stock_movements') {
          return { insert: mockInsert }
        }
        return { select: mockSelect }
      })

      mockSelect.mockImplementation(() => {
        if (mockSelect.mock.calls.length === 1) {
          // warehouses query
          return { eq: vi.fn().mockResolvedValue({ data: mockWarehouses, error: null }) }
        }
        return { eq: mockEq }
      })
      
      mockEq.mockReturnValue({ single: mockSingle })
      mockInsert.mockReturnValue({ select: mockSelect })
      mockUpdate.mockReturnValue({ eq: vi.fn().mockResolvedValue({ error: null }) })

      const result = await salesService.createSalesTransactionWithInventory(
        'branch-1',
        items,
        customerInfo,
        paymentInfo,
        'user-1'
      )

      expect(result).toEqual(mockTransaction)
      expect(mockSupabase.from).toHaveBeenCalledWith('sales_transactions')
      expect(mockSupabase.from).toHaveBeenCalledWith('menu_items')
      expect(mockSupabase.from).toHaveBeenCalledWith('inventory')
      expect(mockSupabase.from).toHaveBeenCalledWith('stock_movements')
    })
  })

  describe('generateProductionRecommendations', () => {
    it('should generate production recommendations based on sales data', async () => {
      const mockSalesData = [
        {
          quantity: 5,
          created_at: '2024-01-01T10:00:00Z',
          menu_item: {
            id: 'menu-1',
            name: 'Rendang',
            recipe: {
              id: 'recipe-1',
              name: 'Rendang Recipe'
            }
          }
        },
        {
          quantity: 3,
          created_at: '2024-01-02T10:00:00Z',
          menu_item: {
            id: 'menu-1',
            name: 'Rendang',
            recipe: {
              id: 'recipe-1',
              name: 'Rendang Recipe'
            }
          }
        }
      ]

      const mockWarehouses = [{ id: 'warehouse-1' }]
      const mockInventory = { current_stock: 5 }

      const mockSelect = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      const mockGte = vi.fn().mockReturnThis()
      const mockSingle = vi.fn()

      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'sales_transaction_items') {
          mockGte.mockResolvedValueOnce({ data: mockSalesData, error: null })
          return { select: mockSelect }
        }
        if (table === 'warehouses') {
          return { select: mockSelect }
        }
        if (table === 'inventory') {
          mockSingle.mockResolvedValueOnce({ data: mockInventory, error: null })
          return { select: mockSelect }
        }
        return { select: mockSelect }
      })

      mockSelect.mockImplementation(() => {
        if (mockSelect.mock.calls.length === 1) {
          // sales_transaction_items query
          return { eq: mockEq }
        } else if (mockSelect.mock.calls.length === 2) {
          // warehouses query
          return { eq: vi.fn().mockResolvedValue({ data: mockWarehouses, error: null }) }
        } else {
          // inventory query
          return { eq: mockEq }
        }
      })

      mockEq.mockImplementation(() => {
        if (mockEq.mock.calls.length === 1) {
          // sales_transaction_items eq
          return { gte: mockGte }
        } else {
          // inventory eq
          return { single: mockSingle }
        }
      })

      const result = await salesService.generateProductionRecommendations('branch-1', 7)

      expect(result.recommendations).toBeDefined()
      expect(result.totalRecommendations).toBeGreaterThanOrEqual(0)
      expect(result.forecastPeriod).toBe(7)
      
      if (result.recommendations.length > 0) {
        const recommendation = result.recommendations[0]
        expect(recommendation).toHaveProperty('recipeId')
        expect(recommendation).toHaveProperty('recipeName')
        expect(recommendation).toHaveProperty('recommendedProduction')
        expect(recommendation).toHaveProperty('priority')
      }
    })

    it('should handle empty sales data gracefully', async () => {
      const mockSelect = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      const mockGte = vi.fn().mockResolvedValue({ data: [], error: null })

      mockSupabase.from.mockReturnValue({ select: mockSelect })
      mockSelect.mockReturnValue({ eq: mockEq })
      mockEq.mockReturnValue({ gte: mockGte })

      const result = await salesService.generateProductionRecommendations('branch-1', 7)

      expect(result.recommendations).toEqual([])
      expect(result.totalRecommendations).toBe(0)
    })
  })

  describe('Integration Scenarios', () => {
    it('should handle complete production-to-sales workflow', async () => {
      // This test simulates the complete workflow:
      // 1. Create production plan based on demand forecast
      // 2. Start production batch
      // 3. Complete production with finished goods
      // 4. Sell items and consume inventory
      
      // Mock data for the workflow
      const mockPlan = { id: 'plan-1', status: 'approved' }
      const mockBatch = { id: 'batch-1', status: 'completed' }
      const mockTransaction = { id: 'transaction-1' }

      const mockSelect = vi.fn().mockReturnThis()
      const mockInsert = vi.fn().mockReturnThis()
      const mockUpdate = vi.fn().mockReturnThis()
      const mockSingle = vi.fn()

      // Mock all database operations
      mockSupabase.from.mockImplementation((table: string) => {
        switch (table) {
          case 'production_plans':
            mockSingle.mockResolvedValueOnce({ data: mockPlan, error: null })
            return { insert: mockInsert }
          case 'production_batches':
            mockSingle.mockResolvedValueOnce({ data: mockBatch, error: null })
            return { insert: mockInsert }
          case 'sales_transactions':
            mockSingle.mockResolvedValueOnce({ data: mockTransaction, error: null })
            return { insert: mockInsert }
          default:
            return { select: mockSelect, insert: mockInsert, update: mockUpdate }
        }
      })

      mockInsert.mockReturnValue({ select: mockSelect })
      mockSelect.mockReturnValue({ single: mockSingle })

      // Test that the workflow can be executed without errors
      expect(async () => {
        // This would be a more complex integration test
        // involving multiple service calls in sequence
        const plan = await mockSupabase.from('production_plans').insert({}).select().single()
        const batch = await mockSupabase.from('production_batches').insert({}).select().single()
        const transaction = await mockSupabase.from('sales_transactions').insert({}).select().single()
        
        return { plan, batch, transaction }
      }).not.toThrow()
    })
  })
})
