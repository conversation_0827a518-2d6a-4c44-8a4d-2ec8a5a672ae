"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InventoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(app-pages-browser)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_inventory_AddStockModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/inventory/AddStockModal */ \"(app-pages-browser)/./src/components/inventory/AddStockModal.tsx\");\n/* harmony import */ var _components_inventory_UpdateStockModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/inventory/UpdateStockModal */ \"(app-pages-browser)/./src/components/inventory/UpdateStockModal.tsx\");\n/* harmony import */ var _components_inventory_TransferStockModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/inventory/TransferStockModal */ \"(app-pages-browser)/./src/components/inventory/TransferStockModal.tsx\");\n/* harmony import */ var _components_inventory_BulkUpdateModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/inventory/BulkUpdateModal */ \"(app-pages-browser)/./src/components/inventory/BulkUpdateModal.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction InventoryPage() {\n    var _warehouses_find;\n    _s();\n    const { profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [inventory, setInventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [finishedGoods, setFinishedGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [warehouses, setWarehouses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWarehouse, setSelectedWarehouse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [lowStockItems, setLowStockItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('raw_ingredients');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isInventoryLoading, setIsInventoryLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Modal states\n    const [isAddStockModalOpen, setIsAddStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdateStockModalOpen, setIsUpdateStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferStockModalOpen, setIsTransferStockModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedItem, setSelectedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadWarehouses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadWarehouses]\": async ()=>{\n            try {\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_9__.warehouseService.getAllWarehouses();\n                setWarehouses(data);\n                // Auto-select first warehouse if available and no warehouse is selected\n                if (data.length > 0 && !selectedWarehouse) {\n                    setSelectedWarehouse(data[0].id);\n                }\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load warehouses';\n                setError(errorMessage);\n                console.error('Error loading warehouses:', err);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadWarehouses]\"], [\n        selectedWarehouse\n    ]);\n    const loadInventory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadInventory]\": async (warehouseId)=>{\n            if (!warehouseId) return;\n            try {\n                setIsInventoryLoading(true);\n                setError(null) // Clear previous errors\n                ;\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_9__.inventoryService.getWarehouseInventory(warehouseId);\n                setInventory(data || []) // Ensure we always have an array\n                ;\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load inventory';\n                setError(errorMessage);\n                console.error('Error loading inventory:', err);\n                setInventory([]) // Reset inventory on error\n                ;\n            } finally{\n                setIsInventoryLoading(false);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadInventory]\"], []);\n    const loadFinishedGoods = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadFinishedGoods]\": async (warehouseId)=>{\n            if (!warehouseId) return;\n            try {\n                setIsInventoryLoading(true);\n                setError(null);\n                // This would be a new service method to get finished goods inventory\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_10__.supabase.from('finished_goods_inventory').select(\"\\n          *,\\n          menu_item:menu_items(id, name, category),\\n          warehouse:warehouses(id, name, code)\\n        \").eq('warehouse_id', warehouseId);\n                if (error) throw error;\n                setFinishedGoods(data || []);\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to load finished goods';\n                setError(errorMessage);\n                console.error('Error loading finished goods:', err);\n                setFinishedGoods([]);\n            } finally{\n                setIsInventoryLoading(false);\n            }\n        }\n    }[\"InventoryPage.useCallback[loadFinishedGoods]\"], []);\n    const loadLowStockItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InventoryPage.useCallback[loadLowStockItems]\": async ()=>{\n            try {\n                const data = await _lib_database__WEBPACK_IMPORTED_MODULE_9__.inventoryService.getLowStockItems();\n                setLowStockItems(data || []);\n            } catch (err) {\n                console.error('Failed to load low stock items:', err);\n            // Don't set error state for this as it's not critical\n            }\n        }\n    }[\"InventoryPage.useCallback[loadLowStockItems]\"], []);\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InventoryPage.useEffect\": ()=>{\n            const initializeData = {\n                \"InventoryPage.useEffect.initializeData\": async ()=>{\n                    setIsLoading(true);\n                    await Promise.all([\n                        loadWarehouses(),\n                        loadLowStockItems()\n                    ]);\n                    setIsLoading(false);\n                }\n            }[\"InventoryPage.useEffect.initializeData\"];\n            initializeData();\n        }\n    }[\"InventoryPage.useEffect\"], [\n        loadWarehouses,\n        loadLowStockItems\n    ]);\n    // Load inventory when warehouse changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InventoryPage.useEffect\": ()=>{\n            if (selectedWarehouse) {\n                loadInventory(selectedWarehouse);\n            } else {\n                setInventory([]);\n            }\n        }\n    }[\"InventoryPage.useEffect\"], [\n        selectedWarehouse,\n        loadInventory\n    ]);\n    const getStockStatus = (item)=>{\n        if (item.current_stock <= item.minimum_stock) {\n            return {\n                status: 'critical',\n                color: 'text-red-600 bg-red-100'\n            };\n        } else if (item.current_stock <= item.reorder_point) {\n            return {\n                status: 'low',\n                color: 'text-yellow-600 bg-yellow-100'\n            };\n        } else {\n            return {\n                status: 'good',\n                color: 'text-green-600 bg-green-100'\n            };\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('id-ID', {\n            style: 'currency',\n            currency: 'IDR',\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const calculateInventoryStats = ()=>{\n        const totalItems = inventory.length;\n        const lowStockCount = inventory.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        const reorderNeededCount = inventory.filter((item)=>item.current_stock <= item.reorder_point).length;\n        const totalValue = inventory.reduce((total, item)=>{\n            const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0);\n            return total + itemValue;\n        }, 0);\n        return {\n            totalItems,\n            lowStockCount,\n            reorderNeededCount,\n            totalValue\n        };\n    };\n    const handleWarehouseChange = (event)=>{\n        setSelectedWarehouse(event.target.value);\n    };\n    const handleAddStock = ()=>{\n        setIsAddStockModalOpen(true);\n    };\n    const handleBulkUpdate = ()=>{\n        setIsBulkUpdateModalOpen(true);\n    };\n    const handleUpdateStock = (itemId)=>{\n        const item = inventory.find((i)=>i.id === itemId);\n        if (item) {\n            setSelectedItem(item);\n            setIsUpdateStockModalOpen(true);\n        }\n    };\n    const handleTransferStock = (itemId)=>{\n        const item = inventory.find((i)=>i.id === itemId);\n        if (item) {\n            setSelectedItem(item);\n            setIsTransferStockModalOpen(true);\n        }\n    };\n    const handleStockUpdated = ()=>{\n        if (selectedWarehouse) {\n            loadInventory(selectedWarehouse);\n        }\n        loadLowStockItems();\n    };\n    const stats = calculateInventoryStats();\n    // Show loading spinner during initial load\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            allowedRoles: [\n                'admin',\n                'manager',\n                'staff'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        allowedRoles: [\n            'admin',\n            'manager',\n            'staff'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Inventory Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedWarehouse,\n                                        onChange: handleWarehouseChange,\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                                        disabled: warehouses.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select Warehouse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this),\n                                            warehouses.map((warehouse)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: warehouse.id,\n                                                    children: [\n                                                        warehouse.name,\n                                                        \" (\",\n                                                        warehouse.code,\n                                                        \")\"\n                                                    ]\n                                                }, warehouse.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleAddStock,\n                                                disabled: !selectedWarehouse,\n                                                className: \"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                                children: \"Add Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleBulkUpdate,\n                                                disabled: !selectedWarehouse || inventory.length === 0,\n                                                className: \"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                                children: \"Bulk Update\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n                        role: \"alert\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block sm:inline\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setError(null),\n                                className: \"absolute top-0 bottom-0 right-0 px-4 py-3\",\n                                \"aria-label\": \"Close error message\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 13\n                    }, this),\n                    lowStockItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400 text-xl mr-3\",\n                                    \"aria-hidden\": \"true\",\n                                    children: \"⚠️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-red-800\",\n                                            children: \"Low Stock Alert\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-700\",\n                                            children: [\n                                                lowStockItems.length,\n                                                \" item\",\n                                                lowStockItems.length !== 1 ? 's' : '',\n                                                lowStockItems.length === 1 ? ' is' : ' are',\n                                                \" running low across all warehouses\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDCE6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Items\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: stats.totalItems\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"⚠️\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Low Stock Items\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-red-600\",\n                                                            children: stats.lowStockCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDCB0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Total Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: formatCurrency(stats.totalValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-5 w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                            className: \"text-sm font-medium text-gray-500 truncate\",\n                                                            children: \"Reorder Needed\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                            className: \"text-lg font-medium text-yellow-600\",\n                                                            children: stats.reorderNeededCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow overflow-hidden sm:rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg leading-6 font-medium text-gray-900\",\n                                    children: [\n                                        \"Current Inventory\",\n                                        selectedWarehouse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"- \",\n                                                ((_warehouses_find = warehouses.find((w)=>w.id === selectedWarehouse)) === null || _warehouses_find === void 0 ? void 0 : _warehouses_find.name) || 'Unknown Warehouse'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this),\n                            isInventoryLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 15\n                            }, this) : !selectedWarehouse ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: \"Please select a warehouse to view inventory\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 15\n                            }, this) : inventory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: \"No inventory items found for this warehouse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Ingredient\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Current Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Min/Max\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: inventory.map((item)=>{\n                                                const stockStatus = getStockStatus(item);\n                                                const itemValue = item.current_stock * (item.ingredient.cost_per_unit || 0);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: item.ingredient.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: item.ingredient.code\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    item.current_stock.toLocaleString(),\n                                                                    \" \",\n                                                                    item.ingredient.unit\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(stockStatus.color),\n                                                                children: stockStatus.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: [\n                                                                item.minimum_stock.toLocaleString(),\n                                                                \" / \",\n                                                                item.maximum_stock.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: formatCurrency(itemValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleUpdateStock(item.id),\n                                                                    className: \"text-indigo-600 hover:text-indigo-900 mr-3 transition-colors\",\n                                                                    children: \"Update\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleTransferStock(item.id),\n                                                                    className: \"text-green-600 hover:text-green-900 transition-colors\",\n                                                                    children: \"Transfer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_AddStockModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isOpen: isAddStockModalOpen,\n                        onClose: ()=>setIsAddStockModalOpen(false),\n                        warehouseId: selectedWarehouse,\n                        onStockAdded: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_UpdateStockModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: isUpdateStockModalOpen,\n                        onClose: ()=>setIsUpdateStockModalOpen(false),\n                        item: selectedItem,\n                        onStockUpdated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_TransferStockModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        isOpen: isTransferStockModalOpen,\n                        onClose: ()=>setIsTransferStockModalOpen(false),\n                        item: selectedItem,\n                        onTransferCreated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_BulkUpdateModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        isOpen: isBulkUpdateModalOpen,\n                        onClose: ()=>setIsBulkUpdateModalOpen(false),\n                        items: inventory,\n                        onBulkUpdated: handleStockUpdated\n                    }, void 0, false, {\n                        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\padanghub_supabase\\\\padang-restaurant-system\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, this);\n}\n_s(InventoryPage, \"BwHEq5rByXHqlQ5sYvuA4SMiFJg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = InventoryPage;\nvar _c;\n$RefreshReg$(_c, \"InventoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inventory/page.tsx\n"));

/***/ })

});