# Inventory Flow & Stock Counting Optimization Guide

## Current System Analysis

### 🔍 **Current Implementation Issues**

1. **Inconsistent Stock Consumption Model**
   - POS system consumes raw ingredients directly instead of finished goods
   - No proper finished goods inventory tracking
   - Production output doesn't integrate with POS consumption

2. **Menu Availability Logic Gaps**
   - Availability based on manual flags and daily quotas only
   - No real-time inventory-based availability checking
   - Missing integration between production capacity and menu availability

3. **Hybrid Inventory System Problems**
   - Same `inventory` table used for both raw materials and finished goods
   - Using `recipe_id` as `ingredient_id` creates confusion
   - No clear separation between production inputs and outputs

## 🎯 **Optimized Inventory Flow Architecture**

### **1. Stock Source & Flow Model**

```
Raw Ingredients → Production Process → Finished Goods → POS Sales
     ↓                    ↓                ↓             ↓
  Inventory           Batch Tracking   Finished Goods   Customer
   Table              & Quality        Inventory        Orders
                      Control          Table
```

### **2. Enhanced Database Schema**

#### **Finished Goods Inventory Table**
```sql
CREATE TABLE finished_goods_inventory (
    id UUID PRIMARY KEY,
    warehouse_id UUID REFERENCES warehouses(id),
    menu_item_id UUID REFERENCES menu_items(id),
    current_stock DECIMAL(10,3),
    minimum_stock DECIMAL(10,3),
    cost_per_unit DECIMAL(10,2),
    last_produced_at TIMESTAMP,
    production_batch_id UUID REFERENCES production_batches(id)
);
```

#### **Enhanced Stock Movements**
```sql
CREATE TABLE enhanced_stock_movements (
    id UUID PRIMARY KEY,
    item_type TEXT CHECK (item_type IN ('raw_ingredient', 'finished_goods')),
    item_id UUID, -- ingredient_id or menu_item_id
    movement_type TEXT CHECK (movement_type IN ('production_input', 'production_output', 'sale', 'waste')),
    quantity DECIMAL(10,3),
    reference_type TEXT,
    reference_id UUID
);
```

### **3. Real-time Availability Logic**

#### **Menu Item Availability Determination**
```typescript
async checkMenuItemAvailability(branchId: string, menuItemId: string) {
  // 1. Check finished goods inventory
  const finishedStock = await getFinishedGoodsStock(branchId, menuItemId)
  
  if (finishedStock > 0) {
    return { available: true, reason: 'in_stock', stock: finishedStock }
  }
  
  // 2. Check if can produce (raw ingredients available)
  const canProduce = await checkProductionCapability(menuItemId, branchId)
  
  if (canProduce.possible) {
    return { 
      available: true, 
      reason: 'can_produce', 
      estimated_time: canProduce.time 
    }
  }
  
  // 3. Not available
  return { 
    available: false, 
    reason: 'out_of_stock', 
    missing: canProduce.missing_ingredients 
  }
}
```

## 🔄 **Optimized Stock Consumption Flow**

### **Production to Finished Goods**
```typescript
// When production batch completes
async completeProductionBatch(batchId: string, actualQuantity: number) {
  // 1. Update production batch status
  await updateProductionBatch(batchId, { status: 'completed', actualQuantity })
  
  // 2. Add to finished goods inventory
  await addFinishedGoods({
    menu_item_id: batch.menu_item_id,
    warehouse_id: batch.warehouse_id,
    quantity: actualQuantity,
    cost_per_unit: calculateCostPerUnit(batchId),
    production_batch_id: batchId
  })
  
  // 3. Record stock movement
  await recordStockMovement({
    item_type: 'finished_goods',
    movement_type: 'production_output',
    quantity: actualQuantity,
    reference_type: 'production',
    reference_id: batchId
  })
  
  // 4. Update menu availability
  await updateMenuAvailability(batch.menu_item_id)
}
```

### **POS Sales Consumption**
```typescript
// When customer orders through POS
async processSalesOrder(branchId: string, orderItems: OrderItem[]) {
  for (const item of orderItems) {
    // 1. Try to consume from finished goods first
    const consumed = await consumeFinishedGoods(
      branchId, 
      item.menu_item_id, 
      item.quantity
    )
    
    if (consumed.success) {
      // Record finished goods consumption
      await recordStockMovement({
        item_type: 'finished_goods',
        movement_type: 'sale',
        quantity: item.quantity,
        reference_type: 'sale',
        reference_id: transactionId
      })
    } else {
      // 2. If no finished goods, trigger production or mark as backorder
      await triggerProductionRequest(item.menu_item_id, item.quantity)
    }
  }
}
```

## 📊 **Real-time Updates Implementation**

### **Automatic Availability Updates**
```sql
-- Trigger to update menu availability when finished goods change
CREATE TRIGGER update_menu_availability
    AFTER INSERT OR UPDATE ON finished_goods_inventory
    FOR EACH ROW
    EXECUTE FUNCTION update_menu_availability_cache();
```

### **Performance Optimization**
```sql
-- Availability cache table for fast lookups
CREATE TABLE menu_item_availability_cache (
    branch_id UUID,
    menu_item_id UUID,
    is_available BOOLEAN,
    availability_reason TEXT,
    current_stock DECIMAL(10,3),
    can_produce BOOLEAN,
    last_updated TIMESTAMP
);
```

## 🎯 **Implementation Roadmap**

### **Phase 1: Database Schema Enhancement**
1. Create `finished_goods_inventory` table
2. Create `enhanced_stock_movements` table
3. Create `menu_item_availability_cache` table
4. Add database triggers for automatic updates

### **Phase 2: Service Layer Updates**
1. Implement `enhancedInventoryService`
2. Update production completion to create finished goods
3. Update POS sales to consume finished goods
4. Implement real-time availability checking

### **Phase 3: UI Integration**
1. Update POS interface to show real-time availability
2. Add finished goods inventory management
3. Create inventory dashboard with both raw and finished goods
4. Add production recommendations based on finished goods levels

### **Phase 4: Testing & Optimization**
1. Comprehensive testing of new flow
2. Performance optimization
3. Data migration from current system
4. Staff training on new processes

## 🔧 **Migration Strategy**

### **Data Migration Script**
```sql
-- Migrate existing production data to finished goods inventory
INSERT INTO finished_goods_inventory (
    warehouse_id, menu_item_id, current_stock, cost_per_unit, last_produced_at
)
SELECT 
    k.warehouse_id,
    mi.id,
    COALESCE(SUM(pb.actual_quantity), 0),
    AVG(pc.cost_per_unit),
    MAX(pb.actual_end_time)
FROM menu_items mi
JOIN recipes r ON r.id = mi.recipe_id
JOIN production_batches pb ON pb.recipe_id = r.id AND pb.status = 'completed'
JOIN kitchens k ON k.id = pb.kitchen_id
LEFT JOIN production_costs pc ON pc.batch_id = pb.id
GROUP BY k.warehouse_id, mi.id;
```

## 📈 **Expected Benefits**

1. **Accurate Inventory Tracking**: Clear separation between raw materials and finished goods
2. **Real-time Availability**: Menu items show actual availability based on stock levels
3. **Better Production Planning**: Finished goods levels drive production recommendations
4. **Improved Customer Experience**: Accurate availability prevents order cancellations
5. **Cost Control**: Better tracking of production costs and finished goods valuation
6. **Operational Efficiency**: Automated availability updates reduce manual management

## 🚀 **Quick Start Implementation**

To implement the optimized inventory flow:

1. **Run the enhanced schema**: Execute `enhanced-inventory-schema.sql`
2. **Deploy the service**: Add `enhanced-inventory-flow.ts` to your services
3. **Update POS integration**: Use the new availability checking methods
4. **Migrate existing data**: Run the migration scripts
5. **Test thoroughly**: Validate the new flow with test transactions

This optimization transforms your restaurant management system from a basic ingredient-tracking system to a comprehensive inventory management solution that properly handles the complete flow from raw materials through production to customer sales.
