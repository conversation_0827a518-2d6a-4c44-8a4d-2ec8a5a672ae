"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/production/page",{

/***/ "(app-pages-browser)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsService: () => (/* binding */ analyticsService),\n/* harmony export */   branchService: () => (/* binding */ branchService),\n/* harmony export */   ingredientService: () => (/* binding */ ingredientService),\n/* harmony export */   inventoryService: () => (/* binding */ inventoryService),\n/* harmony export */   kitchenService: () => (/* binding */ kitchenService),\n/* harmony export */   menuService: () => (/* binding */ menuService),\n/* harmony export */   productionService: () => (/* binding */ productionService),\n/* harmony export */   salesService: () => (/* binding */ salesService),\n/* harmony export */   warehouseService: () => (/* binding */ warehouseService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _auditLog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auditLog */ \"(app-pages-browser)/./src/lib/auditLog.ts\");\n\n\n// Inventory Management Functions\nconst inventoryService = {\n    // Get all inventory items for a warehouse\n    async getWarehouseInventory (warehouseId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*)\\n      \").eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get low stock items across all warehouses (optimized with database filtering)\n    async getLowStockItems (branchId) {\n        // Use a more efficient query with database-side filtering\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('get_low_stock_items_optimized', {\n            branch_id_param: branchId || null\n        });\n        if (error) {\n            // Fallback to client-side filtering if RPC function doesn't exist\n            console.warn('RPC function not available, using fallback query');\n            return this.getLowStockItemsFallback(branchId);\n        }\n        return data || [];\n    },\n    // Fallback method for low stock items\n    async getLowStockItemsFallback (branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*)\\n      \").filter('current_stock', 'lt', 'minimum_stock') // Database-side filtering\n        ;\n        if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query.order('current_stock', {\n            ascending: true\n        }).limit(100) // Limit results for performance\n        ;\n        if (error) throw error;\n        // Filter low stock items in JavaScript\n        // const lowStockItems = data?.filter(item =>\n        //   item.current_stock < item.minimum_stock\n        // ).sort((a, b) => a.current_stock - b.current_stock)\n        // return lowStockItems\n        return data || [];\n    },\n    // async getLowStockItems(branchId?: string) {\n    //   let query = supabase\n    //     .from('inventory')\n    //     .select(`\n    //       *,\n    //       ingredient:ingredients(*),\n    //       warehouse:warehouses(*)\n    //     `)\n    //     .filter('current_stock', 'lt', 10)\n    //   if (branchId) {\n    //     query = query.eq('warehouses.branch_id', branchId)\n    //   }\n    //   const { data, error } = await query.order('current_stock', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    // Update stock levels with enhanced validation and transaction safety\n    async updateStock (inventoryId, newStock, movementType, notes, performedBy) {\n        // Input validation\n        if (!inventoryId || newStock < 0) {\n            throw new Error('Invalid input: inventory ID is required and stock cannot be negative');\n        }\n        if (![\n            'in',\n            'out',\n            'adjustment',\n            'waste'\n        ].includes(movementType)) {\n            throw new Error('Invalid movement type');\n        }\n        // Fetch current inventory with detailed information\n        const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(unit, name),\\n        warehouse:warehouses(name, code)\\n      \").eq('id', inventoryId).single();\n        if (fetchError) {\n            throw new Error(\"Failed to fetch inventory: \".concat(fetchError.message));\n        }\n        const quantity = newStock - inventory.current_stock;\n        const isIncrease = quantity > 0;\n        // Business rule validation\n        if (newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n            var _inventory_ingredient;\n            throw new Error(\"Stock cannot exceed maximum limit of \".concat(inventory.maximum_stock, \" \").concat((_inventory_ingredient = inventory.ingredient) === null || _inventory_ingredient === void 0 ? void 0 : _inventory_ingredient.unit));\n        }\n        // Check if this would create negative stock for outbound movements\n        if (movementType === 'out' && newStock < 0) {\n            throw new Error('Cannot reduce stock below zero');\n        }\n        try {\n            var _inventory_ingredient1, _inventory_ingredient2, _inventory_warehouse;\n            // Use a transaction-like approach with error handling\n            const timestamp = new Date().toISOString();\n            // Update inventory record\n            const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                current_stock: newStock,\n                updated_at: timestamp,\n                last_restocked_at: isIncrease ? timestamp : inventory.last_restocked_at\n            }).eq('id', inventoryId);\n            if (updateError) {\n                throw new Error(\"Failed to update inventory: \".concat(updateError.message));\n            }\n            // Record stock movement with enhanced details\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n                warehouse_id: inventory.warehouse_id,\n                ingredient_id: inventory.ingredient_id,\n                movement_type: movementType,\n                quantity: Math.abs(quantity),\n                unit: ((_inventory_ingredient1 = inventory.ingredient) === null || _inventory_ingredient1 === void 0 ? void 0 : _inventory_ingredient1.unit) || 'kg',\n                reference_type: 'manual_adjustment',\n                notes: notes || \"Stock \".concat(movementType, \" - \").concat((_inventory_ingredient2 = inventory.ingredient) === null || _inventory_ingredient2 === void 0 ? void 0 : _inventory_ingredient2.name, \" at \").concat((_inventory_warehouse = inventory.warehouse) === null || _inventory_warehouse === void 0 ? void 0 : _inventory_warehouse.name),\n                performed_by: performedBy,\n                created_at: timestamp\n            });\n            if (movementError) {\n                // Attempt to rollback the inventory update\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: inventory.current_stock,\n                    updated_at: inventory.updated_at\n                }).eq('id', inventoryId);\n                throw new Error(\"Failed to record stock movement: \".concat(movementError.message));\n            }\n            // Log the stock change for audit trail\n            if (performedBy) {\n                try {\n                    var _inventory_ingredient3, _inventory_warehouse1, _inventory_ingredient4;\n                    await (0,_auditLog__WEBPACK_IMPORTED_MODULE_1__.logStockChange)(movementType, inventoryId, performedBy, 'staff', {\n                        ingredient_name: ((_inventory_ingredient3 = inventory.ingredient) === null || _inventory_ingredient3 === void 0 ? void 0 : _inventory_ingredient3.name) || 'Unknown',\n                        warehouse_name: ((_inventory_warehouse1 = inventory.warehouse) === null || _inventory_warehouse1 === void 0 ? void 0 : _inventory_warehouse1.name) || 'Unknown',\n                        previous_stock: inventory.current_stock,\n                        new_stock: newStock,\n                        quantity_changed: Math.abs(quantity),\n                        unit: ((_inventory_ingredient4 = inventory.ingredient) === null || _inventory_ingredient4 === void 0 ? void 0 : _inventory_ingredient4.unit) || 'kg',\n                        notes: notes\n                    }, inventory.warehouse_id);\n                } catch (auditError) {\n                    console.error('Failed to log stock change:', auditError);\n                // Don't fail the operation if audit logging fails\n                }\n            }\n            return {\n                success: true,\n                previousStock: inventory.current_stock,\n                newStock: newStock,\n                quantity: Math.abs(quantity),\n                movementType,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(\"Stock update failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Create new inventory record\n    async createInventoryRecord (warehouseId, ingredientId, initialStock, notes) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            current_stock: initialStock,\n            minimum_stock: 0,\n            maximum_stock: initialStock * 10,\n            reorder_point: initialStock * 0.2\n        }).select().single();\n        if (error) throw error;\n        // Record initial stock movement\n        const { data: ingredient } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select('unit').eq('id', ingredientId).single();\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert({\n            warehouse_id: warehouseId,\n            ingredient_id: ingredientId,\n            movement_type: 'in',\n            quantity: initialStock,\n            unit: (ingredient === null || ingredient === void 0 ? void 0 : ingredient.unit) || 'kg',\n            reference_type: 'initial_stock',\n            notes: notes || 'Initial stock entry'\n        });\n        return data;\n    },\n    // Get stock movements for a warehouse\n    async getStockMovements (warehouseId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*),\\n        performer:profiles(full_name)\\n      \").eq('warehouse_id', warehouseId).order('created_at', {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    },\n    // Create warehouse transfer\n    async createTransfer (fromWarehouseId, toWarehouseId, items, requestedBy, notes) {\n        // Create transfer record\n        const { data: transfer, error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').insert({\n            from_warehouse_id: fromWarehouseId,\n            to_warehouse_id: toWarehouseId,\n            requested_by: requestedBy,\n            total_items: items.length,\n            notes,\n            status: 'pending'\n        }).select().single();\n        if (transferError) throw transferError;\n        // Create transfer items\n        const transferItems = items.map((item)=>({\n                transfer_id: transfer.id,\n                ingredient_id: item.ingredient_id,\n                requested_quantity: item.quantity,\n                unit: item.unit,\n                notes: item.notes\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').insert(transferItems);\n        if (itemsError) throw itemsError;\n        return transfer;\n    },\n    // Get pending transfers\n    async getPendingTransfers (warehouseId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(\"\\n        *,\\n        from_warehouse:warehouses!from_warehouse_id(*),\\n        to_warehouse:warehouses!to_warehouse_id(*),\\n        requester:profiles!requested_by(*),\\n        transfer_items(*, ingredient:ingredients(*))\\n      \").eq('status', 'pending');\n        if (warehouseId) {\n            query = query.or(\"from_warehouse_id.eq.\".concat(warehouseId, \",to_warehouse_id.eq.\").concat(warehouseId));\n        }\n        const { data, error } = await query.order('requested_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Approve transfer with enhanced validation\n    async approveTransfer (transferId, approvedBy, approvedItems) {\n        let autoComplete = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        try {\n            // Validate transfer exists and is in pending status\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(\"\\n          *,\\n          from_warehouse:warehouses!from_warehouse_id(*),\\n          to_warehouse:warehouses!to_warehouse_id(*),\\n          transfer_items(*, ingredient:ingredients(*))\\n        \").eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(\"Transfer not found: \".concat(fetchError.message));\n            }\n            if (transfer.status !== 'pending') {\n                throw new Error(\"Cannot approve transfer with status: \".concat(transfer.status));\n            }\n            // Validate stock availability for approved quantities\n            for (const approvedItem of approvedItems){\n                const transferItem = transfer.transfer_items.find((item)=>item.id === approvedItem.id);\n                if (!transferItem) {\n                    throw new Error(\"Transfer item not found: \".concat(approvedItem.id));\n                }\n                // Check current stock in source warehouse\n                const { data: inventory, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock').eq('warehouse_id', transfer.from_warehouse_id).eq('ingredient_id', transferItem.ingredient_id).single();\n                if (inventoryError) {\n                    throw new Error(\"Cannot verify stock for ingredient: \".concat(transferItem.ingredient.name));\n                }\n                if (inventory.current_stock < approvedItem.approved_quantity) {\n                    throw new Error(\"Insufficient stock for \".concat(transferItem.ingredient.name, \". Available: \").concat(inventory.current_stock, \", Requested: \").concat(approvedItem.approved_quantity));\n                }\n            }\n            const timestamp = new Date().toISOString();\n            // Update transfer status\n            const { error: transferError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'approved',\n                approved_by: approvedBy,\n                approved_at: timestamp\n            }).eq('id', transferId);\n            if (transferError) {\n                throw new Error(\"Failed to approve transfer: \".concat(transferError.message));\n            }\n            // Update approved quantities for items\n            for (const item of approvedItems){\n                const { error: itemError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transfer_items').update({\n                    approved_quantity: item.approved_quantity\n                }).eq('id', item.id);\n                if (itemError) {\n                    throw new Error(\"Failed to update approved quantity: \".concat(itemError.message));\n                }\n            }\n            // Auto-complete the transfer if requested\n            if (autoComplete) {\n                try {\n                    const completionResult = await this.completeTransfer(transferId, approvedBy);\n                    return {\n                        success: true,\n                        transferId,\n                        approvedAt: timestamp,\n                        approvedItems: approvedItems.length,\n                        autoCompleted: true,\n                        completionResult\n                    };\n                } catch (completionError) {\n                    // If auto-completion fails, log the error but don't fail the approval\n                    console.error('Auto-completion failed after approval:', completionError);\n                    return {\n                        success: true,\n                        transferId,\n                        approvedAt: timestamp,\n                        approvedItems: approvedItems.length,\n                        autoCompleted: false,\n                        autoCompletionError: completionError instanceof Error ? completionError.message : 'Unknown error'\n                    };\n                }\n            }\n            return {\n                success: true,\n                transferId,\n                approvedAt: timestamp,\n                approvedItems: approvedItems.length,\n                autoCompleted: false\n            };\n        } catch (error) {\n            throw new Error(\"Transfer approval failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Complete transfer and update inventory levels\n    async completeTransfer (transferId, completedBy) {\n        try {\n            // Fetch transfer with all details\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select(\"\\n          *,\\n          from_warehouse:warehouses!from_warehouse_id(*),\\n          to_warehouse:warehouses!to_warehouse_id(*),\\n          transfer_items(*, ingredient:ingredients(*))\\n        \").eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(\"Transfer not found: \".concat(fetchError.message));\n            }\n            if (transfer.status !== 'approved' && transfer.status !== 'in_transit') {\n                throw new Error(\"Cannot complete transfer with status: \".concat(transfer.status));\n            }\n            const timestamp = new Date().toISOString();\n            const stockMovements = [];\n            // Process each transfer item\n            for (const item of transfer.transfer_items){\n                const approvedQty = item.approved_quantity || item.requested_quantity;\n                // Reduce stock from source warehouse\n                const { data: sourceInventory, error: sourceError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', transfer.from_warehouse_id).eq('ingredient_id', item.ingredient_id).single();\n                if (sourceError) {\n                    throw new Error(\"Source inventory not found for \".concat(item.ingredient.name));\n                }\n                if (sourceInventory.current_stock < approvedQty) {\n                    throw new Error(\"Insufficient stock for \".concat(item.ingredient.name, \". Available: \").concat(sourceInventory.current_stock, \", Required: \").concat(approvedQty));\n                }\n                // Update source inventory\n                const { error: sourceUpdateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                    current_stock: sourceInventory.current_stock - approvedQty,\n                    updated_at: timestamp\n                }).eq('id', sourceInventory.id);\n                if (sourceUpdateError) {\n                    throw new Error(\"Failed to update source inventory: \".concat(sourceUpdateError.message));\n                }\n                // Add stock movement for source (outbound)\n                stockMovements.push({\n                    warehouse_id: transfer.from_warehouse_id,\n                    ingredient_id: item.ingredient_id,\n                    movement_type: 'transfer',\n                    quantity: approvedQty,\n                    unit: item.unit,\n                    reference_type: 'transfer_out',\n                    reference_id: transferId,\n                    notes: \"Transfer out to \".concat(transfer.to_warehouse.name, \" - \").concat(item.ingredient.name),\n                    performed_by: completedBy,\n                    created_at: timestamp\n                });\n                // Check if destination inventory exists\n                const { data: destInventory, error: destFetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', transfer.to_warehouse_id).eq('ingredient_id', item.ingredient_id).maybeSingle();\n                if (destFetchError) {\n                    throw new Error(\"Error checking destination inventory: \".concat(destFetchError.message));\n                }\n                if (destInventory) {\n                    // Update existing destination inventory\n                    const { error: destUpdateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: destInventory.current_stock + approvedQty,\n                        updated_at: timestamp,\n                        last_restocked_at: timestamp\n                    }).eq('id', destInventory.id);\n                    if (destUpdateError) {\n                        throw new Error(\"Failed to update destination inventory: \".concat(destUpdateError.message));\n                    }\n                } else {\n                    // Create new destination inventory record\n                    const { error: destCreateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').insert({\n                        warehouse_id: transfer.to_warehouse_id,\n                        ingredient_id: item.ingredient_id,\n                        current_stock: approvedQty,\n                        minimum_stock: 0,\n                        maximum_stock: approvedQty * 10,\n                        reorder_point: approvedQty * 0.2,\n                        last_restocked_at: timestamp\n                    });\n                    if (destCreateError) {\n                        throw new Error(\"Failed to create destination inventory: \".concat(destCreateError.message));\n                    }\n                }\n                // Add stock movement for destination (inbound)\n                stockMovements.push({\n                    warehouse_id: transfer.to_warehouse_id,\n                    ingredient_id: item.ingredient_id,\n                    movement_type: 'transfer',\n                    quantity: approvedQty,\n                    unit: item.unit,\n                    reference_type: 'transfer_in',\n                    reference_id: transferId,\n                    notes: \"Transfer in from \".concat(transfer.from_warehouse.name, \" - \").concat(item.ingredient.name),\n                    performed_by: completedBy,\n                    created_at: timestamp\n                });\n            }\n            // Insert all stock movements\n            const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n            if (movementError) {\n                throw new Error(\"Failed to record stock movements: \".concat(movementError.message));\n            }\n            // Update transfer status to completed\n            const { error: completeError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'completed',\n                completed_at: timestamp\n            }).eq('id', transferId);\n            if (completeError) {\n                throw new Error(\"Failed to complete transfer: \".concat(completeError.message));\n            }\n            return {\n                success: true,\n                transferId,\n                completedAt: timestamp,\n                itemsTransferred: transfer.transfer_items.length,\n                stockMovements: stockMovements.length\n            };\n        } catch (error) {\n            throw new Error(\"Transfer completion failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Cancel transfer\n    async cancelTransfer (transferId, cancelledBy, reason) {\n        try {\n            // Validate transfer exists and can be cancelled\n            const { data: transfer, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').select('*').eq('id', transferId).single();\n            if (fetchError) {\n                throw new Error(\"Transfer not found: \".concat(fetchError.message));\n            }\n            if (![\n                'pending',\n                'approved',\n                'in_transit'\n            ].includes(transfer.status)) {\n                throw new Error(\"Cannot cancel transfer with status: \".concat(transfer.status));\n            }\n            const timestamp = new Date().toISOString();\n            // Update transfer status\n            const { error: cancelError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouse_transfers').update({\n                status: 'cancelled',\n                notes: transfer.notes ? \"\".concat(transfer.notes, \"\\n\\nCancelled: \").concat(reason || 'No reason provided') : \"Cancelled: \".concat(reason || 'No reason provided'),\n                completed_at: timestamp\n            }).eq('id', transferId);\n            if (cancelError) {\n                throw new Error(\"Failed to cancel transfer: \".concat(cancelError.message));\n            }\n            return {\n                success: true,\n                transferId,\n                cancelledAt: timestamp,\n                reason: reason || 'No reason provided'\n            };\n        } catch (error) {\n            throw new Error(\"Transfer cancellation failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Bulk update stock levels for multiple items\n    async bulkUpdateStock (updates, performedBy) {\n        if (!updates || updates.length === 0) {\n            throw new Error('No updates provided');\n        }\n        const results = [];\n        const timestamp = new Date().toISOString();\n        const stockMovements = [];\n        try {\n            // Process each update\n            for (const update of updates){\n                try {\n                    var _inventory_ingredient, _inventory_ingredient1;\n                    // Validate input\n                    if (!update.inventoryId || update.newStock < 0) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: 'Invalid input: inventory ID is required and stock cannot be negative'\n                        });\n                        continue;\n                    }\n                    // Fetch current inventory\n                    const { data: inventory, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n              *,\\n              ingredient:ingredients(unit, name),\\n              warehouse:warehouses(name, code)\\n            \").eq('id', update.inventoryId).single();\n                    if (fetchError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Failed to fetch inventory: \".concat(fetchError.message)\n                        });\n                        continue;\n                    }\n                    const quantity = update.newStock - inventory.current_stock;\n                    // Business rule validation\n                    if (update.newStock > inventory.maximum_stock && inventory.maximum_stock > 0) {\n                        var _inventory_ingredient2;\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Stock cannot exceed maximum limit of \".concat(inventory.maximum_stock, \" \").concat((_inventory_ingredient2 = inventory.ingredient) === null || _inventory_ingredient2 === void 0 ? void 0 : _inventory_ingredient2.unit)\n                        });\n                        continue;\n                    }\n                    // Update inventory\n                    const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                        current_stock: update.newStock,\n                        updated_at: timestamp,\n                        last_restocked_at: quantity > 0 ? timestamp : inventory.last_restocked_at\n                    }).eq('id', update.inventoryId);\n                    if (updateError) {\n                        results.push({\n                            inventoryId: update.inventoryId,\n                            success: false,\n                            error: \"Failed to update inventory: \".concat(updateError.message)\n                        });\n                        continue;\n                    }\n                    // Prepare stock movement record\n                    stockMovements.push({\n                        warehouse_id: inventory.warehouse_id,\n                        ingredient_id: inventory.ingredient_id,\n                        movement_type: update.movementType,\n                        quantity: Math.abs(quantity),\n                        unit: ((_inventory_ingredient = inventory.ingredient) === null || _inventory_ingredient === void 0 ? void 0 : _inventory_ingredient.unit) || 'kg',\n                        reference_type: 'bulk_adjustment',\n                        notes: update.notes || \"Bulk stock \".concat(update.movementType, \" - \").concat((_inventory_ingredient1 = inventory.ingredient) === null || _inventory_ingredient1 === void 0 ? void 0 : _inventory_ingredient1.name),\n                        performed_by: performedBy,\n                        created_at: timestamp\n                    });\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: true,\n                        previousStock: inventory.current_stock,\n                        newStock: update.newStock\n                    });\n                } catch (error) {\n                    results.push({\n                        inventoryId: update.inventoryId,\n                        success: false,\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    });\n                }\n            }\n            // Insert all stock movements in batch\n            if (stockMovements.length > 0) {\n                const { error: movementError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n                if (movementError) {\n                    throw new Error(\"Failed to record stock movements: \".concat(movementError.message));\n                }\n            }\n            const successCount = results.filter((r)=>r.success).length;\n            const failureCount = results.filter((r)=>!r.success).length;\n            return {\n                success: failureCount === 0,\n                totalUpdates: updates.length,\n                successCount,\n                failureCount,\n                results,\n                timestamp\n            };\n        } catch (error) {\n            throw new Error(\"Bulk update failed: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n        }\n    },\n    // Get comprehensive low stock alerts with reorder suggestions\n    async getLowStockAlertsWithReorderSuggestions (branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(*),\\n        warehouse:warehouses(*)\\n      \");\n        if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query;\n        if (error) throw error;\n        // Process and categorize alerts\n        const alerts = data === null || data === void 0 ? void 0 : data.map((item)=>{\n            const stockLevel = item.current_stock;\n            const minStock = item.minimum_stock;\n            const reorderPoint = item.reorder_point;\n            const maxStock = item.maximum_stock;\n            let alertLevel = 'good';\n            let suggestedReorderQuantity = 0;\n            let daysUntilStockout = null;\n            // Determine alert level\n            if (stockLevel <= 0) {\n                alertLevel = 'critical';\n            } else if (stockLevel <= minStock) {\n                alertLevel = 'critical';\n            } else if (stockLevel <= reorderPoint) {\n                alertLevel = 'reorder';\n            } else if (stockLevel <= minStock * 1.5) {\n                alertLevel = 'low';\n            }\n            // Calculate suggested reorder quantity\n            if (alertLevel !== 'good') {\n                const targetStock = maxStock || minStock * 3;\n                suggestedReorderQuantity = Math.max(0, targetStock - stockLevel);\n            }\n            // Estimate days until stockout (simplified calculation)\n            // In a real system, you'd use historical consumption data\n            if (stockLevel > 0 && minStock > 0) {\n                const avgDailyConsumption = minStock / 30 // Rough estimate\n                ;\n                if (avgDailyConsumption > 0) {\n                    daysUntilStockout = Math.floor(stockLevel / avgDailyConsumption);\n                }\n            }\n            return {\n                ...item,\n                alertLevel,\n                suggestedReorderQuantity,\n                daysUntilStockout,\n                stockPercentage: maxStock > 0 ? stockLevel / maxStock * 100 : 0\n            };\n        }).filter((item)=>item.alertLevel !== 'good').sort((a, b)=>{\n            // Sort by alert level priority, then by days until stockout\n            const alertPriority = {\n                critical: 0,\n                low: 1,\n                reorder: 2,\n                good: 3\n            };\n            const aPriority = alertPriority[a.alertLevel];\n            const bPriority = alertPriority[b.alertLevel];\n            if (aPriority !== bPriority) {\n                return aPriority - bPriority;\n            }\n            // If same alert level, sort by days until stockout (ascending)\n            if (a.daysUntilStockout !== null && b.daysUntilStockout !== null) {\n                return a.daysUntilStockout - b.daysUntilStockout;\n            }\n            return 0;\n        });\n        return alerts || [];\n    },\n    // Get inventory performance metrics\n    async getInventoryMetrics (warehouseId, branchId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        *,\\n        ingredient:ingredients(cost_per_unit),\\n        warehouse:warehouses(branch_id)\\n      \");\n        if (warehouseId) {\n            query = query.eq('warehouse_id', warehouseId);\n        } else if (branchId) {\n            query = query.eq('warehouses.branch_id', branchId);\n        }\n        const { data, error } = await query;\n        if (error) throw error;\n        if (!data || data.length === 0) {\n            return {\n                totalItems: 0,\n                totalValue: 0,\n                lowStockCount: 0,\n                criticalStockCount: 0,\n                reorderNeededCount: 0,\n                averageStockLevel: 0,\n                turnoverRate: 0\n            };\n        }\n        const totalItems = data.length;\n        const totalValue = data.reduce((sum, item)=>{\n            var _item_ingredient;\n            const cost = ((_item_ingredient = item.ingredient) === null || _item_ingredient === void 0 ? void 0 : _item_ingredient.cost_per_unit) || 0;\n            return sum + item.current_stock * cost;\n        }, 0);\n        const lowStockCount = data.filter((item)=>item.current_stock <= item.minimum_stock && item.current_stock > 0).length;\n        const criticalStockCount = data.filter((item)=>item.current_stock <= 0).length;\n        const reorderNeededCount = data.filter((item)=>item.current_stock <= item.reorder_point).length;\n        const averageStockLevel = data.reduce((sum, item)=>{\n            const maxStock = item.maximum_stock || item.minimum_stock * 3;\n            const stockPercentage = maxStock > 0 ? item.current_stock / maxStock * 100 : 0;\n            return sum + stockPercentage;\n        }, 0) / totalItems;\n        return {\n            totalItems,\n            totalValue,\n            lowStockCount,\n            criticalStockCount,\n            reorderNeededCount,\n            averageStockLevel: Math.round(averageStockLevel),\n            turnoverRate: 0 // Would need historical data to calculate\n        };\n    },\n    // Generate automatic reorder suggestions\n    async generateReorderSuggestions (warehouseId, branchId) {\n        const alerts = await this.getLowStockAlertsWithReorderSuggestions(branchId);\n        const suggestions = alerts.filter((item)=>item.suggestedReorderQuantity > 0).map((item)=>({\n                inventoryId: item.id,\n                ingredientId: item.ingredient.id,\n                ingredientName: item.ingredient.name,\n                ingredientCode: item.ingredient.code,\n                warehouseId: item.warehouse_id,\n                warehouseName: item.warehouse.name,\n                currentStock: item.current_stock,\n                minimumStock: item.minimum_stock,\n                reorderPoint: item.reorder_point,\n                maximumStock: item.maximum_stock,\n                suggestedQuantity: item.suggestedReorderQuantity,\n                estimatedCost: (item.ingredient.cost_per_unit || 0) * item.suggestedReorderQuantity,\n                priority: item.alertLevel,\n                daysUntilStockout: item.daysUntilStockout,\n                unit: item.ingredient.unit\n            })).sort((a, b)=>{\n            const priorityOrder = {\n                critical: 0,\n                low: 1,\n                reorder: 2\n            };\n            return priorityOrder[a.priority] - priorityOrder[b.priority];\n        });\n        const totalEstimatedCost = suggestions.reduce((sum, item)=>sum + item.estimatedCost, 0);\n        const criticalCount = suggestions.filter((item)=>item.priority === 'critical').length;\n        const lowCount = suggestions.filter((item)=>item.priority === 'low').length;\n        const reorderCount = suggestions.filter((item)=>item.priority === 'reorder').length;\n        return {\n            suggestions,\n            summary: {\n                totalItems: suggestions.length,\n                totalEstimatedCost,\n                criticalCount,\n                lowCount,\n                reorderCount\n            }\n        };\n    }\n};\n// Branch Management Functions\n// export const branchService = {\n//   // Get all branches\n//   async getAllBranches() {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*)\n//       `)\n//       .eq('is_active', true)\n//       .order('name')\n//     if (error) throw error\n//     return data\n//   },\n//   // Get branch with details\n//   async getBranchDetails(branchId: string) {\n//     const { data, error } = await supabase\n//       .from('branches')\n//       .select(`\n//         *,\n//         manager:profiles(*),\n//         warehouses(*),\n//         kitchens(*)\n//       `)\n//       .eq('id', branchId)\n//       .single()\n//     if (error) throw error\n//     return data\n//   }\n// }\nconst branchService = {\n    // Get all branches\n    async getAllBranches () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(\"\\n        *,\\n        manager:profiles!fk_branches_manager(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get branch with details\n    async getBranchDetails (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branches').select(\"\\n        *,\\n        manager:profiles!fk_branches_manager(*),\\n        warehouses(*),\\n        kitchens(*)\\n      \").eq('id', branchId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Warehouse Management Functions\nconst warehouseService = {\n    // Get all warehouses\n    async getAllWarehouses () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(\"\\n        *,\\n        branch:branches(*),\\n        manager:profiles(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get warehouses by branch\n    async getWarehousesByBranch (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select(\"\\n        *,\\n        manager:profiles(*)\\n      \").eq('branch_id', branchId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Ingredient Management Functions\nconst ingredientService = {\n    // Get all ingredients\n    async getAllIngredients () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(\"\\n        *,\\n        category:ingredient_categories(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get ingredients by category\n    async getIngredientsByCategory (categoryId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ingredients').select(\"\\n        *,\\n        category:ingredient_categories(*)\\n      \").eq('category_id', categoryId).eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    }\n};\n// Production Management Functions\nconst productionService = {\n    // Get all recipes\n    async getAllRecipes () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(\"\\n        *,\\n        recipe_ingredients(\\n          *,\\n          ingredient:ingredients(*)\\n        )\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get recipe details\n    async getRecipeDetails (recipeId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('recipes').select(\"\\n        *,\\n        recipe_ingredients(\\n          *,\\n          ingredient:ingredients(*)\\n        )\\n      \").eq('id', recipeId).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create production batch with inventory validation\n    async createProductionBatch (kitchenId, recipeId, plannedQuantity, startedBy, productionPlanId) {\n        const batchNumber = \"BATCH-\".concat(Date.now());\n        // Get recipe details with ingredients\n        const recipe = await this.getRecipeDetails(recipeId);\n        if (!recipe) throw new Error('Recipe not found');\n        // Get kitchen details to find associated warehouse\n        const { data: kitchen, error: kitchenError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select('*, warehouse:warehouses(*)').eq('id', kitchenId).single();\n        if (kitchenError || !kitchen) throw new Error('Kitchen not found');\n        // Check ingredient availability\n        const ingredientChecks = await Promise.all(recipe.recipe_ingredients.map(async (recipeIngredient)=>{\n            var _inventory_ingredient;\n            const requiredQuantity = recipeIngredient.quantity * plannedQuantity;\n            const { data: inventory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock, ingredient:ingredients(name)').eq('warehouse_id', kitchen.warehouse_id).eq('ingredient_id', recipeIngredient.ingredient_id).single();\n            return {\n                ingredient_id: recipeIngredient.ingredient_id,\n                ingredient_name: (inventory === null || inventory === void 0 ? void 0 : (_inventory_ingredient = inventory.ingredient) === null || _inventory_ingredient === void 0 ? void 0 : _inventory_ingredient.name) || 'Unknown',\n                required: requiredQuantity,\n                available: (inventory === null || inventory === void 0 ? void 0 : inventory.current_stock) || 0,\n                sufficient: ((inventory === null || inventory === void 0 ? void 0 : inventory.current_stock) || 0) >= requiredQuantity\n            };\n        }));\n        // Check if all ingredients are available\n        const insufficientIngredients = ingredientChecks.filter((check)=>!check.sufficient);\n        if (insufficientIngredients.length > 0) {\n            const errorMessage = \"Insufficient ingredients: \".concat(insufficientIngredients.map((ing)=>\"\".concat(ing.ingredient_name, \" (need \").concat(ing.required, \", have \").concat(ing.available, \")\")).join(', '));\n            throw new Error(errorMessage);\n        }\n        // Calculate estimated cost\n        const estimatedCost = recipe.recipe_ingredients.reduce((total, recipeIngredient)=>{\n            const ingredientCost = (recipeIngredient.ingredient.cost_per_unit || 0) * (recipeIngredient.quantity * plannedQuantity);\n            return total + ingredientCost;\n        }, 0);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').insert({\n            kitchen_id: kitchenId,\n            recipe_id: recipeId,\n            batch_number: batchNumber,\n            planned_quantity: plannedQuantity,\n            status: 'planned',\n            started_by: startedBy,\n            planned_start_time: new Date().toISOString(),\n            production_plan_id: productionPlanId,\n            estimated_cost: estimatedCost\n        }).select().single();\n        if (error) throw error;\n        // Create planned ingredient usage records\n        const plannedUsage = recipe.recipe_ingredients.map((recipeIngredient)=>({\n                batch_id: data.id,\n                ingredient_id: recipeIngredient.ingredient_id,\n                planned_quantity: recipeIngredient.quantity * plannedQuantity,\n                unit: recipeIngredient.unit,\n                cost_per_unit: recipeIngredient.ingredient.cost_per_unit || 0\n            }));\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('batch_ingredients_used').insert(plannedUsage);\n        return data;\n    },\n    // Get production batches\n    async getProductionBatches (kitchenId, status) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(*),\\n        kitchen:kitchens(*),\\n        starter:profiles!started_by(*),\\n        completer:profiles!completed_by(*)\\n      \");\n        if (kitchenId) {\n            query = query.eq('kitchen_id', kitchenId);\n        }\n        if (status) {\n            query = query.eq('status', status);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update batch status\n    async updateBatchStatus (batchId, status, userId, actualQuantity, qualityScore, qualityNotes) {\n        const updates = {\n            status,\n            updated_at: new Date().toISOString()\n        };\n        if (status === 'in_progress') {\n            updates.actual_start_time = new Date().toISOString();\n        } else if (status === 'completed') {\n            updates.actual_end_time = new Date().toISOString();\n            updates.completed_by = userId;\n            if (actualQuantity) updates.actual_quantity = actualQuantity;\n            if (qualityScore) updates.quality_score = qualityScore;\n            if (qualityNotes) updates.quality_notes = qualityNotes;\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update(updates).eq('id', batchId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Record ingredient usage for batch\n    async recordIngredientUsage (batchId, ingredientUsage) {\n        const usageRecords = ingredientUsage.map((usage)=>({\n                batch_id: batchId,\n                ingredient_id: usage.ingredient_id,\n                planned_quantity: usage.planned_quantity,\n                actual_quantity: usage.actual_quantity,\n                unit: usage.unit,\n                cost_per_unit: usage.cost_per_unit,\n                total_cost: usage.actual_quantity * usage.cost_per_unit\n            }));\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('batch_ingredients_used').insert(usageRecords);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Get kitchen production summary\n    async getKitchenProductionSummary (kitchenId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(name)\\n      \").eq('kitchen_id', kitchenId);\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Start production batch with inventory consumption\n    async startProductionBatch (batchId, userId) {\n        // Get batch details with ingredients\n        const { data: batch, error: batchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(*),\\n        kitchen:kitchens(warehouse_id),\\n        batch_ingredients_used(*)\\n      \").eq('id', batchId).single();\n        if (batchError || !batch) throw new Error('Production batch not found');\n        if (batch.status !== 'planned') {\n            throw new Error('Batch must be in planned status to start production');\n        }\n        // Update batch status to in_progress\n        const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update({\n            status: 'in_progress',\n            actual_start_time: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).eq('id', batchId);\n        if (updateError) throw updateError;\n        // Consume ingredients from inventory\n        const stockMovements = [];\n        for (const ingredientUsed of batch.batch_ingredients_used){\n            // Get current inventory\n            const { data: inventory, error: invError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', batch.kitchen.warehouse_id).eq('ingredient_id', ingredientUsed.ingredient_id).single();\n            if (invError || !inventory) {\n                throw new Error(\"Inventory not found for ingredient \".concat(ingredientUsed.ingredient_id));\n            }\n            // Check if sufficient stock\n            if (inventory.current_stock < ingredientUsed.planned_quantity) {\n                throw new Error(\"Insufficient stock for ingredient \".concat(ingredientUsed.ingredient_id));\n            }\n            // Update inventory\n            const newStock = inventory.current_stock - ingredientUsed.planned_quantity;\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                current_stock: newStock,\n                updated_at: new Date().toISOString()\n            }).eq('id', inventory.id);\n            // Record stock movement (traditional)\n            stockMovements.push({\n                warehouse_id: batch.kitchen.warehouse_id,\n                ingredient_id: ingredientUsed.ingredient_id,\n                movement_type: 'out',\n                quantity: ingredientUsed.planned_quantity,\n                unit: ingredientUsed.unit,\n                reference_type: 'production',\n                reference_id: batchId,\n                notes: \"Production consumption for batch \".concat(batch.batch_number),\n                performed_by: userId\n            });\n            // Record enhanced stock movement for raw ingredients\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('enhanced_stock_movements').insert({\n                warehouse_id: batch.kitchen.warehouse_id,\n                item_type: 'raw_ingredient',\n                item_id: ingredientUsed.ingredient_id,\n                movement_type: 'production_input',\n                quantity: ingredientUsed.planned_quantity,\n                unit: ingredientUsed.unit,\n                reference_type: 'production',\n                reference_id: batchId,\n                notes: \"Production consumption for batch \".concat(batch.batch_number),\n                performed_by: userId\n            });\n        }\n        // Insert stock movements\n        if (stockMovements.length > 0) {\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n        }\n        return {\n            success: true,\n            message: 'Production started successfully'\n        };\n    },\n    // Complete production batch with finished goods\n    async completeProductionBatch (batchId, userId, actualQuantity, qualityScore, qualityNotes, wasteData) {\n        const { data: batch, error: batchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(*),\\n        kitchen:kitchens(warehouse_id)\\n      \").eq('id', batchId).single();\n        if (batchError || !batch) throw new Error('Production batch not found');\n        if (batch.status !== 'in_progress') {\n            throw new Error('Batch must be in progress to complete');\n        }\n        // Calculate yield percentage\n        const yieldPercentage = actualQuantity / batch.planned_quantity * 100;\n        // Update batch status\n        const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update({\n            status: 'completed',\n            actual_quantity: actualQuantity,\n            actual_end_time: new Date().toISOString(),\n            completed_by: userId,\n            quality_score: qualityScore,\n            quality_notes: qualityNotes,\n            yield_percentage: yieldPercentage,\n            updated_at: new Date().toISOString()\n        }).eq('id', batchId);\n        if (updateError) throw updateError;\n        // Record waste if provided\n        if (wasteData && wasteData.length > 0) {\n            const wasteRecords = wasteData.map((waste)=>({\n                    batch_id: batchId,\n                    ingredient_id: waste.ingredient_id,\n                    waste_quantity: waste.waste_quantity,\n                    unit: 'kg',\n                    waste_type: waste.waste_type,\n                    waste_reason: waste.waste_reason,\n                    reported_by: userId\n                }));\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_waste').insert(wasteRecords);\n        }\n        // Add finished goods to inventory if recipe has a corresponding menu item\n        const { data: menuItem } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select('id, name').eq('recipe_id', batch.recipe_id).single();\n        if (menuItem) {\n            // Calculate cost per unit\n            const { data: productionCost } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_costs').select('total_cost').eq('batch_id', batchId).single();\n            const costPerUnit = productionCost ? productionCost.total_cost / actualQuantity : 0;\n            // Check if finished goods inventory exists\n            const { data: existingFinishedGoods } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('finished_goods_inventory').select('*').eq('warehouse_id', batch.kitchen.warehouse_id).eq('menu_item_id', menuItem.id).single();\n            if (existingFinishedGoods) {\n                // Update existing finished goods inventory\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('finished_goods_inventory').update({\n                    current_stock: existingFinishedGoods.current_stock + actualQuantity,\n                    cost_per_unit: costPerUnit,\n                    last_produced_at: new Date().toISOString(),\n                    production_batch_id: batchId,\n                    updated_at: new Date().toISOString()\n                }).eq('id', existingFinishedGoods.id);\n            } else {\n                // Create new finished goods inventory record\n                await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('finished_goods_inventory').insert({\n                    warehouse_id: batch.kitchen.warehouse_id,\n                    menu_item_id: menuItem.id,\n                    current_stock: actualQuantity,\n                    minimum_stock: 10,\n                    maximum_stock: 100,\n                    reorder_point: 20,\n                    cost_per_unit: costPerUnit,\n                    last_produced_at: new Date().toISOString(),\n                    production_batch_id: batchId\n                });\n            }\n            // Record production output\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_finished_goods_output').insert({\n                production_batch_id: batchId,\n                menu_item_id: menuItem.id,\n                warehouse_id: batch.kitchen.warehouse_id,\n                quantity_produced: actualQuantity,\n                cost_per_unit: costPerUnit,\n                total_cost: (productionCost === null || productionCost === void 0 ? void 0 : productionCost.total_cost) || 0,\n                expiry_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours default\n            });\n            // Record enhanced stock movement for finished goods\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('enhanced_stock_movements').insert({\n                warehouse_id: batch.kitchen.warehouse_id,\n                item_type: 'finished_goods',\n                item_id: menuItem.id,\n                movement_type: 'production_output',\n                quantity: actualQuantity,\n                unit: 'portions',\n                reference_type: 'production',\n                reference_id: batchId,\n                cost_per_unit: costPerUnit,\n                total_cost: (productionCost === null || productionCost === void 0 ? void 0 : productionCost.total_cost) || 0,\n                notes: \"Production output for batch \".concat(batch.batch_number, \" - \").concat(menuItem.name),\n                performed_by: userId\n            });\n        }\n        return {\n            success: true,\n            message: 'Production completed successfully'\n        };\n    },\n    // Create production plan based on demand forecast\n    async createProductionPlan (kitchenId, recipeId, plannedDate, plannedQuantity, createdBy, demandForecast) {\n        // Get current stock level for the recipe/menu item\n        const { data: kitchen } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select('warehouse_id').eq('id', kitchenId).single();\n        let currentStockLevel = 0;\n        if (kitchen) {\n            const { data: inventory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock').eq('warehouse_id', kitchen.warehouse_id).eq('ingredient_id', recipeId).single();\n            currentStockLevel = (inventory === null || inventory === void 0 ? void 0 : inventory.current_stock) || 0;\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_plans').insert({\n            kitchen_id: kitchenId,\n            recipe_id: recipeId,\n            planned_date: plannedDate,\n            planned_quantity: plannedQuantity,\n            demand_forecast_quantity: demandForecast,\n            current_stock_level: currentStockLevel,\n            created_by: createdBy,\n            status: 'pending'\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Generate demand forecast based on historical sales\n    async generateDemandForecast (recipeId) {\n        let forecastDays = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 7;\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - forecastDays * 4) // Look back 4 weeks\n        ;\n        const { data: salesData, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').select(\"\\n        quantity,\\n        created_at,\\n        menu_item:menu_items!inner(recipe_id)\\n      \").eq('menu_item.recipe_id', recipeId).gte('created_at', startDate.toISOString());\n        if (error) throw error;\n        if (!salesData || salesData.length === 0) {\n            return {\n                averageDailyDemand: 0,\n                forecastedDemand: 0,\n                confidence: 'low',\n                historicalData: []\n            };\n        }\n        // Group by day and calculate daily totals\n        const dailyTotals = {};\n        salesData.forEach((item)=>{\n            const date = new Date(item.created_at).toISOString().split('T')[0];\n            dailyTotals[date] = (dailyTotals[date] || 0) + item.quantity;\n        });\n        const dailyValues = Object.values(dailyTotals);\n        const averageDailyDemand = dailyValues.reduce((sum, val)=>sum + val, 0) / dailyValues.length;\n        const forecastedDemand = Math.ceil(averageDailyDemand * forecastDays);\n        // Calculate confidence based on data consistency\n        const variance = dailyValues.reduce((sum, val)=>sum + Math.pow(val - averageDailyDemand, 2), 0) / dailyValues.length;\n        const standardDeviation = Math.sqrt(variance);\n        const coefficientOfVariation = standardDeviation / averageDailyDemand;\n        let confidence = 'high';\n        if (coefficientOfVariation > 0.5) confidence = 'low';\n        else if (coefficientOfVariation > 0.3) confidence = 'medium';\n        return {\n            averageDailyDemand: Math.round(averageDailyDemand * 100) / 100,\n            forecastedDemand,\n            confidence,\n            historicalData: dailyTotals,\n            dataPoints: dailyValues.length\n        };\n    },\n    // Get production plans\n    async getProductionPlans (kitchenId, status, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_plans').select(\"\\n        *,\\n        recipe:recipes(*),\\n        kitchen:kitchens(*),\\n        creator:profiles!created_by(*),\\n        approver:profiles!approved_by(*)\\n      \");\n        if (kitchenId) query = query.eq('kitchen_id', kitchenId);\n        if (status) query = query.eq('status', status);\n        if (startDate) query = query.gte('planned_date', startDate);\n        if (endDate) query = query.lte('planned_date', endDate);\n        const { data, error } = await query.order('planned_date', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Approve production plan\n    async approveProductionPlan (planId, approvedBy) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_plans').update({\n            status: 'approved',\n            approved_by: approvedBy,\n            updated_at: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    },\n    // Get production analytics\n    async getProductionAnalytics (kitchenId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(name, category),\\n        production_costs(*),\\n        production_waste(*)\\n      \");\n        if (kitchenId) query = query.eq('kitchen_id', kitchenId);\n        if (startDate) query = query.gte('created_at', startDate);\n        if (endDate) query = query.lte('created_at', endDate);\n        const { data: batches, error } = await query;\n        if (error) throw error;\n        // Calculate analytics\n        const totalBatches = batches.length;\n        const completedBatches = batches.filter((b)=>b.status === 'completed');\n        const averageYield = completedBatches.reduce((sum, b)=>sum + (b.yield_percentage || 0), 0) / completedBatches.length;\n        const averageQuality = completedBatches.reduce((sum, b)=>sum + (b.quality_score || 0), 0) / completedBatches.length;\n        const totalWaste = batches.reduce((sum, b)=>{\n            var _b_production_waste;\n            return sum + (((_b_production_waste = b.production_waste) === null || _b_production_waste === void 0 ? void 0 : _b_production_waste.reduce((wasteSum, w)=>wasteSum + w.waste_quantity, 0)) || 0);\n        }, 0);\n        return {\n            totalBatches,\n            completedBatches: completedBatches.length,\n            completionRate: completedBatches.length / totalBatches * 100,\n            averageYield: Math.round(averageYield * 100) / 100,\n            averageQuality: Math.round(averageQuality * 100) / 100,\n            totalWaste: Math.round(totalWaste * 100) / 100,\n            batchesByStatus: {\n                planned: batches.filter((b)=>b.status === 'planned').length,\n                in_progress: batches.filter((b)=>b.status === 'in_progress').length,\n                completed: batches.filter((b)=>b.status === 'completed').length,\n                cancelled: batches.filter((b)=>b.status === 'cancelled').length\n            }\n        };\n    },\n    // Quality Control Methods\n    async createQualityCheckpoint (batchId, checkpointName, checkpointType, status, score, notes, checkedBy) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('quality_checkpoints').insert({\n            batch_id: batchId,\n            checkpoint_name: checkpointName,\n            checkpoint_type: checkpointType,\n            status,\n            score,\n            notes,\n            checked_by: checkedBy\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getQualityCheckpoints (batchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('quality_checkpoints').select(\"\\n        *,\\n        checker:profiles!checked_by(full_name)\\n      \").eq('batch_id', batchId).order('checked_at', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Production Cost Tracking\n    async recordProductionCosts (batchId, ingredientCost) {\n        let laborCost = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, overheadCost = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 0;\n        // Get batch details to calculate cost per unit\n        const { data: batch } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select('actual_quantity, planned_quantity').eq('id', batchId).single();\n        const quantity = (batch === null || batch === void 0 ? void 0 : batch.actual_quantity) || (batch === null || batch === void 0 ? void 0 : batch.planned_quantity) || 1;\n        const totalCost = ingredientCost + laborCost + overheadCost;\n        const costPerUnit = totalCost / quantity;\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_costs').insert({\n            batch_id: batchId,\n            ingredient_cost: ingredientCost,\n            labor_cost: laborCost,\n            overhead_cost: overheadCost,\n            cost_per_unit: costPerUnit\n        }).select().single();\n        if (error) throw error;\n        // Update batch with actual cost\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update({\n            actual_cost: totalCost\n        }).eq('id', batchId);\n        return data;\n    },\n    async getProductionCosts (batchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_costs').select('*').eq('batch_id', batchId).single();\n        if (error) throw error;\n        return data;\n    },\n    // Waste Tracking\n    async recordProductionWaste (batchId, ingredientId, wasteQuantity, unit, wasteType, wasteReason, costImpact, reportedBy) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_waste').insert({\n            batch_id: batchId,\n            ingredient_id: ingredientId,\n            waste_quantity: wasteQuantity,\n            unit,\n            waste_type: wasteType,\n            waste_reason: wasteReason,\n            cost_impact: costImpact,\n            reported_by: reportedBy\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getProductionWaste (batchId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_waste').select(\"\\n        *,\\n        ingredient:ingredients(name, unit),\\n        batch:production_batches(batch_number, recipe:recipes(name)),\\n        reporter:profiles!reported_by(full_name)\\n      \");\n        if (batchId) query = query.eq('batch_id', batchId);\n        if (startDate) query = query.gte('created_at', startDate);\n        if (endDate) query = query.lte('created_at', endDate);\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Batch Production (Multiple recipes in one batch)\n    async createMultiRecipeBatch (kitchenId, recipes, startedBy, batchName) {\n        const batchNumber = batchName || \"MULTI-BATCH-\".concat(Date.now());\n        const createdBatches = [];\n        for (const recipe of recipes){\n            const batch = await this.createProductionBatch(kitchenId, recipe.recipe_id, recipe.planned_quantity, startedBy);\n            // Update batch number to group them\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').update({\n                batch_number: \"\".concat(batchNumber, \"-\").concat(recipe.recipe_id.slice(-4)),\n                special_instructions: \"Part of multi-recipe batch: \".concat(batchNumber)\n            }).eq('id', batch.id);\n            createdBatches.push(batch);\n        }\n        return {\n            success: true,\n            batchNumber,\n            batches: createdBatches,\n            message: \"Created \".concat(createdBatches.length, \" production batches\")\n        };\n    },\n    // Production Efficiency Metrics\n    async getProductionEfficiencyMetrics (kitchenId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('production_batches').select(\"\\n        *,\\n        recipe:recipes(prep_time_minutes, cook_time_minutes),\\n        production_costs(*)\\n      \").eq('status', 'completed');\n        if (kitchenId) query = query.eq('kitchen_id', kitchenId);\n        if (startDate) query = query.gte('created_at', startDate);\n        if (endDate) query = query.lte('created_at', endDate);\n        const { data: batches, error } = await query;\n        if (error) throw error;\n        if (!batches || batches.length === 0) {\n            return {\n                totalBatches: 0,\n                averageEfficiency: 0,\n                onTimeCompletion: 0,\n                costEfficiency: 0\n            };\n        }\n        // Calculate efficiency metrics\n        let totalEfficiency = 0;\n        let onTimeCount = 0;\n        let totalCostVariance = 0;\n        batches.forEach((batch)=>{\n            // Time efficiency\n            const plannedDuration = (batch.recipe.prep_time_minutes || 0) + (batch.recipe.cook_time_minutes || 0);\n            if (batch.actual_start_time && batch.actual_end_time && plannedDuration > 0) {\n                const actualDuration = (new Date(batch.actual_end_time).getTime() - new Date(batch.actual_start_time).getTime()) / (1000 * 60);\n                const efficiency = plannedDuration / actualDuration * 100;\n                totalEfficiency += Math.min(efficiency, 200) // Cap at 200% efficiency\n                ;\n                if (efficiency >= 90) onTimeCount++;\n            }\n            // Cost efficiency\n            if (batch.estimated_cost && batch.actual_cost) {\n                const costVariance = (batch.actual_cost - batch.estimated_cost) / batch.estimated_cost * 100;\n                totalCostVariance += Math.abs(costVariance);\n            }\n        });\n        return {\n            totalBatches: batches.length,\n            averageEfficiency: Math.round(totalEfficiency / batches.length * 100) / 100,\n            onTimeCompletion: Math.round(onTimeCount / batches.length * 100 * 100) / 100,\n            costEfficiency: Math.round((100 - totalCostVariance / batches.length) * 100) / 100\n        };\n    }\n};\n// Kitchen Management Functions\nconst kitchenService = {\n    // Get all kitchens\n    async getAllKitchens () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(\"\\n        *,\\n        branch:branches(*),\\n        warehouse:warehouses(*),\\n        head_chef:profiles(*)\\n      \").eq('is_active', true).order('name');\n        if (error) throw error;\n        return data;\n    },\n    // Get kitchen details\n    async getKitchenDetails (kitchenId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('kitchens').select(\"\\n        *,\\n        branch:branches(*),\\n        warehouse:warehouses(*),\\n        head_chef:profiles(*)\\n      \").eq('id', kitchenId).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Sales Management Functions\nconst salesService = {\n    // Get sales transactions\n    async getSalesTransactions (branchId, startDate, endDate) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select(\"\\n        *,\\n        branch:branches(*),\\n        server:profiles!served_by(*),\\n        sales_transaction_items(\\n          *,\\n          menu_item:menu_items(*)\\n        )\\n      \");\n        if (branchId) {\n            query = query.eq('branch_id', branchId);\n        }\n        if (startDate) {\n            query = query.gte('created_at', startDate);\n        }\n        if (endDate) {\n            query = query.lte('created_at', endDate);\n        }\n        const { data, error } = await query.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get daily sales summary\n    async getDailySalesSummary (branchId, date) {\n        const targetDate = date || new Date().toISOString().split('T')[0];\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('daily_sales_summaries').select('*').eq('branch_id', branchId).eq('date', targetDate).single();\n        if (error && error.code !== 'PGRST116') throw error;\n        return data;\n    },\n    // Create sales transaction\n    async createSalesTransaction (branchId, items, customerInfo, paymentInfo, servedBy) {\n        const transactionNumber = \"TXN-\".concat(Date.now());\n        const totalAmount = items.reduce((sum, item)=>sum + item.quantity * item.unit_price, 0);\n        // Create transaction\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').insert({\n            branch_id: branchId,\n            transaction_number: transactionNumber,\n            customer_name: customerInfo.name,\n            customer_phone: customerInfo.phone,\n            total_amount: totalAmount,\n            tax_amount: paymentInfo.tax_amount || 0,\n            discount_amount: paymentInfo.discount_amount || 0,\n            payment_method: paymentInfo.method,\n            payment_status: 'completed',\n            served_by: servedBy\n        }).select().single();\n        if (transactionError) throw transactionError;\n        // Create transaction items\n        const transactionItems = items.map((item)=>({\n                transaction_id: transaction.id,\n                menu_item_id: item.menu_item_id,\n                quantity: item.quantity,\n                unit_price: item.unit_price,\n                total_price: item.quantity * item.unit_price,\n                special_instructions: item.special_instructions\n            }));\n        const { error: itemsError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').insert(transactionItems);\n        if (itemsError) throw itemsError;\n        return transaction;\n    },\n    // Enhanced sales transaction with finished goods consumption\n    async createSalesTransactionWithFinishedGoods (branchId, items, customerInfo, paymentInfo, servedBy) {\n        const transactionNumber = \"TXN-\".concat(Date.now());\n        const totalAmount = items.reduce((sum, item)=>sum + item.quantity * item.price, 0);\n        // Create transaction\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').insert({\n            branch_id: branchId,\n            transaction_number: transactionNumber,\n            customer_name: customerInfo.name,\n            customer_phone: customerInfo.phone,\n            total_amount: totalAmount,\n            tax_amount: paymentInfo.tax_amount || 0,\n            discount_amount: paymentInfo.discount_amount || 0,\n            payment_method: paymentInfo.method,\n            payment_status: 'completed',\n            served_by: servedBy\n        }).select().single();\n        if (transactionError) throw transactionError;\n        // Create transaction items and update inventory\n        const transactionItems = [];\n        const stockMovements = [];\n        // Get branch warehouses\n        const { data: warehouses } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select('id').eq('branch_id', branchId);\n        const warehouseIds = (warehouses === null || warehouses === void 0 ? void 0 : warehouses.map((w)=>w.id)) || [];\n        for (const item of items){\n            var _menuItem_recipe;\n            // Create transaction item\n            transactionItems.push({\n                transaction_id: transaction.id,\n                menu_item_id: item.menu_item_id,\n                quantity: item.quantity,\n                unit_price: item.price,\n                total_price: item.quantity * item.price,\n                special_instructions: item.special_instructions\n            });\n            // Get menu item recipe to update ingredient inventory\n            const { data: menuItem } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select(\"\\n          *,\\n          recipe:recipes(\\n            recipe_ingredients(\\n              *,\\n              ingredient:ingredients(*)\\n            )\\n          )\\n        \").eq('id', item.menu_item_id).single();\n            if (menuItem === null || menuItem === void 0 ? void 0 : (_menuItem_recipe = menuItem.recipe) === null || _menuItem_recipe === void 0 ? void 0 : _menuItem_recipe.recipe_ingredients) {\n                // Update inventory for each ingredient used\n                for (const recipeIngredient of menuItem.recipe.recipe_ingredients){\n                    const consumedQuantity = recipeIngredient.quantity * item.quantity;\n                    // Find inventory in branch warehouses\n                    for (const warehouseId of warehouseIds){\n                        const { data: inventory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('*').eq('warehouse_id', warehouseId).eq('ingredient_id', recipeIngredient.ingredient_id).single();\n                        if (inventory && inventory.current_stock >= consumedQuantity) {\n                            // Update inventory\n                            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').update({\n                                current_stock: inventory.current_stock - consumedQuantity,\n                                updated_at: new Date().toISOString()\n                            }).eq('id', inventory.id);\n                            // Record stock movement\n                            stockMovements.push({\n                                warehouse_id: warehouseId,\n                                ingredient_id: recipeIngredient.ingredient_id,\n                                movement_type: 'out',\n                                quantity: consumedQuantity,\n                                unit: recipeIngredient.unit,\n                                reference_type: 'sale',\n                                reference_id: transaction.id,\n                                notes: \"Sale consumption - \".concat(menuItem.name),\n                                performed_by: servedBy\n                            });\n                            break; // Found inventory, no need to check other warehouses\n                        }\n                    }\n                }\n            }\n        }\n        // Insert transaction items\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').insert(transactionItems);\n        // Insert stock movements\n        if (stockMovements.length > 0) {\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('stock_movements').insert(stockMovements);\n        }\n        return transaction;\n    },\n    // Generate automatic production recommendations based on sales trends\n    async generateProductionRecommendations (branchId) {\n        let forecastDays = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 7;\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - 30) // Look back 30 days\n        ;\n        // Get sales data for the branch\n        const { data: salesData, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').select(\"\\n        quantity,\\n        created_at,\\n        menu_item:menu_items(\\n          *,\\n          recipe:recipes(*)\\n        ),\\n        sales_transactions!inner(branch_id)\\n      \").eq('sales_transactions.branch_id', branchId).gte('created_at', startDate.toISOString());\n        if (error) throw error;\n        // Group by recipe and calculate trends\n        const recipeStats = {};\n        salesData === null || salesData === void 0 ? void 0 : salesData.forEach((item)=>{\n            var _item_menu_item;\n            if ((_item_menu_item = item.menu_item) === null || _item_menu_item === void 0 ? void 0 : _item_menu_item.recipe) {\n                const recipeId = item.menu_item.recipe.id;\n                if (!recipeStats[recipeId]) {\n                    recipeStats[recipeId] = {\n                        recipe: item.menu_item.recipe,\n                        menuItem: item.menu_item,\n                        totalSold: 0,\n                        dailySales: {},\n                        trend: 'stable'\n                    };\n                }\n                recipeStats[recipeId].totalSold += item.quantity;\n                const date = new Date(item.created_at).toISOString().split('T')[0];\n                recipeStats[recipeId].dailySales[date] = (recipeStats[recipeId].dailySales[date] || 0) + item.quantity;\n            }\n        });\n        // Calculate recommendations\n        const recommendations = [];\n        for (const [recipeId, stats] of Object.entries(recipeStats)){\n            const dailyValues = Object.values(stats.dailySales);\n            const averageDailySales = dailyValues.reduce((sum, val)=>sum + val, 0) / dailyValues.length;\n            const forecastedDemand = Math.ceil(averageDailySales * forecastDays);\n            // Get current stock level\n            const { data: warehouses } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select('id').eq('branch_id', branchId);\n            let currentStock = 0;\n            if (warehouses) {\n                for (const warehouse of warehouses){\n                    const { data: inventory } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select('current_stock').eq('warehouse_id', warehouse.id).eq('ingredient_id', recipeId).single();\n                    currentStock += (inventory === null || inventory === void 0 ? void 0 : inventory.current_stock) || 0;\n                }\n            }\n            const recommendedProduction = Math.max(0, forecastedDemand - currentStock);\n            if (recommendedProduction > 0) {\n                recommendations.push({\n                    recipeId,\n                    recipeName: stats.recipe.name,\n                    menuItemName: stats.menuItem.name,\n                    currentStock,\n                    averageDailySales: Math.round(averageDailySales * 100) / 100,\n                    forecastedDemand,\n                    recommendedProduction,\n                    priority: recommendedProduction > averageDailySales * 2 ? 'high' : 'medium',\n                    reason: currentStock < forecastedDemand ? 'low_stock' : 'demand_forecast'\n                });\n            }\n        }\n        // Sort by priority and recommended quantity\n        recommendations.sort((a, b)=>{\n            if (a.priority === 'high' && b.priority !== 'high') return -1;\n            if (b.priority === 'high' && a.priority !== 'high') return 1;\n            return b.recommendedProduction - a.recommendedProduction;\n        });\n        return {\n            recommendations,\n            generatedAt: new Date().toISOString(),\n            forecastPeriod: forecastDays,\n            totalRecommendations: recommendations.length\n        };\n    }\n};\n// Menu Management Functions\nconst menuService = {\n    // Get menu items\n    async getMenuItems () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('menu_items').select(\"\\n        *,\\n        recipe:recipes(*)\\n      \").eq('is_available', true).order('category', {\n            ascending: true\n        }).order('name', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get branch menu with pricing\n    // async getBranchMenu(branchId: string) {\n    //   const { data, error } = await supabase\n    //     .from('branch_menu_pricing')\n    //     .select(`\n    //       *,\n    //       menu_item:menu_items(\n    //         *,\n    //         recipe:recipes(*)\n    //       )\n    //     `)\n    //     .eq('branch_id', branchId)\n    //     .eq('is_available', true)\n    //     .order('menu_item.category', { ascending: true })\n    //   if (error) throw error\n    //   return data\n    // },\n    async getBranchMenu (branchId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').select(\"\\n        *,\\n        menu_item:menu_items(\\n          *,\\n          recipe:recipes(*)\\n        )\\n      \").eq('branch_id', branchId).eq('is_available', true).order('menu_item(category)', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Update menu item availability\n    async updateMenuItemAvailability (branchId, menuItemId, isAvailable) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('branch_menu_pricing').update({\n            is_available: isAvailable\n        }).eq('branch_id', branchId).eq('menu_item_id', menuItemId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    }\n};\n// // Analytics and Reporting Functions\n// export const analyticsService = {\n//   // Get branch performance metrics\n//   async getBranchPerformance(branchId: string, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     // Get sales data\n//     const { data: salesData, error: salesError } = await supabase\n//       .from('sales_transactions')\n//       .select('total_amount, created_at, payment_method')\n//       .eq('branch_id', branchId)\n//       .gte('created_at', start)\n//       .lte('created_at', end)\n//     if (salesError) throw salesError\n//     // Get inventory data\n//     const { data: inventoryData, error: inventoryError } = await supabase\n//       .from('inventory')\n//       .select(`\n//         current_stock,\n//         minimum_stock,\n//         ingredient:ingredients(cost_per_unit)\n//       `)\n//       .in('warehouse_id',\n//         supabase\n//           .from('warehouses')\n//           .select('id')\n//           .eq('branch_id', branchId)\n//       )\n//     if (inventoryError) throw inventoryError\n//     // Calculate metrics\n//     const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total_amount, 0)\n//     const totalTransactions = salesData.length\n//     const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0\n//     const inventoryValue = inventoryData.reduce((sum, item) =>\n//       sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0)), 0\n//     )\n//     const lowStockItems = inventoryData.filter(item =>\n//       item.current_stock <= item.minimum_stock\n//     ).length\n//     return {\n//       totalRevenue,\n//       totalTransactions,\n//       averageTransaction,\n//       inventoryValue,\n//       lowStockItems,\n//       salesData,\n//       inventoryData\n//     }\n//   },\n//   // Get top selling items\n//   async getTopSellingItems(branchId: string, limit = 10, startDate?: string, endDate?: string) {\n//     const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()\n//     const end = endDate || new Date().toISOString()\n//     const { data, error } = await supabase\n//       .from('sales_transaction_items')\n//       .select(`\n//         quantity,\n//         total_price,\n//         menu_item:menu_items(name, category),\n//         transaction:sales_transactions!inner(created_at, branch_id)\n//       `)\n//       .eq('transaction.branch_id', branchId)\n//       .gte('transaction.created_at', start)\n//       .lte('transaction.created_at', end)\n//     if (error) throw error\n//     // Aggregate by menu item\n//     const itemStats = data.reduce((acc: any, item) => {\n//       const itemName = item.menu_item.name\n//       if (!acc[itemName]) {\n//         acc[itemName] = {\n//           name: itemName,\n//           category: item.menu_item.category,\n//           totalQuantity: 0,\n//           totalRevenue: 0\n//         }\n//       }\n//       acc[itemName].totalQuantity += item.quantity\n//       acc[itemName].totalRevenue += item.total_price\n//       return acc\n//     }, {})\n//     return Object.values(itemStats)\n//       .sort((a: any, b: any) => b.totalQuantity - a.totalQuantity)\n//       .slice(0, limit)\n//   }\n// }\n// Analytics and Reporting Functions\nconst analyticsService = {\n    // Get branch performance metrics\n    async getBranchPerformance (branchId, startDate, endDate) {\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Get sales data\n        const { data: salesData, error: salesError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transactions').select('total_amount, created_at, payment_method').eq('branch_id', branchId).gte('created_at', start).lte('created_at', end);\n        if (salesError) throw salesError;\n        // First get warehouse IDs for the branch\n        const { data: warehouseIds, error: warehouseError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('warehouses').select('id').eq('branch_id', branchId);\n        if (warehouseError) throw warehouseError;\n        // Get inventory data using warehouse IDs\n        const warehouseIdList = warehouseIds.map((w)=>w.id);\n        const { data: inventoryData, error: inventoryError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('inventory').select(\"\\n        current_stock,\\n        minimum_stock,\\n        ingredient:ingredients(cost_per_unit)\\n      \").in('warehouse_id', warehouseIdList);\n        if (inventoryError) throw inventoryError;\n        // Calculate metrics\n        const totalRevenue = salesData.reduce((sum, sale)=>sum + sale.total_amount, 0);\n        const totalTransactions = salesData.length;\n        const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;\n        const inventoryValue = inventoryData.reduce((sum, item)=>{\n            var _item_ingredient;\n            return sum + item.current_stock * (((_item_ingredient = item.ingredient) === null || _item_ingredient === void 0 ? void 0 : _item_ingredient.cost_per_unit) || 0);\n        }, 0);\n        const lowStockItems = inventoryData.filter((item)=>item.current_stock <= item.minimum_stock).length;\n        return {\n            totalRevenue,\n            totalTransactions,\n            averageTransaction,\n            inventoryValue,\n            lowStockItems,\n            salesData,\n            inventoryData\n        };\n    },\n    // Get top selling items\n    async getTopSellingItems (branchId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, startDate = arguments.length > 2 ? arguments[2] : void 0, endDate = arguments.length > 3 ? arguments[3] : void 0;\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('sales_transaction_items').select(\"\\n        quantity,\\n        total_price,\\n        menu_item:menu_items(name, category),\\n        sales_transactions!inner(created_at, branch_id)\\n      \").eq('sales_transactions.branch_id', branchId).gte('sales_transactions.created_at', start).lte('sales_transactions.created_at', end);\n        if (error) throw error;\n        // Aggregate by menu item\n        const itemStats = data.reduce((acc, item)=>{\n            const itemName = item.menu_item.name;\n            if (!acc[itemName]) {\n                acc[itemName] = {\n                    name: itemName,\n                    category: item.menu_item.category,\n                    totalQuantity: 0,\n                    totalRevenue: 0\n                };\n            }\n            acc[itemName].totalQuantity += item.quantity;\n            acc[itemName].totalRevenue += item.total_price;\n            return acc;\n        }, {});\n        return Object.values(itemStats).sort((a, b)=>b.totalQuantity - a.totalQuantity).slice(0, limit);\n    },\n    // Alternative approach for top selling items using RPC if the above doesn't work\n    async getTopSellingItemsRPC (branchId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, startDate = arguments.length > 2 ? arguments[2] : void 0, endDate = arguments.length > 3 ? arguments[3] : void 0;\n        const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();\n        const end = endDate || new Date().toISOString();\n        // Call a stored procedure/function for complex aggregation\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('get_top_selling_items', {\n            branch_id: branchId,\n            start_date: start,\n            end_date: end,\n            item_limit: limit\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/database.ts\n"));

/***/ })

});