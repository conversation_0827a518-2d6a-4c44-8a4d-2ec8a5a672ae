import { describe, it, expect, vi, beforeEach } from 'vitest'
import { productionService } from '@/lib/database'

// Mock Supabase
const mockSupabase = {
  from: vi.fn(),
  select: vi.fn(),
  insert: vi.fn(),
  update: vi.fn(),
  eq: vi.fn(),
  single: vi.fn(),
  order: vi.fn(),
  gte: vi.fn(),
  lte: vi.fn(),
}

vi.mock('@/lib/supabase', () => ({
  supabase: mockSupabase
}))

describe('Production Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('createProductionBatch', () => {
    it('should create a production batch with inventory validation', async () => {
      const mockRecipe = {
        id: 'recipe-1',
        name: 'Rendang',
        recipe_ingredients: [
          {
            ingredient_id: 'ingredient-1',
            quantity: 2,
            unit: 'kg',
            ingredient: { cost_per_unit: 10000 }
          }
        ]
      }

      const mockKitchen = {
        id: 'kitchen-1',
        warehouse_id: 'warehouse-1'
      }

      const mockInventory = {
        current_stock: 10,
        ingredient: { name: 'Beef' }
      }

      const mockBatch = {
        id: 'batch-1',
        batch_number: 'BATCH-123',
        status: 'planned'
      }

      // Mock the chain of calls
      const mockSelect = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      const mockSingle = vi.fn()

      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'recipes') {
          mockSingle.mockResolvedValueOnce({ data: mockRecipe, error: null })
          return { select: mockSelect }
        }
        if (table === 'kitchens') {
          mockSingle.mockResolvedValueOnce({ data: mockKitchen, error: null })
          return { select: mockSelect }
        }
        if (table === 'inventory') {
          mockSingle.mockResolvedValueOnce({ data: mockInventory, error: null })
          return { select: mockSelect }
        }
        if (table === 'production_batches') {
          mockSingle.mockResolvedValueOnce({ data: mockBatch, error: null })
          return { insert: vi.fn().mockReturnThis(), select: mockSelect }
        }
        if (table === 'batch_ingredients_used') {
          return { insert: vi.fn().mockResolvedValue({ error: null }) }
        }
        return { select: mockSelect }
      })

      mockSelect.mockReturnValue({ eq: mockEq })
      mockEq.mockReturnValue({ single: mockSingle })

      const result = await productionService.createProductionBatch(
        'kitchen-1',
        'recipe-1',
        5,
        'user-1'
      )

      expect(result).toEqual(mockBatch)
      expect(mockSupabase.from).toHaveBeenCalledWith('recipes')
      expect(mockSupabase.from).toHaveBeenCalledWith('kitchens')
      expect(mockSupabase.from).toHaveBeenCalledWith('production_batches')
    })

    it('should throw error when insufficient ingredients', async () => {
      const mockRecipe = {
        id: 'recipe-1',
        name: 'Rendang',
        recipe_ingredients: [
          {
            ingredient_id: 'ingredient-1',
            quantity: 2,
            unit: 'kg',
            ingredient: { cost_per_unit: 10000 }
          }
        ]
      }

      const mockKitchen = {
        id: 'kitchen-1',
        warehouse_id: 'warehouse-1'
      }

      const mockInventory = {
        current_stock: 1, // Insufficient stock
        ingredient: { name: 'Beef' }
      }

      const mockSelect = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      const mockSingle = vi.fn()

      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'recipes') {
          mockSingle.mockResolvedValueOnce({ data: mockRecipe, error: null })
          return { select: mockSelect }
        }
        if (table === 'kitchens') {
          mockSingle.mockResolvedValueOnce({ data: mockKitchen, error: null })
          return { select: mockSelect }
        }
        if (table === 'inventory') {
          mockSingle.mockResolvedValueOnce({ data: mockInventory, error: null })
          return { select: mockSelect }
        }
        return { select: mockSelect }
      })

      mockSelect.mockReturnValue({ eq: mockEq })
      mockEq.mockReturnValue({ single: mockSingle })

      await expect(
        productionService.createProductionBatch('kitchen-1', 'recipe-1', 5, 'user-1')
      ).rejects.toThrow('Insufficient ingredients')
    })
  })

  describe('startProductionBatch', () => {
    it('should start production and consume inventory', async () => {
      const mockBatch = {
        id: 'batch-1',
        status: 'planned',
        kitchen: { warehouse_id: 'warehouse-1' },
        batch_ingredients_used: [
          {
            ingredient_id: 'ingredient-1',
            planned_quantity: 2,
            unit: 'kg'
          }
        ]
      }

      const mockInventory = {
        id: 'inventory-1',
        current_stock: 10
      }

      const mockSelect = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      const mockSingle = vi.fn()
      const mockUpdate = vi.fn().mockReturnThis()
      const mockInsert = vi.fn()

      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'production_batches') {
          if (mockSelect.mock.calls.length === 0) {
            mockSingle.mockResolvedValueOnce({ data: mockBatch, error: null })
            return { select: mockSelect }
          } else {
            return { update: mockUpdate }
          }
        }
        if (table === 'inventory') {
          if (mockSelect.mock.calls.length === 1) {
            mockSingle.mockResolvedValueOnce({ data: mockInventory, error: null })
            return { select: mockSelect }
          } else {
            return { update: mockUpdate }
          }
        }
        if (table === 'stock_movements') {
          return { insert: mockInsert }
        }
        return { select: mockSelect }
      })

      mockSelect.mockReturnValue({ eq: mockEq })
      mockEq.mockReturnValue({ single: mockSingle })
      mockUpdate.mockReturnValue({ eq: vi.fn().mockResolvedValue({ error: null }) })
      mockInsert.mockResolvedValue({ error: null })

      const result = await productionService.startProductionBatch('batch-1', 'user-1')

      expect(result.success).toBe(true)
      expect(mockSupabase.from).toHaveBeenCalledWith('production_batches')
      expect(mockSupabase.from).toHaveBeenCalledWith('inventory')
      expect(mockSupabase.from).toHaveBeenCalledWith('stock_movements')
    })
  })

  describe('generateDemandForecast', () => {
    it('should generate demand forecast based on historical sales', async () => {
      const mockSalesData = [
        { quantity: 5, created_at: '2024-01-01T10:00:00Z' },
        { quantity: 3, created_at: '2024-01-02T10:00:00Z' },
        { quantity: 7, created_at: '2024-01-03T10:00:00Z' }
      ]

      const mockSelect = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      const mockGte = vi.fn().mockReturnThis()

      mockSupabase.from.mockReturnValue({ select: mockSelect })
      mockSelect.mockReturnValue({ eq: mockEq })
      mockEq.mockReturnValue({ gte: mockGte })
      mockGte.mockResolvedValue({ data: mockSalesData, error: null })

      const result = await productionService.generateDemandForecast('recipe-1', 7)

      expect(result.averageDailyDemand).toBeCloseTo(5) // (5+3+7)/3
      expect(result.forecastedDemand).toBe(35) // 5 * 7
      expect(result.confidence).toBeDefined()
      expect(result.dataPoints).toBe(3)
    })

    it('should return zero forecast when no sales data', async () => {
      const mockSelect = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      const mockGte = vi.fn().mockReturnThis()

      mockSupabase.from.mockReturnValue({ select: mockSelect })
      mockSelect.mockReturnValue({ eq: mockEq })
      mockEq.mockReturnValue({ gte: mockGte })
      mockGte.mockResolvedValue({ data: [], error: null })

      const result = await productionService.generateDemandForecast('recipe-1', 7)

      expect(result.averageDailyDemand).toBe(0)
      expect(result.forecastedDemand).toBe(0)
      expect(result.confidence).toBe('low')
    })
  })

  describe('getProductionAnalytics', () => {
    it('should calculate production analytics correctly', async () => {
      const mockBatches = [
        {
          status: 'completed',
          yield_percentage: 95,
          quality_score: 8,
          production_waste: [{ waste_quantity: 0.5 }]
        },
        {
          status: 'completed',
          yield_percentage: 90,
          quality_score: 9,
          production_waste: [{ waste_quantity: 0.3 }]
        },
        {
          status: 'in_progress',
          yield_percentage: null,
          quality_score: null,
          production_waste: []
        }
      ]

      const mockSelect = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      const mockGte = vi.fn().mockReturnThis()
      const mockLte = vi.fn().mockReturnThis()

      mockSupabase.from.mockReturnValue({ select: mockSelect })
      mockSelect.mockReturnValue({ eq: mockEq })
      mockEq.mockReturnValue({ gte: mockGte })
      mockGte.mockReturnValue({ lte: mockLte })
      mockLte.mockResolvedValue({ data: mockBatches, error: null })

      const result = await productionService.getProductionAnalytics()

      expect(result.totalBatches).toBe(3)
      expect(result.completedBatches).toBe(2)
      expect(result.completionRate).toBeCloseTo(66.67)
      expect(result.averageYield).toBeCloseTo(92.5) // (95+90)/2
      expect(result.averageQuality).toBeCloseTo(8.5) // (8+9)/2
      expect(result.totalWaste).toBeCloseTo(0.8) // 0.5+0.3
    })
  })

  describe('createQualityCheckpoint', () => {
    it('should create quality checkpoint successfully', async () => {
      const mockCheckpoint = {
        id: 'checkpoint-1',
        batch_id: 'batch-1',
        checkpoint_name: 'Spice Level Check',
        status: 'passed'
      }

      const mockInsert = vi.fn().mockReturnThis()
      const mockSelect = vi.fn().mockReturnThis()
      const mockSingle = vi.fn().mockResolvedValue({ data: mockCheckpoint, error: null })

      mockSupabase.from.mockReturnValue({ insert: mockInsert })
      mockInsert.mockReturnValue({ select: mockSelect })
      mockSelect.mockReturnValue({ single: mockSingle })

      const result = await productionService.createQualityCheckpoint(
        'batch-1',
        'Spice Level Check',
        'cooking',
        'passed',
        8,
        'Perfect spice balance',
        'user-1'
      )

      expect(result).toEqual(mockCheckpoint)
      expect(mockSupabase.from).toHaveBeenCalledWith('quality_checkpoints')
    })
  })

  describe('recordProductionWaste', () => {
    it('should record production waste correctly', async () => {
      const mockWaste = {
        id: 'waste-1',
        batch_id: 'batch-1',
        waste_quantity: 0.5,
        waste_type: 'spoilage'
      }

      const mockInsert = vi.fn().mockReturnThis()
      const mockSelect = vi.fn().mockReturnThis()
      const mockSingle = vi.fn().mockResolvedValue({ data: mockWaste, error: null })

      mockSupabase.from.mockReturnValue({ insert: mockInsert })
      mockInsert.mockReturnValue({ select: mockSelect })
      mockSelect.mockReturnValue({ single: mockSingle })

      const result = await productionService.recordProductionWaste(
        'batch-1',
        'ingredient-1',
        0.5,
        'kg',
        'spoilage',
        'Ingredient went bad',
        5000,
        'user-1'
      )

      expect(result).toEqual(mockWaste)
      expect(mockSupabase.from).toHaveBeenCalledWith('production_waste')
    })
  })
})
