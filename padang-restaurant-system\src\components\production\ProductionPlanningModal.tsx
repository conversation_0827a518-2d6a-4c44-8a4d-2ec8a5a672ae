'use client'

import { useState, useEffect } from 'react'
import { productionService, kitchenService, salesService } from '@/lib/database'
import { useAuth } from '@/contexts/AuthContext'

interface Recipe {
  id: string
  name: string
  code: string
  description: string
  category: string
  serving_size: number
  prep_time_minutes: number
  cook_time_minutes: number
  difficulty_level: number
  recipe_ingredients: {
    id: string
    quantity: number
    unit: string
    ingredient: {
      id: string
      name: string
      code: string
      unit: string
    }
  }[]
}

interface Kitchen {
  id: string
  name: string
  code: string
  type: string
  capacity: number
}

interface ProductionPlanningModalProps {
  isOpen: boolean
  onClose: () => void
  onPlanCreated: () => void
  selectedKitchen?: string
}

export default function ProductionPlanningModal({ 
  isOpen, 
  onClose, 
  onPlanCreated, 
  selectedKitchen 
}: ProductionPlanningModalProps) {
  const { profile } = useAuth()
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [kitchens, setKitchens] = useState<Kitchen[]>([])
  const [selectedRecipe, setSelectedRecipe] = useState<string>('')
  const [kitchenId, setKitchenId] = useState<string>(selectedKitchen || '')
  const [plannedQuantity, setPlannedQuantity] = useState<number>(1)
  const [plannedDate, setPlannedDate] = useState<string>(new Date().toISOString().split('T')[0])
  const [demandForecast, setDemandForecast] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (isOpen) {
      loadInitialData()
    }
  }, [isOpen])

  useEffect(() => {
    if (selectedRecipe) {
      loadDemandForecast()
    }
  }, [selectedRecipe])

  const loadInitialData = async () => {
    try {
      setLoading(true)
      const [recipesData, kitchensData] = await Promise.all([
        productionService.getAllRecipes(),
        kitchenService.getAllKitchens()
      ])
      
      setRecipes(recipesData)
      setKitchens(kitchensData)
    } catch (err) {
      setError('Failed to load data')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const loadDemandForecast = async () => {
    try {
      const forecast = await productionService.generateDemandForecast(selectedRecipe, 7)
      setDemandForecast(forecast)
      setPlannedQuantity(Math.max(1, forecast.forecastedDemand))
    } catch (err) {
      console.error('Failed to load demand forecast:', err)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedRecipe || !kitchenId || !profile?.id) {
      setError('Please fill in all required fields')
      return
    }

    try {
      setLoading(true)
      await productionService.createProductionPlan(
        kitchenId,
        selectedRecipe,
        plannedDate,
        plannedQuantity,
        profile.id,
        demandForecast?.forecastedDemand
      )
      
      onPlanCreated()
      handleClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create production plan')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setSelectedRecipe('')
    setPlannedQuantity(1)
    setPlannedDate(new Date().toISOString().split('T')[0])
    setDemandForecast(null)
    setError(null)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Create Production Plan</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <span className="sr-only">Close</span>
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Kitchen
            </label>
            <select
              value={kitchenId}
              onChange={(e) => setKitchenId(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            >
              <option value="">Select Kitchen</option>
              {kitchens.map((kitchen) => (
                <option key={kitchen.id} value={kitchen.id}>
                  {kitchen.name} ({kitchen.code})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Recipe
            </label>
            <select
              value={selectedRecipe}
              onChange={(e) => setSelectedRecipe(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            >
              <option value="">Select Recipe</option>
              {recipes.map((recipe) => (
                <option key={recipe.id} value={recipe.id}>
                  {recipe.name} ({recipe.category})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Planned Date
            </label>
            <input
              type="date"
              value={plannedDate}
              onChange={(e) => setPlannedDate(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Planned Quantity
            </label>
            <input
              type="number"
              min="1"
              value={plannedQuantity}
              onChange={(e) => setPlannedQuantity(parseInt(e.target.value))}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            />
          </div>

          {demandForecast && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <h3 className="text-sm font-medium text-blue-900 mb-2">Demand Forecast</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-700">Average Daily Sales:</span>
                  <span className="ml-2 font-medium">{demandForecast.averageDailyDemand}</span>
                </div>
                <div>
                  <span className="text-blue-700">7-Day Forecast:</span>
                  <span className="ml-2 font-medium">{demandForecast.forecastedDemand}</span>
                </div>
                <div>
                  <span className="text-blue-700">Confidence:</span>
                  <span className={`ml-2 font-medium ${
                    demandForecast.confidence === 'high' ? 'text-green-600' :
                    demandForecast.confidence === 'medium' ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {demandForecast.confidence}
                  </span>
                </div>
                <div>
                  <span className="text-blue-700">Data Points:</span>
                  <span className="ml-2 font-medium">{demandForecast.dataPoints}</span>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create Plan'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
