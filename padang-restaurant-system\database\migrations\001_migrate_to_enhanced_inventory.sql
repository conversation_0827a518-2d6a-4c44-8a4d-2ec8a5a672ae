-- Migration script to populate enhanced inventory system
-- Run this after applying the enhanced schema

-- Step 1: Migrate existing production data to finished goods inventory
INSERT INTO finished_goods_inventory (
    warehouse_id,
    menu_item_id,
    current_stock,
    minimum_stock,
    maximum_stock,
    reorder_point,
    unit,
    cost_per_unit,
    last_produced_at,
    production_batch_id,
    created_at,
    updated_at
)
SELECT DISTINCT
    k.warehouse_id,
    mi.id as menu_item_id,
    COALESCE(SUM(pb.actual_quantity), 0) as current_stock,
    10 as minimum_stock, -- Default minimum
    100 as maximum_stock, -- Default maximum
    20 as reorder_point, -- Default reorder point
    'portions' as unit,
    COALESCE(AVG(pc.cost_per_unit), 0) as cost_per_unit,
    MAX(pb.actual_end_time) as last_produced_at,
    MAX(pb.id) as production_batch_id,
    NOW() as created_at,
    NOW() as updated_at
FROM menu_items mi
JOIN recipes r ON r.id = mi.recipe_id
JOIN production_batches pb ON pb.recipe_id = r.id AND pb.status = 'completed'
JOIN kitchens k ON k.id = pb.kitchen_id
LEFT JOIN production_costs pc ON pc.batch_id = pb.id
WHERE mi.is_available = true
GROUP BY k.warehouse_id, mi.id
ON CONFLICT (warehouse_id, menu_item_id, batch_number) DO NOTHING;

-- Step 2: Create production finished goods output records for existing batches
INSERT INTO production_finished_goods_output (
    production_batch_id,
    menu_item_id,
    warehouse_id,
    quantity_produced,
    unit,
    cost_per_unit,
    total_cost,
    expiry_date,
    created_at
)
SELECT 
    pb.id as production_batch_id,
    mi.id as menu_item_id,
    k.warehouse_id,
    pb.actual_quantity as quantity_produced,
    'portions' as unit,
    COALESCE(pc.cost_per_unit, 0) as cost_per_unit,
    COALESCE(pc.total_cost, 0) as total_cost,
    pb.actual_end_time + INTERVAL '24 hours' as expiry_date, -- Default 24 hour expiry
    pb.actual_end_time as created_at
FROM production_batches pb
JOIN recipes r ON r.id = pb.recipe_id
JOIN menu_items mi ON mi.recipe_id = r.id
JOIN kitchens k ON k.id = pb.kitchen_id
LEFT JOIN production_costs pc ON pc.batch_id = pb.id
WHERE pb.status = 'completed' 
  AND pb.actual_quantity > 0
  AND mi.is_available = true
ON CONFLICT DO NOTHING;

-- Step 3: Migrate existing stock movements to enhanced stock movements
INSERT INTO enhanced_stock_movements (
    warehouse_id,
    item_type,
    item_id,
    movement_type,
    quantity,
    unit,
    reference_type,
    reference_id,
    batch_number,
    cost_per_unit,
    total_cost,
    notes,
    performed_by,
    created_at
)
SELECT 
    sm.warehouse_id,
    'raw_ingredient' as item_type,
    sm.ingredient_id as item_id,
    CASE 
        WHEN sm.movement_type = 'in' AND sm.reference_type = 'production' THEN 'production_output'
        WHEN sm.movement_type = 'out' AND sm.reference_type = 'production' THEN 'production_input'
        ELSE sm.movement_type
    END as movement_type,
    sm.quantity,
    sm.unit,
    sm.reference_type,
    sm.reference_id,
    NULL as batch_number,
    0 as cost_per_unit, -- Will be calculated later
    0 as total_cost, -- Will be calculated later
    sm.notes,
    sm.performed_by,
    sm.created_at
FROM stock_movements sm
WHERE sm.created_at >= NOW() - INTERVAL '30 days' -- Only migrate recent movements
ON CONFLICT DO NOTHING;

-- Step 4: Initialize menu item availability cache
INSERT INTO menu_item_availability_cache (
    branch_id,
    menu_item_id,
    is_available,
    availability_reason,
    current_finished_stock,
    can_produce,
    missing_ingredients,
    estimated_production_time,
    last_updated
)
SELECT 
    b.id as branch_id,
    bmp.menu_item_id,
    CASE 
        WHEN COALESCE(fgi.current_stock, 0) > 0 THEN true
        ELSE false
    END as is_available,
    CASE 
        WHEN COALESCE(fgi.current_stock, 0) > COALESCE(fgi.minimum_stock, 0) THEN 'in_stock'
        WHEN COALESCE(fgi.current_stock, 0) > 0 THEN 'low_stock'
        ELSE 'out_of_stock'
    END as availability_reason,
    COALESCE(fgi.current_stock, 0) as current_finished_stock,
    true as can_produce, -- Default to true, will be updated by triggers
    ARRAY[]::TEXT[] as missing_ingredients,
    COALESCE(r.prep_time_minutes, 0) + COALESCE(r.cook_time_minutes, 0) as estimated_production_time,
    NOW() as last_updated
FROM branches b
CROSS JOIN branch_menu_pricing bmp
JOIN menu_items mi ON mi.id = bmp.menu_item_id
LEFT JOIN recipes r ON r.id = mi.recipe_id
LEFT JOIN warehouses w ON w.branch_id = b.id
LEFT JOIN finished_goods_inventory fgi ON fgi.warehouse_id = w.id AND fgi.menu_item_id = mi.id
WHERE bmp.branch_id = b.id
  AND mi.is_available = true
ON CONFLICT (branch_id, menu_item_id) DO UPDATE SET
    is_available = EXCLUDED.is_available,
    availability_reason = EXCLUDED.availability_reason,
    current_finished_stock = EXCLUDED.current_finished_stock,
    last_updated = EXCLUDED.last_updated;

-- Step 5: Update branch menu pricing with current sold counts (reset daily)
UPDATE branch_menu_pricing 
SET current_sold = 0, 
    updated_at = NOW()
WHERE DATE(updated_at) < CURRENT_DATE;

-- Step 6: Create indexes for performance (if not already created)
CREATE INDEX IF NOT EXISTS idx_finished_goods_low_stock 
ON finished_goods_inventory(warehouse_id) 
WHERE current_stock <= minimum_stock;

CREATE INDEX IF NOT EXISTS idx_menu_availability_cache_updated 
ON menu_item_availability_cache(last_updated);

CREATE INDEX IF NOT EXISTS idx_enhanced_stock_movements_date 
ON enhanced_stock_movements(created_at);

-- Step 7: Update statistics
ANALYZE finished_goods_inventory;
ANALYZE enhanced_stock_movements;
ANALYZE menu_item_availability_cache;
ANALYZE production_finished_goods_output;

-- Migration completion log
INSERT INTO audit_logs (
    table_name,
    operation,
    record_id,
    old_values,
    new_values,
    performed_by,
    performed_at,
    notes
) VALUES (
    'migration',
    'SYSTEM_MIGRATION',
    'enhanced_inventory_001',
    '{}',
    '{"migration": "enhanced_inventory_system", "version": "001"}',
    'system',
    NOW(),
    'Migrated to enhanced inventory system with finished goods tracking'
);

-- Verification queries (run these to verify migration success)
/*
-- Check finished goods inventory
SELECT 
    w.name as warehouse_name,
    mi.name as menu_item_name,
    fgi.current_stock,
    fgi.minimum_stock,
    fgi.last_produced_at
FROM finished_goods_inventory fgi
JOIN warehouses w ON w.id = fgi.warehouse_id
JOIN menu_items mi ON mi.id = fgi.menu_item_id
ORDER BY w.name, mi.name;

-- Check availability cache
SELECT 
    b.name as branch_name,
    mi.name as menu_item_name,
    mac.is_available,
    mac.availability_reason,
    mac.current_finished_stock
FROM menu_item_availability_cache mac
JOIN branches b ON b.id = mac.branch_id
JOIN menu_items mi ON mi.id = mac.menu_item_id
ORDER BY b.name, mi.name;

-- Check enhanced stock movements
SELECT 
    w.name as warehouse_name,
    esm.item_type,
    esm.movement_type,
    esm.quantity,
    esm.reference_type,
    esm.created_at
FROM enhanced_stock_movements esm
JOIN warehouses w ON w.id = esm.warehouse_id
ORDER BY esm.created_at DESC
LIMIT 20;
*/
