'use client'

import { useState, useEffect } from 'react'
import { productionService, salesService } from '@/lib/database'

interface ProductionAnalyticsProps {
  kitchenId?: string
  branchId?: string
}

interface AnalyticsData {
  totalBatches: number
  completedBatches: number
  completionRate: number
  averageYield: number
  averageQuality: number
  totalWaste: number
  batchesByStatus: {
    planned: number
    in_progress: number
    completed: number
    cancelled: number
  }
}

interface EfficiencyMetrics {
  totalBatches: number
  averageEfficiency: number
  onTimeCompletion: number
  costEfficiency: number
}

interface ProductionRecommendation {
  recipeId: string
  recipeName: string
  menuItemName: string
  currentStock: number
  averageDailySales: number
  forecastedDemand: number
  recommendedProduction: number
  priority: string
  reason: string
}

export default function ProductionAnalytics({ kitchenId, branchId }: ProductionAnalyticsProps) {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [efficiency, setEfficiency] = useState<EfficiencyMetrics | null>(null)
  const [recommendations, setRecommendations] = useState<ProductionRecommendation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState<string>('7') // days

  useEffect(() => {
    loadAnalytics()
  }, [kitchenId, branchId, timeRange])

  const loadAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)

      const endDate = new Date().toISOString()
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - parseInt(timeRange))

      const [analyticsData, efficiencyData, recommendationsData] = await Promise.all([
        productionService.getProductionAnalytics(kitchenId, startDate.toISOString(), endDate),
        productionService.getProductionEfficiencyMetrics(kitchenId, startDate.toISOString(), endDate),
        branchId ? salesService.generateProductionRecommendations(branchId, 7) : Promise.resolve({ recommendations: [] })
      ])

      setAnalytics(analyticsData)
      setEfficiency(efficiencyData)
      setRecommendations(recommendationsData.recommendations || [])
    } catch (err) {
      console.error('Failed to load analytics:', err)
      setError('Failed to load production analytics')
    } finally {
      setLoading(false)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-green-100 text-green-800'
    }
  }

  const getEfficiencyColor = (value: number, threshold: number = 80) => {
    if (value >= threshold) return 'text-green-600'
    if (value >= threshold * 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-red-600 text-center">{error}</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-900">Production Analytics</h2>
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        >
          <option value="7">Last 7 days</option>
          <option value="30">Last 30 days</option>
          <option value="90">Last 90 days</option>
        </select>
      </div>

      {/* Production Overview */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl">📊</div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Batches</dt>
                  <dd className="text-lg font-medium text-gray-900">{analytics.totalBatches}</dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl">✅</div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Completion Rate</dt>
                  <dd className={`text-lg font-medium ${getEfficiencyColor(analytics.completionRate)}`}>
                    {analytics.completionRate.toFixed(1)}%
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl">⭐</div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Avg Quality</dt>
                  <dd className={`text-lg font-medium ${getEfficiencyColor(analytics.averageQuality * 10)}`}>
                    {analytics.averageQuality.toFixed(1)}/10
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl">🗑️</div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Waste</dt>
                  <dd className="text-lg font-medium text-gray-900">{analytics.totalWaste} kg</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Efficiency Metrics */}
      {efficiency && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Efficiency Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className={`text-2xl font-bold ${getEfficiencyColor(efficiency.averageEfficiency)}`}>
                {efficiency.averageEfficiency.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">Time Efficiency</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getEfficiencyColor(efficiency.onTimeCompletion)}`}>
                {efficiency.onTimeCompletion.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">On-Time Completion</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getEfficiencyColor(efficiency.costEfficiency)}`}>
                {efficiency.costEfficiency.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">Cost Efficiency</div>
            </div>
          </div>
        </div>
      )}

      {/* Production Recommendations */}
      {recommendations.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Production Recommendations</h3>
          <div className="space-y-3">
            {recommendations.slice(0, 5).map((rec, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{rec.recipeName}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(rec.priority)}`}>
                    {rec.priority} priority
                  </span>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Current Stock:</span>
                    <span className="ml-2 font-medium">{rec.currentStock}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Daily Sales:</span>
                    <span className="ml-2 font-medium">{rec.averageDailySales}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Forecast:</span>
                    <span className="ml-2 font-medium">{rec.forecastedDemand}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Recommended:</span>
                    <span className="ml-2 font-medium text-indigo-600">{rec.recommendedProduction}</span>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  Reason: {rec.reason.replace('_', ' ')}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Batch Status Distribution */}
      {analytics && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Batch Status Distribution</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-xl font-bold text-blue-600">{analytics.batchesByStatus.planned}</div>
              <div className="text-sm text-gray-500">Planned</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-yellow-600">{analytics.batchesByStatus.in_progress}</div>
              <div className="text-sm text-gray-500">In Progress</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-green-600">{analytics.batchesByStatus.completed}</div>
              <div className="text-sm text-gray-500">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-red-600">{analytics.batchesByStatus.cancelled}</div>
              <div className="text-sm text-gray-500">Cancelled</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
