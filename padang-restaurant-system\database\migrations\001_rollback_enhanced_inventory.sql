-- Rollback script for enhanced inventory system migration
-- WARNING: This will remove all enhanced inventory data
-- Only run this if you need to completely revert the enhanced inventory system

-- Step 1: Drop triggers first
DROP TRIGGER IF EXISTS trigger_update_menu_availability ON finished_goods_inventory;
DROP TRIGGER IF EXISTS trigger_update_quota_availability ON branch_menu_pricing;

-- Step 2: Drop functions
DROP FUNCTION IF EXISTS update_menu_availability_on_stock_change();
DROP FUNCTION IF EXISTS update_daily_quota_availability();

-- Step 3: Drop view
DROP VIEW IF EXISTS menu_item_availability_view;

-- Step 4: Clear enhanced data (but keep tables for safety)
-- Comment out these lines if you want to preserve data
/*
TRUNCATE TABLE production_finished_goods_output CASCADE;
TRUNCATE TABLE menu_item_availability_cache CASCADE;
TRUNCATE TABLE enhanced_stock_movements CASCADE;
TRUNCATE TABLE finished_goods_inventory CASCADE;
*/

-- Step 5: Drop enhanced indexes
DROP INDEX IF EXISTS idx_finished_goods_warehouse_menu;
DROP INDEX IF EXISTS idx_finished_goods_stock_level;
DROP INDEX IF EXISTS idx_enhanced_stock_movements_warehouse;
DROP INDEX IF EXISTS idx_enhanced_stock_movements_item;
DROP INDEX IF EXISTS idx_enhanced_stock_movements_reference;
DROP INDEX IF EXISTS idx_menu_availability_cache_branch;
DROP INDEX IF EXISTS idx_menu_availability_cache_available;
DROP INDEX IF EXISTS idx_production_finished_goods_batch;
DROP INDEX IF EXISTS idx_production_finished_goods_menu;
DROP INDEX IF EXISTS idx_finished_goods_low_stock;
DROP INDEX IF EXISTS idx_menu_availability_cache_updated;
DROP INDEX IF EXISTS idx_enhanced_stock_movements_date;

-- Step 6: Drop enhanced tables (DANGEROUS - only if you want complete removal)
-- Uncomment these lines only if you want to completely remove the enhanced system
/*
DROP TABLE IF EXISTS production_finished_goods_output CASCADE;
DROP TABLE IF EXISTS menu_item_availability_cache CASCADE;
DROP TABLE IF EXISTS enhanced_stock_movements CASCADE;
DROP TABLE IF EXISTS finished_goods_inventory CASCADE;
*/

-- Step 7: Reset branch menu pricing to original state
UPDATE branch_menu_pricing 
SET current_sold = 0,
    updated_at = NOW()
WHERE current_sold > 0;

-- Step 8: Log the rollback
INSERT INTO audit_logs (
    table_name,
    operation,
    record_id,
    old_values,
    new_values,
    performed_by,
    performed_at,
    notes
) VALUES (
    'migration',
    'SYSTEM_ROLLBACK',
    'enhanced_inventory_001_rollback',
    '{"migration": "enhanced_inventory_system", "version": "001"}',
    '{}',
    'system',
    NOW(),
    'Rolled back enhanced inventory system migration'
);

-- Verification queries after rollback
/*
-- Verify tables are dropped/cleared
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN (
    'finished_goods_inventory',
    'enhanced_stock_movements', 
    'menu_item_availability_cache',
    'production_finished_goods_output'
  );

-- Verify functions are dropped
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name IN (
    'update_menu_availability_on_stock_change',
    'update_daily_quota_availability'
  );

-- Verify triggers are dropped
SELECT trigger_name, event_object_table
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
  AND trigger_name IN (
    'trigger_update_menu_availability',
    'trigger_update_quota_availability'
  );
*/
