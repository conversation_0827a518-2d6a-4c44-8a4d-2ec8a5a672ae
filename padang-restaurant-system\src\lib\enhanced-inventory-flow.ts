import { supabase } from './supabase'

// Enhanced inventory flow with proper finished goods tracking

export interface FinishedGoodsInventory {
  id: string
  warehouse_id: string
  menu_item_id: string
  current_stock: number
  minimum_stock: number
  maximum_stock: number
  reorder_point: number
  unit: string // portions, servings, etc.
  cost_per_unit: number
  last_produced_at: string
  expiry_date: string
  created_at: string
  updated_at: string
}

export interface MenuItemAvailability {
  menu_item_id: string
  is_available: boolean
  availability_reason: 'in_stock' | 'out_of_stock' | 'low_stock' | 'quota_reached' | 'manually_disabled'
  current_stock: number
  can_produce: boolean
  missing_ingredients: string[]
  estimated_production_time: number
}

export const enhancedInventoryService = {
  
  // Check real-time menu item availability based on finished goods inventory
  async checkMenuItemAvailability(branchId: string, menuItemId: string): Promise<MenuItemAvailability> {
    // Get branch warehouses
    const { data: warehouses } = await supabase
      .from('warehouses')
      .select('id')
      .eq('branch_id', branchId)

    if (!warehouses || warehouses.length === 0) {
      return {
        menu_item_id: menuItemId,
        is_available: false,
        availability_reason: 'out_of_stock',
        current_stock: 0,
        can_produce: false,
        missing_ingredients: [],
        estimated_production_time: 0
      }
    }

    // Check finished goods inventory
    let totalFinishedStock = 0
    for (const warehouse of warehouses) {
      const { data: finishedGoods } = await supabase
        .from('finished_goods_inventory')
        .select('current_stock')
        .eq('warehouse_id', warehouse.id)
        .eq('menu_item_id', menuItemId)
        .single()

      totalFinishedStock += finishedGoods?.current_stock || 0
    }

    // Check daily quota
    const { data: branchMenu } = await supabase
      .from('branch_menu_pricing')
      .select('daily_quota, current_sold, is_available')
      .eq('branch_id', branchId)
      .eq('menu_item_id', menuItemId)
      .single()

    // Check if manually disabled
    if (!branchMenu?.is_available) {
      return {
        menu_item_id: menuItemId,
        is_available: false,
        availability_reason: 'manually_disabled',
        current_stock: totalFinishedStock,
        can_produce: false,
        missing_ingredients: [],
        estimated_production_time: 0
      }
    }

    // Check daily quota
    if (branchMenu?.daily_quota && branchMenu.current_sold >= branchMenu.daily_quota) {
      return {
        menu_item_id: menuItemId,
        is_available: false,
        availability_reason: 'quota_reached',
        current_stock: totalFinishedStock,
        can_produce: false,
        missing_ingredients: [],
        estimated_production_time: 0
      }
    }

    // If we have finished goods in stock
    if (totalFinishedStock > 0) {
      return {
        menu_item_id: menuItemId,
        is_available: true,
        availability_reason: totalFinishedStock <= 5 ? 'low_stock' : 'in_stock',
        current_stock: totalFinishedStock,
        can_produce: true,
        missing_ingredients: [],
        estimated_production_time: 0
      }
    }

    // Check if we can produce (raw ingredients available)
    const productionCheck = await this.checkProductionCapability(menuItemId, warehouses[0].id)
    
    return {
      menu_item_id: menuItemId,
      is_available: productionCheck.can_produce,
      availability_reason: productionCheck.can_produce ? 'out_of_stock' : 'out_of_stock',
      current_stock: 0,
      can_produce: productionCheck.can_produce,
      missing_ingredients: productionCheck.missing_ingredients,
      estimated_production_time: productionCheck.estimated_time
    }
  },

  // Check if we can produce a menu item based on raw ingredient availability
  async checkProductionCapability(menuItemId: string, warehouseId: string) {
    const { data: menuItem } = await supabase
      .from('menu_items')
      .select(`
        *,
        recipe:recipes(
          prep_time_minutes,
          cook_time_minutes,
          recipe_ingredients(
            quantity,
            unit,
            ingredient:ingredients(name, unit)
          )
        )
      `)
      .eq('id', menuItemId)
      .single()

    if (!menuItem?.recipe) {
      return { can_produce: false, missing_ingredients: ['Recipe not found'], estimated_time: 0 }
    }

    const missingIngredients: string[] = []
    
    for (const recipeIngredient of menuItem.recipe.recipe_ingredients) {
      const { data: inventory } = await supabase
        .from('inventory')
        .select('current_stock')
        .eq('warehouse_id', warehouseId)
        .eq('ingredient_id', recipeIngredient.ingredient.id)
        .single()

      if (!inventory || inventory.current_stock < recipeIngredient.quantity) {
        missingIngredients.push(recipeIngredient.ingredient.name)
      }
    }

    const estimatedTime = (menuItem.recipe.prep_time_minutes || 0) + (menuItem.recipe.cook_time_minutes || 0)

    return {
      can_produce: missingIngredients.length === 0,
      missing_ingredients: missingIngredients,
      estimated_time: estimatedTime
    }
  },

  // Enhanced sales transaction that properly consumes finished goods
  async createSalesTransactionWithFinishedGoods(
    branchId: string, 
    items: any[], 
    customerInfo: any, 
    paymentInfo: any, 
    servedBy: string
  ) {
    const transactionNumber = `TXN-${Date.now()}`
    const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.price), 0)

    // Create transaction
    const { data: transaction, error: transactionError } = await supabase
      .from('sales_transactions')
      .insert({
        branch_id: branchId,
        transaction_number: transactionNumber,
        customer_name: customerInfo.name,
        customer_phone: customerInfo.phone,
        total_amount: totalAmount,
        tax_amount: paymentInfo.tax_amount || 0,
        discount_amount: paymentInfo.discount_amount || 0,
        payment_method: paymentInfo.method,
        payment_status: 'completed',
        served_by: servedBy
      })
      .select()
      .single()

    if (transactionError) throw transactionError

    // Get branch warehouses
    const { data: warehouses } = await supabase
      .from('warehouses')
      .select('id')
      .eq('branch_id', branchId)

    const warehouseIds = warehouses?.map(w => w.id) || []
    const transactionItems = []
    const stockMovements = []

    for (const item of items) {
      // Create transaction item
      transactionItems.push({
        transaction_id: transaction.id,
        menu_item_id: item.menu_item_id,
        quantity: item.quantity,
        unit_price: item.price,
        total_price: item.quantity * item.price,
        special_instructions: item.special_instructions
      })

      // Try to consume from finished goods first
      let remainingQuantity = item.quantity
      
      for (const warehouseId of warehouseIds) {
        if (remainingQuantity <= 0) break

        const { data: finishedGoods } = await supabase
          .from('finished_goods_inventory')
          .select('*')
          .eq('warehouse_id', warehouseId)
          .eq('menu_item_id', item.menu_item_id)
          .single()

        if (finishedGoods && finishedGoods.current_stock > 0) {
          const consumeQuantity = Math.min(remainingQuantity, finishedGoods.current_stock)
          
          // Update finished goods inventory
          await supabase
            .from('finished_goods_inventory')
            .update({
              current_stock: finishedGoods.current_stock - consumeQuantity,
              updated_at: new Date().toISOString()
            })
            .eq('id', finishedGoods.id)

          // Record stock movement
          stockMovements.push({
            warehouse_id: warehouseId,
            item_type: 'finished_goods',
            item_id: item.menu_item_id,
            movement_type: 'out',
            quantity: consumeQuantity,
            unit: 'portions',
            reference_type: 'sale',
            reference_id: transaction.id,
            notes: `Sale consumption - finished goods`,
            performed_by: servedBy
          })

          remainingQuantity -= consumeQuantity
        }
      }

      // If we still have remaining quantity, we need to produce on-demand
      // This would trigger immediate production or mark as backorder
      if (remainingQuantity > 0) {
        // Log this as a production trigger
        console.log(`Need to produce ${remainingQuantity} units of ${item.menu_item_id}`)
        // Could trigger automatic production batch creation here
      }
    }

    // Insert transaction items and stock movements
    await Promise.all([
      supabase.from('sales_transaction_items').insert(transactionItems),
      stockMovements.length > 0 ? supabase.from('stock_movements').insert(stockMovements) : Promise.resolve()
    ])

    return transaction
  },

  // Update menu availability across all branches based on inventory levels
  async updateMenuAvailabilityRealTime() {
    const { data: branches } = await supabase.from('branches').select('id')
    
    for (const branch of branches || []) {
      const { data: menuItems } = await supabase
        .from('branch_menu_pricing')
        .select('menu_item_id')
        .eq('branch_id', branch.id)

      for (const menuItem of menuItems || []) {
        const availability = await this.checkMenuItemAvailability(branch.id, menuItem.menu_item_id)
        
        await supabase
          .from('branch_menu_pricing')
          .update({ 
            is_available: availability.is_available,
            updated_at: new Date().toISOString()
          })
          .eq('branch_id', branch.id)
          .eq('menu_item_id', menuItem.menu_item_id)
      }
    }
  },

  // Enhanced production completion that properly creates finished goods
  async completeProductionBatchWithFinishedGoods(
    batchId: string,
    userId: string,
    actualQuantity: number,
    qualityScore: number,
    qualityNotes?: string,
    wasteData?: Array<{ingredient_id: string, waste_quantity: number, waste_type: string, waste_reason?: string}>
  ) {
    const { data: batch, error: batchError } = await supabase
      .from('production_batches')
      .select(`
        *,
        recipe:recipes(*),
        kitchen:kitchens(warehouse_id)
      `)
      .eq('id', batchId)
      .single()

    if (batchError || !batch) throw new Error('Production batch not found')

    // Calculate yield percentage
    const yieldPercentage = (actualQuantity / batch.planned_quantity) * 100

    // Update batch status
    await supabase
      .from('production_batches')
      .update({
        status: 'completed',
        actual_quantity: actualQuantity,
        actual_end_time: new Date().toISOString(),
        completed_by: userId,
        quality_score: qualityScore,
        quality_notes: qualityNotes,
        yield_percentage: yieldPercentage,
        updated_at: new Date().toISOString()
      })
      .eq('id', batchId)

    // Get corresponding menu item
    const { data: menuItem } = await supabase
      .from('menu_items')
      .select('id, name')
      .eq('recipe_id', batch.recipe_id)
      .single()

    if (menuItem) {
      // Calculate cost per unit
      const { data: productionCost } = await supabase
        .from('production_costs')
        .select('total_cost')
        .eq('batch_id', batchId)
        .single()

      const costPerUnit = productionCost ? productionCost.total_cost / actualQuantity : 0

      // Check if finished goods inventory exists
      const { data: existingFinishedGoods } = await supabase
        .from('finished_goods_inventory')
        .select('*')
        .eq('warehouse_id', batch.kitchen.warehouse_id)
        .eq('menu_item_id', menuItem.id)
        .single()

      if (existingFinishedGoods) {
        // Update existing finished goods inventory
        await supabase
          .from('finished_goods_inventory')
          .update({
            current_stock: existingFinishedGoods.current_stock + actualQuantity,
            cost_per_unit: costPerUnit,
            last_produced_at: new Date().toISOString(),
            production_batch_id: batchId,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingFinishedGoods.id)
      } else {
        // Create new finished goods inventory record
        await supabase
          .from('finished_goods_inventory')
          .insert({
            warehouse_id: batch.kitchen.warehouse_id,
            menu_item_id: menuItem.id,
            current_stock: actualQuantity,
            minimum_stock: 10, // Default minimum
            maximum_stock: 100, // Default maximum
            reorder_point: 20, // Default reorder point
            cost_per_unit: costPerUnit,
            last_produced_at: new Date().toISOString(),
            production_batch_id: batchId
          })
      }

      // Record production output
      await supabase
        .from('production_finished_goods_output')
        .insert({
          production_batch_id: batchId,
          menu_item_id: menuItem.id,
          warehouse_id: batch.kitchen.warehouse_id,
          quantity_produced: actualQuantity,
          cost_per_unit: costPerUnit,
          total_cost: productionCost?.total_cost || 0,
          expiry_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours default
        })

      // Record enhanced stock movement
      await supabase
        .from('enhanced_stock_movements')
        .insert({
          warehouse_id: batch.kitchen.warehouse_id,
          item_type: 'finished_goods',
          item_id: menuItem.id,
          movement_type: 'production_output',
          quantity: actualQuantity,
          unit: 'portions',
          reference_type: 'production',
          reference_id: batchId,
          cost_per_unit: costPerUnit,
          total_cost: productionCost?.total_cost || 0,
          notes: `Production output for batch ${batch.batch_number} - ${menuItem.name}`,
          performed_by: userId
        })
    }

    // Record waste if provided
    if (wasteData && wasteData.length > 0) {
      const wasteRecords = wasteData.map(waste => ({
        batch_id: batchId,
        ingredient_id: waste.ingredient_id,
        waste_quantity: waste.waste_quantity,
        unit: 'kg',
        waste_type: waste.waste_type,
        waste_reason: waste.waste_reason,
        reported_by: userId
      }))

      await supabase
        .from('production_waste')
        .insert(wasteRecords)
    }

    return { success: true, message: 'Production completed and finished goods updated' }
  },

  // Get comprehensive inventory dashboard data
  async getInventoryDashboard(branchId: string) {
    // Get branch warehouses
    const { data: warehouses } = await supabase
      .from('warehouses')
      .select('id, name')
      .eq('branch_id', branchId)

    if (!warehouses || warehouses.length === 0) {
      return { error: 'No warehouses found for branch' }
    }

    const warehouseIds = warehouses.map(w => w.id)

    // Get raw ingredients inventory
    const { data: rawInventory } = await supabase
      .from('inventory')
      .select(`
        *,
        ingredient:ingredients(name, unit, cost_per_unit),
        warehouse:warehouses(name)
      `)
      .in('warehouse_id', warehouseIds)

    // Get finished goods inventory
    const { data: finishedInventory } = await supabase
      .from('finished_goods_inventory')
      .select(`
        *,
        menu_item:menu_items(name, category),
        warehouse:warehouses(name)
      `)
      .in('warehouse_id', warehouseIds)

    // Get menu availability
    const { data: menuAvailability } = await supabase
      .from('menu_item_availability_view')
      .select('*')
      .eq('branch_id', branchId)

    // Calculate summary statistics
    const rawInventoryValue = rawInventory?.reduce((sum, item) => {
      return sum + (item.current_stock * (item.ingredient?.cost_per_unit || 0))
    }, 0) || 0

    const finishedInventoryValue = finishedInventory?.reduce((sum, item) => {
      return sum + (item.current_stock * (item.cost_per_unit || 0))
    }, 0) || 0

    const lowStockRaw = rawInventory?.filter(item =>
      item.current_stock <= item.minimum_stock
    ).length || 0

    const lowStockFinished = finishedInventory?.filter(item =>
      item.current_stock <= item.minimum_stock
    ).length || 0

    const unavailableMenuItems = menuAvailability?.filter(item =>
      !item.final_availability
    ).length || 0

    return {
      summary: {
        total_raw_inventory_value: rawInventoryValue,
        total_finished_inventory_value: finishedInventoryValue,
        total_inventory_value: rawInventoryValue + finishedInventoryValue,
        low_stock_raw_items: lowStockRaw,
        low_stock_finished_items: lowStockFinished,
        unavailable_menu_items: unavailableMenuItems,
        total_menu_items: menuAvailability?.length || 0
      },
      raw_inventory: rawInventory,
      finished_inventory: finishedInventory,
      menu_availability: menuAvailability,
      warehouses: warehouses
    }
  }
}
